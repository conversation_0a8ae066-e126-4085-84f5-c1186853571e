"""HKTest框架适配器模块"""

import importlib.util
import logging
import sys
from pathlib import Path
from typing import Any, Dict, Optional

from engine.adapters.base import TestFrameworkAdapter
from hkautos.hktest import HKTest
from tests.test_manager import TestManager


class HKTestAdapter(TestFrameworkAdapter):
    """HKTest框架适配器,负责初始化和执行测试"""

    def __init__(self):
        self.hktest: Optional[HKTest] = None
        self._test_cases_dir: Optional[Path] = None
        self._test_manager: Optional[TestManager] = None
        self.report_manager = None
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """配置日志记录器"""
        logger = logging.getLogger("hktest.adapter")
        if not logger.handlers:
            logger.setLevel(logging.DEBUG)

            # 1. 控制台处理器 - 保持原有的控制台输出
            console = logging.StreamHandler()
            console.setLevel(logging.DEBUG)
            formatter = logging.Formatter(
                "[%(asctime)s][%(process)d][%(levelname)s] > %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S"
            )
            console.setFormatter(formatter)
            logger.addHandler(console)

            # 2. 报告处理器 - 用于写入报告
            class ReportHandler(logging.Handler):
                def __init__(self, adapter):
                    super().__init__()
                    self.adapter = adapter

                def emit(self, record):
                    if self.adapter.report_manager:
                        # 使用原始日志格式
                        original_msg = record.getMessage()
                        if hasattr(record, 'url'):
                            original_msg = f"{original_msg} > {record.url}"

                        self.adapter.report_manager.add_log({
                            "timestamp": record.asctime,
                            "thread_id": record.thread,
                            "source": record.pathname,
                            "level": record.levelname,
                            "message": record.message
                        })

            handler = ReportHandler(self)
            handler.setLevel(logging.DEBUG)
            handler.setFormatter(formatter)  # 使用相同的格式化器
            logger.addHandler(handler)

            logger.propagate = False
        return logger

    def initialize(self, config_path: str, test_cases_dir: str = "tests/test_cases") -> None:
        """初始化HKTest实例"""
        # 配置根日志记录器以捕获所有日志
        root_logger = logging.getLogger()
        # 添加报告处理器到根日志记录器
        report_handler = self.logger.handlers[1]  # 获取报告处理器
        if report_handler not in root_logger.handlers:
            root_logger.addHandler(report_handler)
        root_logger.setLevel(logging.DEBUG)

        self._validate_paths(config_path, test_cases_dir)
        self._init_hktest(config_path)
        self.logger.info("HKTest框架初始化成功")

    def _validate_paths(self, config_path: str, test_cases_dir: str) -> None:
        """验证路径是否有效"""
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件未找到: {config_path}")

        self._test_cases_dir = Path(test_cases_dir)
        if not self._test_cases_dir.exists():
            raise FileNotFoundError(f"测试用例目录未找到: {test_cases_dir}")

    def _init_hktest(self, config_path: str) -> None:
        """初始化HKTest实例和TestManager"""
        # 初始化HKTest
        self.hktest = HKTest(config=config_path)

        # 初始化TestManager
        self._test_manager = TestManager()
        self.hktest.test_manager = self._test_manager
        self.hktest.tests = True

        # 初始化测试模块
        TestManager.init_case_modules(self.hktest)

    def run_test(self, test_case: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """执行测试用例

        Args:
            test_case: 测试用例配置
            **kwargs: 额外参数

        Returns:
            Dict[str, Any]: 测试结果

        Raises:
            RuntimeError: HKTest未初始化时抛出
        """
        # 设置report_manager以便日志处理器使用
        self.report_manager = kwargs.get("report_manager")

        if not self.hktest:
            raise RuntimeError("HKTest未初始化")

        case_name = test_case["name"]
        self.logger.debug(f"开始执行测试用例: {case_name}")

        try:
            module = self._load_test_module(test_case)
            result = self._execute_test_case(module, test_case)
            self.report_manager = None  # 清除引用
            return result
        except Exception as e:
            self.logger.error(f"测试用例执行失败: {case_name}", exc_info=True)
            self.report_manager = None  # 清除引用
            return self._format_error_result(case_name, str(e))

    def _load_test_module(self, test_case: Dict[str, Any]) -> Any:
        """加载测试模块"""
        case_name = test_case["name"]

        # 在测试用例目录下查找用例文件
        test_file = None
        for file_path in self._test_cases_dir.rglob(f"{case_name}.py"):
            test_file = file_path
            break

        if not test_file:
            raise FileNotFoundError(f"未找到测试用例文件: {case_name}.py")

        # 构建模块路径
        module_path = str(test_file.relative_to(self._test_cases_dir)).replace("\\", ".").replace("/", ".")[:-3]

        module = sys.modules.get(module_path)
        if not module:
            spec = importlib.util.spec_from_file_location(module_path, test_file)
            if not spec or not spec.loader:
                raise ImportError(f"无法加载测试用例模块: {module_path}")

            module = importlib.util.module_from_spec(spec)
            sys.modules[module_path] = module
            spec.loader.exec_module(module)

        return module

    def _execute_test_case(self, module: Any, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """执行测试用例"""
        case_name = test_case["name"]
        self.logger.info(f"开始执行测试用例: {case_name}")
        case_class = getattr(module, case_name, None)
        if not case_class:
            raise ValueError(f"未找到测试用例类 {case_name}")

        case = case_class(self.hktest)
        case.parameters = test_case.get("parameters", {})
        self.logger.debug(f"测试用例参数: {case.parameters}")
        try:
            self.logger.info("执行测试步骤...")
            test_result = case.run()
            self.logger.info(f"测试用例执行完成，结果: {'通过' if test_result else '失败'}")

            return {
                "status": "passed" if test_result else "failed",
                "test_case": case_name,
                "parameters": case.parameters,
                "details": [
                    {"name": case_name, "status": "passed" if test_result else "failed", "doc": case_class.__doc__}
                ],
            }
        except Exception as e:
            self.logger.error(f"测试用例执行异常: {str(e)}", exc_info=True)
            raise

    def _format_error_result(self, case_name: str, error: str) -> Dict[str, Any]:
        """格式化错误结果"""
        return {
            "status": "failed",
            "test_case": case_name,
            "error": error,
            "details": [{"name": case_name, "status": "failed", "error": error}],
        }

    def parse_results(self, raw_results: Dict[str, Any]) -> Dict[str, Any]:
        """解析测试结果

        Args:
            raw_results: 原始测试结果

        Returns:
            Dict[str, Any]: 解析后的测试结果
        """
        self.logger.debug(f"解析测试结果: {raw_results}")
        return {"status": raw_results.get("status", "unknown"), "details": raw_results.get("details", [])}

    def cleanup_test(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """清理测试用例

        Args:
            test_case: 测试用例配置

        Returns:
            Dict[str, Any]: 清理结果
        """
        case_name = test_case["name"]
        self.logger.debug(f"清理测试用例: {case_name}")
        try:
            # 执行清理逻辑
            return {"status": "success"}
        except Exception as e:
            self.logger.error(f"测试用例清理失败: {case_name}", exc_info=True)
            return {"status": "error", "error": str(e)}

    def get_test_doc(self, test_case: Dict[str, Any]) -> Optional[str]:
        """获取测试用例的文档信息
        
        Args:
            test_case: 测试用例配置
            
        Returns:
            Optional[str]: 测试用例的文档信息，如果获取失败则返回None
        """
        try:
            case_name = test_case["name"]
            module = self._load_test_module(test_case)
            case_class = getattr(module, case_name, None)
            if case_class:
                return case_class.__doc__
            return None
        except Exception as e:
            self.logger.warning(f"获取测试用例文档失败: {e}")
            return None
