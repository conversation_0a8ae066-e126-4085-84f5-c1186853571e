"""
Description: 4.7 服务器命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

import time

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Server(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Server, self).__init__()
        self.name = "server"

    @validate_param(method=str)
    def cli_query_set_boot_device(self, method, option=None, value=None):
        """4.7.1 查询和设置启动设备（bootdevice）
            命令功能: bootdevice用来查询和设置启动设备。
            命令格式: ipmcget -d bootdevice
                    ipmcset -d bootdevice -v <option> [once | permanent]
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 设置的启动设备编号。
            value:
                once: 系统启动项的设置仅在下一次重启时生效，重启完成后，系统启动项自动恢复为BIOS中设置的默认方式。
                permanent: 系统启动项的设置永久有效。
        """
        params = {"method": method, "option": option, "value": value}
        result = self.dispatcher.dispatch("cli_query_set_boot_device", params=params)[
            0
        ]["parser"]
        return result

    @validate_param(option=str)
    def cli_set_server_reset_mode(self, option, value=None):
        """4.7.2 设置服务器重启方式（frucontrol）
            命令功能: frucontrol命令设置服务器的重启方式。
            命令格式: ipmcset [-t fru0] -d frucontrol -v <option>
        Args:
            option: 服务器重启方式
        """
        params = {"option": option, "value": value}
        result = self.dispatcher.dispatch("cli_set_server_reset_mode", params=params)[
            0
        ]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_server_power_state(self, method, option=None, value=None):
        """4.7.3 查询和设置服务器上下电状态（powerstate）
            命令功能: powerstate命令用于查询和控制服务器的上电和下电状态。
            命令格式: ipmcget [-t fru0] -d powerstate
                    ipmcset [-t fru0] -d powerstate -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 要对服务器进行的操作
        """
        params = {"method": method, "option": option, "value": value}
        result = self.dispatcher.dispatch(
            "cli_query_set_server_power_state", params=params
        )[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_server_power_off_timeout_period(
        self, method, time=None, value=None
    ):
        """4.7.4 查询和设置服务器的下电时限（shutdowntimeout）
            命令功能: shutdowntimeout命令用来查询和设置服务器的下电时限。
            下电时限：执行下电操作后，iBMC系统等待操作系统下电的时间。如果超过该时间操作系统仍未自动下电，iBMC会强制执行下电操作。
                        命令格式: ipmcget [-t fru0] -d shutdowntimeout
            ipmcset [-t fru0] -d shutdowntimeout -v <time>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            time: l表示要关闭服务器的下电时限功能。
        """
        params = {"method": method, "time": time, "value": value}
        result = self.dispatcher.dispatch(
            "cli_query_set_server_power_off_timeout_period", params=params
        )[0]["parser"]
        return result

    def check_power_state(self, state, number=10):
        """检查服务器电源状态是否符合预期

        Args:
            state: 期望的电源状态，取值范围："On"、"Off"
            number: 检查次数，默认10次

        Returns:
            bool: 检查结果
                True: 当前状态与期望状态一致
                False: 检查超时，状态不一致

        Example:
            >>> check_power_state("On")  # 检查是否上电
            True
            >>> check_power_state("Off") # 检查是否下电
            False
        """
        for _ in range(number):
            time.sleep(20)
            if self.cli_query_set_server_power_state(method="query") == state:
                return True

        return False

    def cli_query_mac_address_network_interface_main_board(self):
        """4.7.5 查询服务器板载网卡MAC地址（macaddr）
        命令功能: macaddr命令用来查询服务器主板上网口的MAC地址。
        命令格式: ipmcget -d macaddr
        """
        result = self.dispatcher.dispatch(
            "cli_query_mac_address_network_interface_main_board"
        )[0]["parser"]
        return result

    def cli_query_available_network_port(self):
        """4.7.6 查询系统可用网口（ethport）
        命令功能: ethport命令用来查询服务器上可用网口信息。
        命令格式: ipmcget -d ethport
        """
        result = self.dispatcher.dispatch("cli_query_available_network_port")[0][
            "parser"
        ]
        return result

    def cli_clear_bios_flash(self):
        """4.7.7 清除BIOS Flash（clearcmos）
        命令功能: clearcmos命令用于清除BIOS Flash上的用户自定义信息。
        命令格式: ipmcset -d clearcmos
        """
        result = self.dispatcher.dispatch("cli_clear_bios_flash")[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_query_raid_controller_card(self, option):
        """4.7.8 查询RAID控制器信息（ctrlinfo）
            命令功能: ctrlinfo用来查询RAID控制器信息。
            命令格式: ipmcget -t storage -d ctrlinfo -v <option>
        Args:
            option: 待查询的RAID控制器的ID。
        """
        params = {"option": option}
        result = self.dispatcher.dispatch(
            "cli_query_raid_controller_card", params=params
        )[0]["parser"]
        return result

    @validate_param(option=str, ctrlid=str)
    def cli_query_logical_disk(self, option, ctrlid):
        """4.7.9 查询逻辑盘信息（ldinfo）
            命令功能: ldinfo用来查询RAID控制器所管理的逻辑盘的信息。
            命令格式: ipmcget -t storage -d ldinfo -v <ctrlid> <option>
        Args:
            ctrlid: 待查询逻辑盘所属RAID控制器的ID。
            option: 待查询的逻辑盘的ID。
        """
        params = {"option": option, "ctrlid": ctrlid}
        result = self.dispatcher.dispatch("cli_query_logical_disk", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(option=str)
    def cli_query_physical_disk(self, option):
        """4.7.10 查询物理盘信息（pdinfo）
            命令功能: pdinfo用来查询物理盘的信息。
            命令格式: ipmcget -t storage -d pdinfo -v <option>
        Args:
            option: 待查询的物理盘的ID。
        """
        params = {"option": option}
        result = self.dispatcher.dispatch("cli_query_physical_disk", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(control_id=str, option=str)
    def cli_query_disk_array(self, control_id, option):
        """4.7.11 查询磁盘组信息（arrayinfo）
            命令功能: arrayinfo用来查询磁盘组的信息。
            命令格式: ipmcget -t storage -d arrayinfo -v <control_id> <option>
        Args:
            control_id: 磁盘组所在控制器的ID
            option: 待查询的磁盘组的ID。
        """
        params = {"control_id": control_id, "option": option}
        result = self.dispatcher.dispatch("cli_query_disk_array", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(control_id=str, pd_id=str, raidlevel=str, capative=str)
    def cli_creat_logical_drive(
        self,
        control_id,
        pd_id,
        raidlevel,
        cachecade=None,
        sc=None,
        name=None,
        size=None,
        ss=None,
        rp=None,
        wp=None,
        iop=None,
        ap=None,
        dcp=None,
        init=None,
    ):
        """4.7.12 创建逻辑盘（createld）
            命令功能: createld用于使用空闲物理盘创建虚拟盘。
            命令格式: ipmcset -t storage -d createld -v <control_id> -rl <raidlevel> -pd <pd_id>
                    [-cachecade] [-sc <span_num>] [-name <ldname>] [-size <capative>{m|g|t} ] [-ss <stripesize>]
                    [-rp <rpvalue>] [-wp <wpvalue>] [-iop <iopvalue>] [-ap <apvalue>] [-dcp <dcpvalue>]
                    [-init <initmode>]
        Args:
            cachecade: 命令行中包含“-cachecade”时，表示创建的逻辑盘为CacheCade逻辑盘
            control_id: RAID控制器的ID
            raidlevel: 逻辑盘的RAID级别
            pd_id: 逻辑盘的成员盘列表
            sc: 逻辑盘的子组个数
            name: 逻辑盘名称
            size: 逻辑盘容量
            ss: 逻辑盘条带大小
            rp: 逻辑盘的读策略
            wp: 逻辑盘的写策略
            iop: 逻辑盘的IO策略
            ap: 逻辑盘的访问策略
            dcp: 逻辑盘的磁盘缓存策略
            init: 逻辑盘的初始化方式
        """
        params = {
            "control_id": "control_id",
            "pd_id": "pd_id",
            "raidlevel": "raidlevel",
            "cachecade": "cachecade",
            "sc": "sc",
            "name": "name",
            "size": "size",
            "ss": "ss",
            "rp": "rp",
            "wp": "wp",
            "iop": "iop",
            "ap": "ap",
            "dcp": "dcp",
            "init": "init",
        }
        result = self.dispatcher.dispatch("cli_creat_logical_drive", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(control_id=str, arrayid=str)
    def cli_add_logical_drive(
        self,
        control_id,
        arrayid,
        name=None,
        size=None,
        ss=None,
        rp=None,
        wp=None,
        iop=None,
        ap=None,
        dcp=None,
        init=None,
        block=None,
    ):
        """4.7.13 添加逻辑盘（addld）
            命令功能: addld用于在已有逻辑盘的磁盘组上添加新的逻辑盘。
            命令格式: ipmcset -t storage -d addld -v <control_id> -array <arrayid> [-name <ldname>] [-size
                    <capative>{m|g|t} ] [-ss <stripesize>] [-rp <rpvalue>] [-wp <wpvalue>] [-iop <iopvalue>]
                    [-ap <apvalue>] [-dcp <dcpvalue>] [-init <initmode>][-block <blockid>]
        Args:
            arrayid: 待添加逻辑盘的磁盘组的ID
            control_id: RAID控制器的ID
            name: 逻辑盘名称
            size: 逻辑盘容量
            ss: 逻辑盘条带大小
            rp: 逻辑盘的读策略
            wp: 逻辑盘的写策略
            iop: 逻辑盘的IO策略
            ap: 逻辑盘的访问策略
            dcp: 逻辑盘的磁盘缓存策略
            init: 逻辑盘的初始化方式
        """
        params = {
            "control_id": "control_id",
            "arrayid": "arrayid",
            "name": "name",
            "size": "size",
            "ss": "ss",
            "rp": "rp",
            "wp": "wp",
            "iop": "iop",
            "ap": "ap",
            "dcp": "dcp",
            "init": "init",
            "block": "block",
        }
        result = self.dispatcher.dispatch("cli_add_logical_drive", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(ldid=str, control_id=str)
    def cli_delete_logical_drive(self, ldid, control_id):
        """4.7.14 删除逻辑盘（deleteld）
            命令功能: deleteld用于删除RAID卡管理的逻辑盘。
            命令格式: ipmcset -t storage -d deleteld -v <control_id> <ldid>
        Args:
            control_id: RAID控制器的ID
            ldid: 待删除的逻辑盘的ID
        """
        params = {"ldid": ldid, "control_id": control_id}
        result = self.dispatcher.dispatch("cli_delete_logical_drive", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(ldid=str, control_id=str, name=str)
    def cli_modify_logical_drive_properties(
        self,
        ldid,
        control_id,
        name=None,
        rp=None,
        wp=None,
        iop=None,
        ap=None,
        dcp=None,
        bgi=None,
        boot=None,
        sscd=None,
    ):
        """4.7.15 修改逻辑盘属性（ldconfig）
            命令功能: ldconfig用于修改逻辑盘的属性。
            命令格式: ipmcset -t storage -d ldconfig -v <control_id> <ldid> [-name <ldname>] [-rp <rpvalue>]
            [-wp <wpvalue>] [-iop <iopvalue>] [-ap <apvalue>] [-dcp <dcpvalue>] [-bgi <bgistate>]
            [-boot] [-sscd <sscdstate>]
        Args:
            ldid: 逻辑盘的ID
            control_id: RAID控制器的ID
            name: 逻辑盘名称
            bgi: 逻辑盘的BGI使能状态
            boot: 命令行中包含“-boot”时，表示设置此逻辑盘为启动盘。
            rp: 逻辑盘的读策略
            wp: 逻辑盘的写策略
            iop: 逻辑盘的IO策略
            ap: 逻辑盘的访问策略
            dcp: 逻辑盘的磁盘缓存策略
            sscd: 逻辑盘是否开启SSD Caching功能（即是否使用CacheCade逻辑盘作为缓存）
        """
        params = {
            "ldid": "ldid",
            "control_id": "control_id",
            "name": "name",
            "rp": "rp",
            "wp": "wp",
            "iop": "iop",
            "ap": "ap",
            "dcp": "dcp",
            "bgi": "bgi",
            "boot": "boot",
            "sscd": "sscd",
        }
        result = self.dispatcher.dispatch(
            "cli_modify_logical_drive_properties", params=params
        )[0]["parser"]
        return result

    @validate_param(control_id=str, cb=str)
    def cli_modify_raid_controller_properties(
        self, control_id, cb=None, smartercb=None, jbod=None, restore=None
    ):
        """4.7.16 修改RAID控制器属性（ctrlconfig）
            命令功能: ctrlconfig用于修改RAID控制器的属性。
            命令格式: ipmcset -t storage -d ctrlconfig -v <control_id> <[-cb <cbstate>] [-smartercb <smartercbstate>]
                [-jbod <jbodstate>] [-restore]
        Args:
            control_id: RAID控制器的ID
            cb: RAID控制器的Copyback功能使能状态
            smartercb: RAID控制器在成员盘出现SMART错误时Copyback功能使能状态
            jbod: RAID控制器JBOD模式使能状态
            restore: 命令行中包含“-restore”时，表示将RAID控制器的属性恢复为默认值
        """
        params = {
            "control_id": "control_id",
            "cb": "cb",
            "smartercb": "smartercb",
            "jbod": "jbod",
            "restore": "restore",
        }
        result = self.dispatcher.dispatch(
            "cli_modify_raid_controller_properties", params=params
        )[0]["parser"]
        return result

    @validate_param(pdid=str, hotsparetype=str)
    def cli_modify_physical_drive_properties(
        self, pdid, state=None, hotspare=None, ld=None, locate=None, cryptoerase=None
    ):
        """4.7.17 修改物理盘属性（pdconfig）
            命令功能: pdconfig用于修改RAID控制器所管理的物理盘的属性。
            命令格式: ipmcset -t storage -d pdconfig -v <pdid> [-state <pdstate>]
                    [-hotspare <hotsparetype> [-ld <ldid>]][-locate <locatestate>] [-cryptoerase]
        Args:
            pdid: 物理硬盘的ID
            state: 物理盘的运行状态
            hotspare: 物理盘的热备状态
            ld: 逻辑盘ID。
            locate: 物理盘定位指示灯状态
            cryptoerase: 命令行中包含-cryptoerase时，表示将加密盘的数据擦除。
        """
        params = {
            "pdid": "pdid",
            "state": "state",
            "hotspare": "hotspare",
            "locate": "locate",
            "cryptoerase": "cryptoerase",
            "ld": "ld",
        }
        result = self.dispatcher.dispatch(
            "cli_modify_physical_drive_properties", params=params
        )[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_log_function_screw(self, method, value=None):
        """4.7.18 查询和设置RAID扣卡日志记录功能(raidcom)
            命令功能: raidcom命令用于查询和设置要启用日志记录功能的RAID扣卡信息。指定了RAID扣卡后，
                    可通过一键信息收集功能获取该RAID扣卡的日志，日志文件存放路径为“dump_info/LogDump/storage”
            命令格式: ipmcget -t maintenance -d raidcom
                    ipmcset -t maintenance -d raidcom -v <value>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            <value>: 待记录日志的RAID扣卡编号。
        """
        params = {"method": method, "value": value}
        result = self.dispatcher.dispatch(
            "cli_query_set_log_function_screw", params=params
        )[0]["parser"]
        return result
