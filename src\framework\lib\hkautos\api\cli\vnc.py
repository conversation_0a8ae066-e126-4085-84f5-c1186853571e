"""
Description: 4.6 VNC命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Vnc(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Vnc, self).__init__()
        self.name = "vnc"

    def cli_1_query_vnc_service(self):
        """4.6.1 查询VNC服务信息（vnc -d info）
        命令功能: vnc -d info命令用于查询VNC服务的信息。
        命令格式: ipmcget -t vnc -d info
        """
        result = self.dispatcher.dispatch("cli_1_query_vnc_service")[0]["parser"]
        return result

    @validate_param(oldpassword=str, newpassword=str)
    def cli_set_vnc_service_password(self, oldpassword, newpassword):
        """4.6.2 设置VNC服务的密码（vnc -d password
            命令功能: vnc -d password命令用于设置VNC服务的密码。
            命令格式: ipmcset -t vnc -d password
        Args:
            oldpassword: 旧密码
            newpassword: 新密码
        """
        params = {"oldpassword": oldpassword, "newpassword": newpassword}
        result = self.dispatcher.dispatch("cli_set_vnc_service_password", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_vnc_service_timeout_period(self, value):
        """4.6.3 设置VNC服务的超时时长（vnc -d timeout）
            命令功能: vnc -d timeout命令用于设置VNC服务的超时时长。
            命令格式: ipmcset -t vnc -d timeout -v <value>
        Args:
            value: 表示VNC服务的超时时长
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_vnc_service_timeout_period", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_ssl_encryption_status_vnc_service(self, value):
        """4.6.4 设置VNC服务SSL加密功能的状态（vnc -d ssl）
            命令功能: vnc -d ssl命令用于设置VNC服务SSL加密功能的状态。
            命令格式: ipmcset -t vnc -d ssl -v<enabled|disabled>
        Args:
            value:
                enabled: 表示启用SSL加密功能
                disabled: 表示禁止SSL加密功能
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_ssl_encryption_status_vnc_service", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_keyboard_layout_vnc_service(self, value):
        """4.6.5 设置VNC服务的键盘布局（vnc -d keyboardlayout）
            命令功能: vnc -d keyboardlayout命令用于设置VNC服务的键盘布局。
            命令格式: ipmcset -t vnc -d keyboardlayout -v <en|jp|de>
        Args:
            value:
                en: 表示美式键盘
                jp: 表示日式键盘
                de: 表示德式键盘
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_keyboard_layout_vnc_service", params=params)[0]["parser"]
        return result
