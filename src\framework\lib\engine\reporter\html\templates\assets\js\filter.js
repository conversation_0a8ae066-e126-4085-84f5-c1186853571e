class LogFilter {
    constructor() {
        // 确保元素存在后再初始化
        if (!document.getElementById('filterOptions')) {
            console.warn('Filter options container not found, retrying in 500ms');
            setTimeout(() => new LogFilter(), 500);
            return;
        }
        
        this.initFilterOptions();
        this.initThreadFilter();
        this.initKeywordSearch();
        this.bindEvents();
        console.log('Filter initialized successfully');
    }

    initFilterOptions() {
        // 初始化日志级别过滤选项
        const levels = ['ALL', 'ERROR', 'WARN', 'INFO', 'DEBUG', 'STEP'];
        
        const filterContainer = document.getElementById('filterOptions');
        if (!filterContainer) {
            console.error('Filter container not found');
            return;
        }
        
        // 清空现有内容，避免重复
        filterContainer.innerHTML = '';
        
        levels.forEach(level => {
            const div = document.createElement('div');
            div.className = 'filter-option';
            div.innerHTML = `
                <input type="checkbox" id="${level}" class="level-filter" 
                       ${level === 'ALL' ? 'checked' : ''}>
                <label for="${level}">${level}</label>
            `;
            filterContainer.appendChild(div);
        });
    }

    initThreadFilter() {
        const submitBtn = document.getElementById('submitThreadFilter');
        if (!submitBtn) {
            console.error('Thread filter button not found');
            return;
        }
        submitBtn.addEventListener('click', () => this.applyFilters());
    }

    initKeywordSearch() {
        const keywordInput = document.getElementById('keywordSearch');
        if (!keywordInput) {
            console.error('Keyword search input not found');
            return;
        }
        keywordInput.addEventListener('input', () => this.applyFilters());
    }

    bindEvents() {
        // 绑定筛选事件
        document.querySelectorAll('.level-filter').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                if (e.target.id === 'ALL') {
                    document.querySelectorAll('.level-filter').forEach(cb => {
                        if (cb.id !== 'ALL') cb.checked = e.target.checked;
                    });
                } else {
                    const allCheckbox = document.getElementById('ALL');
                    allCheckbox.checked = false;
                }
                this.applyFilters();
            });
        });
    }

    applyFilters() {
        const selectedLevels = Array.from(document.querySelectorAll('.level-filter:checked'))
            .map(cb => cb.id.toLowerCase())
            .filter(id => id !== 'all');
        
        const threadId = document.getElementById('threadId').value.trim();
        const keyword = document.getElementById('keywordSearch').value.trim().toLowerCase();
        
        document.querySelectorAll('.log-entry').forEach(entry => {
            const level = entry.getAttribute('data-level').toLowerCase();
            const entryThreadId = entry.getAttribute('data-thread-id');
            const message = entry.querySelector('.col-message').textContent.toLowerCase();
            
            const levelMatch = selectedLevels.length === 0 || selectedLevels.includes(level);
            const threadMatch = !threadId || (entryThreadId && entryThreadId.includes(threadId));
            const keywordMatch = !keyword || message.includes(keyword);
            
            entry.style.display = levelMatch && threadMatch && keywordMatch ? '' : 'none';
        });
    }
}

// 初始化过滤器
function initializeFilter() {
    console.log('DOM loaded, initializing filter...');
    window.logFilter = new LogFilter();
}

// 在DOMContentLoaded时初始化
document.addEventListener('DOMContentLoaded', initializeFilter);

// 同时设置一个定时器，确保在页面动态更新后筛选器仍然存在
document.addEventListener('DOMContentLoaded', () => {
    // 每5秒检查一次筛选器是否存在
    setInterval(() => {
        if (!document.querySelector('.level-filter') && document.getElementById('filterOptions')) {
            console.log('Filter not found, reinitializing...');
            initializeFilter();
        }
    }, 5000);
}); 