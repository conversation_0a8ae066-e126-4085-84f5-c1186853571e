"""
Description: Boards_Mgt

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2025/07/08 15:00

"""

from typing import Any

from hkautos import log
from hkautos.config.enum import HostType

from tests.test_logic.component import Component


class Boards_Mgt(Component):
    def __init__(self, owning_device: Any) -> None:
        """初始化板类组件

        Args:
            device: 设备实例
        """
        super().__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.os_host = self.owning_device.get_host(host_type=HostType.HostOS)
        self.bmc_host = self.owning_device.get_host(host_type=HostType.BMC)
        self.web = self.owning_device.get_api(ns_name="Web")
        self.redfish = self.owning_device.get_api(ns_name="Redfish")

    def web_get_boards_info(self, board_type):
        """
        通过Web获取单板信息，能覆盖”系统管理“->”系统信息“->”其它“信息里的大部分内容
        Args:
            type：   单板类型，取值范围见web_get_system_others_info中的index.keys()
        Returns:
            各接口的返回值
        """
        return self.web.web_get_system_others_info(board_type)
