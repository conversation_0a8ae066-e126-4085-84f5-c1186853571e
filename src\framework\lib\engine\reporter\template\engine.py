"""模板引擎模块"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from pathlib import Path

from jinja2 import Environment, FileSystemLoader, Template


class TemplateEngine(ABC):
    """模板引擎基类"""
    
    def __init__(self, template_dir: Optional[str] = None):
        self.template_dir = template_dir
        
    @abstractmethod
    def load_template(self, template_name: str) -> Any:
        """加载模板"""
        pass
        
    @abstractmethod
    def render(self, template: Any, data: Dict[str, Any]) -> str:
        """渲染模板"""
        pass


class Jinja2Engine(TemplateEngine):
    """Jinja2模板引擎"""
    
    def __init__(self, template_dir: Optional[str] = None):
        super().__init__(template_dir)
        if not template_dir:
            raise ValueError("Template directory must be specified")
            
        if not Path(template_dir).exists():
            raise ValueError(f"Template directory does not exist: {template_dir}")
            
        self.env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=True
        )
        
    def load_template(self, template_name: str) -> Any:
        """加载模板"""
        if "\n" in template_name or "{{" in template_name:
            return self.env.from_string(template_name)
        return self.env.get_template(template_name)
        
    def render(self, template: Any, data: Dict[str, Any]) -> str:
        """渲染完整模板"""
        if isinstance(template, str):
            template = self.env.from_string(template)
        return template.render(**data)

    def render_partial(self, template_name: str, data: Dict[str, Any]) -> str:
        """渲染部分模板
        
        Args:
            template_name: 模板文件名
            data: 模板数据
            
        Returns:
            str: 渲染后的内容
        """
        template = self.load_template(template_name)
        return self.render(template, data)

    def render_string(self, template_string: str, data: Dict[str, Any]) -> str:
        """渲染模板字符串
        
        Args:
            template_string: 模板字符串
            data: 模板数据
            
        Returns:
            str: 渲染后的内容
        """
        template = self.env.from_string(template_string)
        return self.render(template, data) 