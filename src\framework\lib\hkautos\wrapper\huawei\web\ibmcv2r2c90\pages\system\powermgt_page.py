import os
from typing import Any, Dict, List

from hkautos import log

# from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.base_page import BasePage
from hkautos.exception.hktest_exception import WrapperException
from hkautos.wrapper.uniwebs.pagebase import PageBase


class PowerMgtPage(PageBase):
    LocatorFile = os.path.join("system", "power_mgt.json")

    def __init__(self, params):
        super(PowerMgtPage, self).__init__(params)
        self.logger = log.get_logger(__name__)
        self.web_init_elements_dict(self.LocatorFile)

    def web_set_power_control(self, params):
        """
        设置服务器上下电状态
        Args:
            params (dict):
                state (int):
                          0 下电 安全下电,与下电时限有关
                          1 上电
                          2 强制下电
                          3 重启 无上下电状态变化
                          4 先下电再上电 先强制下电再上电
                          5 NMI 中断
                  confirm(bool):
                         True 点击确定
                         False 点击取消
        Returns:
            成功返回：True 失败 raise
        """
        self.switch_page("power_cycle")
        state = params.get("state")
        confirm = params.get("confirm")
        button_list = ["off", "on", "forcedOff", "forcedRestart", "forcedCycle"]
        res = self.WebConnection.get_text(f"{self.locator_dict['power_status']}")
        if state == 1 and res != "下电":
            raise WrapperException("当前状态不支持点击上电操作！")
        elif state != 1:
            if res != "上电":
                raise WrapperException("当前状态不支持点击按钮操作！")
        self.WebConnection.click(f"#{self.locator_dict[button_list[state]]}")
        if confirm:
            self.WebConnection.click("#powerOnOffDialog_ok_btn")
            self.WebConnection.wait_for_selector(".ti3-alert-label")
            element_text = self.WebConnection.get_text(".ti3-alert-label")
            if "操作成功" in element_text:
                self.logger.info(f"power {self.locator_dict[button_list[state]]} 成功")
            else:
                raise WrapperException(f"power {button_list[state]} 失败，失败原因 {element_text}")
        else:
            self.WebConnection.click("#powerOnOffDialog_cancel_btn")

        self.WebConnection.wait_for_timeout(5000)

    def web_get_psu_info(self) -> List[Dict[str, Any]]:
        """
        获取WEB获取PSU信息
        Args:
            None
        Returns:
            result: List[Dict[str, Any]]，PSU信息
        """
        result = []
        self.switch_page("power_info")
        self.WebConnection.wait_for_selector(f'//*[@id="powerInfoTab"]')
        count = self.WebConnection.get_elements(f'//*[@id="powerInfoTab"]/app-power-info/div/div[2]/*').__len__()
        for index in range(count):
            items = self.WebConnection.get_inner_text(f'//*[@id="powerInfo{index}"]/*')
            result_dict = {}
            item_list = items.split("\n\t", 1)
            result_dict.update(dict(zip(["name", "watts", "balance_status"], item_list[0].split("\n"))))
            item1 = item_list[1].replace("\t\n\t", '\t').strip()
            result_dict.update(dict(zip(item1.split("\t")[::2], item1.split("\t")[1::2])))
            result.append(result_dict)
        return result
