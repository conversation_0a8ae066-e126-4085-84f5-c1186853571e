from typing import Any, Dict, List, Optional

from .registry import HookRegistry


class HookManager:
    """钩子管理器"""

    def __init__(self, registry: HookRegistry, hook_handlers: List[Any]):
        self.registry = registry
        # 注册所有钩子处理器
        for handler in hook_handlers:
            handler.register_to(self.registry)

    def register(self, hook_name: str, func: Any) -> None:
        """注册自定义钩子"""
        self.registry.register(hook_name, func)

    def unregister(self, hook_name: str, func: Any) -> None:
        """取消注册钩子"""
        self.registry.unregister(hook_name, func)

    def clear(self, hook_name: str = None) -> None:
        """清除钩子"""
        self.registry.clear(hook_name)

    def get_hooks(self, hook_name: str = None) -> Dict[str, Any]:
        """获取已注册的钩子"""
        return self.registry.get_hooks(hook_name)

    def handle_suite(self, is_start: bool, results: Optional[Dict] = None):
        """处理测试套件相关的钩子"""
        if is_start:
            self.registry.call("before_suite")
        else:
            self.registry.call("after_suite", results)

    def handle_test(self, is_start: bool, test_case: Dict[str, Any], result: Optional[Dict] = None):
        """处理测试用例相关的钩子"""
        if is_start:
            self.registry.call("before_test", test_case)
        else:
            self.registry.call("after_test", test_case, result)

    def handle_result(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """处理测试结果相关的钩子"""
        status = result["status"]
        if status == "passed":
            self.registry.call("before_pass", test_case, result)
            self.registry.call("after_pass", test_case, result)
        elif status == "failed":
            self.registry.call("before_fail", test_case, result)
            self.registry.call("after_fail", test_case, result)
        elif status == "skipped":
            self.registry.call("before_skip", test_case, result)
            self.registry.call("after_skip", test_case, result)

    def handle_cleanup(self, is_start: bool, test_case: Dict[str, Any], result: Optional[Dict] = None):
        """处理清理相关的钩子"""
        if is_start:
            self.registry.call("before_cleanup", test_case)
        else:
            self.registry.call("after_cleanup", test_case, result)
