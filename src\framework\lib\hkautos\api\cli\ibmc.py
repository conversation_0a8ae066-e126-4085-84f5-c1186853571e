"""
Description: 4.3 iBMC命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Ibmc(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Ibmc, self).__init__()
        self.name = "ibmc"

    def cli_query_ibmc_ip(self):
        """4.3.1 查询iBMC管理网口的IP信息（ipinfo）
        命令功能: ipinfo命令用来查询iBMC管理网口的IP信息。
        命令格式: ipmcget -d ipinfo
        """
        result = self.dispatch("cli_query_ibmc_ip", clear=True)[0]["parser"]
        return result

    @validate_param(ipaddr=str, mask=str)
    def cli_set_ibmc_ipv4_address(self, ipaddr, mask, gateway=None):
        """4.3.2 设置iBMC管理网口的IPv4信息（ipaddr）
            命令功能: ipaddr命令用于设置iBMC管理网口的IPv4地址、掩码、网关。
            命令格式: ipmcset -d ipaddr -v <ipaddr> <mask> [gateway]
        Args:
            ipaddr: 表示要设置的iBMC网口的IPv4地址。
            mask: 表示要设置的iBMC网口的子网掩码。
            gateway: 表示要设置的iBMC网口的网关地址。
        """
        params = {"ipaddr": ipaddr, "mask": mask, "gateway": gateway}
        result = self.dispatcher.dispatch("cli_set_ibmc_ipv4_address", params=params)[0]["parser"]
        return result

    @validate_param(ipaddr=str, mask=str)
    def cli_set_backup_ipv4_ibmc_management_network_port(self, ipaddr, mask):
        """4.3.3 设置iBMC管理网口的备份IPv4信息（backupipaddr）
            命令功能: backupipaddr命令用于设置iBMC管理网口的备份IPv4地址。在DHCP功能开启时：
            命令格式: ipmcset -d backupipaddr -v <ipaddr> <mask>
        Args:
            ipaddr: 表示要设置的iBMC网口的备份IPv4地址。
            mask: 表示要设置的备份IPv4地址的子网掩码。
        """
        params = {"ipaddr": ipaddr, "mask": mask}
        result = self.dispatcher.dispatch("cli_set_backup_ipv4_ibmc_management_network_port", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(value=str)
    def cli_set_ipv4_mode_ibmc(self, value):
        """4.3.4 设置iBMC管理网口的IPv4模式（ipmode）
            命令功能: ipmode命令用于设置iBMC网口的IPv4模式。
            命令格式: ipmcset -d ipmode -v <dhcp | static>
        Args:
            value:
                dhcp: 表示地址模式为dhcp
                static: 表示地址模式为static
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_ipv4_mode_ibmc", params=params)[0]["parser"]
        return result

    @validate_param(gateway=str)
    def cli_set_ipv4_gateway_address_ibmc(self, gateway):
        """4.3.5 设置iBMC管理网口的IPv4网关（gateway）
            命令功能: gateway命令用来设置iBMC网口的IPv4网关地址。
            命令格式: ipmcset -d gateway -v <gateway>
        Args:
            gateway: 表示iBMC网口的IPv4网关地址。
        """
        params = {"gateway": gateway}
        result = self.dispatcher.dispatch("cli_set_ipv4_gateway_address_ibmc", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_ibmc_ipv6_address(self, value, gateway6=None):
        """4.3.6 设置iBMC管理网口的IPv6信息（ipaddr6）
            命令功能: ipaddr6命令用于设置iBMC网口的IPv6地址、前缀长度和网关地址。
            命令格式: ipmcset -d ipaddr6 -v <ipaddr6/prefixlen> [gateway6]
        Args:
            value:
                ipaddr6: 表示要设置的iBMC网口的IPv6地址。
                prefixlen: 表示要设置的iBMC网口的子网前缀长度。
            gateway6: 表示要设置的iBMC网口的IPv6网关地址。
        """
        params = {"value": value, "gateway6": gateway6}
        result = self.dispatcher.dispatch("cli_set_ibmc_ipv6_address", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_ipv6_mode_ibmc(self, value):
        """4.3.7 设置iBMC管理网口的IPv6模式（ipmode6）
            命令功能: ipmode6命令用于设置iBMC网口的IPv6模式。
            命令格式: ipmcset -d ipmode6 -v <dhcp | static>
        Args:
            value:
                dhcp: 表示地址模式为dhcp
                static: 表示地址模式为static
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_ipv6_mode_ibmc", params=params)[0]["parser"]
        return result

    @validate_param(gateway6=str)
    def cli_set_ipv6_gateway_address_ibmc(self, gateway6):
        """4.3.8 设置iBMC管理网口的IPv6网关（gateway6）
            命令功能: gateway6命令用来设置iBMC网口的IPv6网关地址。
            命令格式: ipmcset -d gateway6 -v <gateway6>
        Args:
            gateway6: 表示iBMC网口的IPv6网关地址。
        """
        params = {"gateway6": gateway6}
        result = self.dispatcher.dispatch("cli_set_ipv6_gateway_address_ibmc", params=params)[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_set_network_port_mode(self, option):
        """4.3.9 设置管理网口模式（netmode）
            命令功能: netmode命令用于设置网口模式。
            命令格式: ipmcset -d netmode -v <option>
        Args:
            option: 网口模式
        """
        params = {"option": option}
        result = self.dispatcher.dispatch("cli_set_network_port_mode", params=params)[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_set_active_ibmc_port(self, option, portid=None):
        """4.3.10 设置激活端口（activeport）
            命令功能: activeport命令用于设置iBMC管理网口的激活端口。
            命令格式: ipmcset -d activeport -v <option> [portid]
        Args:
            option: 激活端口类型
            portid: 激活端口编号
        """
        params = {"option": option, "portid": portid}
        result = self.dispatcher.dispatch("cli_set_active_ibmc_port", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_vlan_id_network_port(self, value, vlan_type=None):
        """4.3.11 设置管理网口VLAN（vlan）
            命令功能: vlan命令用于设置网口的VLAN信息。
            命令格式: ipmcset -d vlan -v <off | id>
        Args:
            value:
                off: 禁止VLAN
                id: 网口所属VLAN
            vlan_type: (str)
                 '1' 设置专用网口vlan  专用网口vlan只支持cli和ipmi接口设置
                 '0' 设置NCSI网口vlan
        """
        params = {"value": value, "vlan_type": vlan_type}
        result = self.dispatcher.dispatch("cli_set_vlan_id_network_port", params=params)[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_query_redirect_serial_port(self, option):
        """4.3.12 查询和设置串口方向（serialdir）
            命令功能: serialdir命令用来查询和设置串口方向。
            命令格式: ipmcget -d serialdir
                    ipmcset -d serialdir -v <option>
        Args:
            <option>: 串口方向
        """
        params = {"option": option}
        result = self.dispatcher.dispatch("cli_query_redirect_serial_port", params=params)[0]["parser"]
        return result

    def cli_restart_ibmc(self):
        """4.3.13 重启iBMC管理系统（reset）
        命令功能: reset命令用来重启iBMC管理系统。
        命令格式: ipmcset -d reset
        """
        result = self.dispatcher.dispatch("cli_restart_ibmc")[0]["parser"]
        return result

    @validate_param(filepath=str)
    def cli_upgrade_firmware(self, filepath):
        """4.3.14 固件升级（upgrade）
            命令功能: upgrade命令用于升级固件。
            命令格式: ipmcset -d upgrade -v <filepath>
        Args:
            filepath: 表示将要升级的目标文件的绝对路径。
        """
        params = {"filepath": filepath}
        result = self.dispatcher.dispatch("cli_upgrade_firmware", params=params)[0]["parser"]
        return result

    def cli_capture_screen(self, wakeup=None):
        """4.3.15 截屏命令（printscreen）
            命令功能: printscreen命令用于截取服务器当前所显示的屏幕图片。
            命令格式: ipmcset -d printscreen [-v wakeup]
        Args:
            wakeup: 截取屏幕图片的同时唤醒系统屏保
        """
        params = {"wakeup": wakeup}
        result = self.dispatcher.dispatch("cli_capture_screen", params=params)[0]["parser"]
        return result

    def cli_roll_back_ibmc_software(self):
        """4.3.16 iBMC软件回滚（rollback）
        命令功能: rollback命令用来将iBMC固件主分区的镜像文件切换到备分区的镜像文件。
        命令格式: ipmcset -d rollback
        """
        result = self.dispatcher.dispatch("cli_roll_back_ibmc_software")[0]["parser"]
        return result

    def cli_query_result_roll_back_ibmc_software(self):
        """4.3.17 查询软件回滚状态（rollbackstatus）
        命令功能: rollbackstatus命令用来查询软件回滚状态。
        命令格式: ipmcget -d rollbackstatus
        """
        result = self.dispatcher.dispatch("cli_query_result_roll_back_ibmc_software")[0]["parser"]
        return result

    @validate_param(option=str, value=str, wait_input_yes=bool)
    def cli_set_service_state(self, option, value, wait_input_yes=False):
        """4.3.18 设置服务状态（service -d state）
            命令功能: service -d state命令用于设置iBMC的服务状态。
            命令格式: ipmcset -t service -d state -v <option> <enabled | disabled>
        Args:
            option: 服务类型
            value:
                enabled: 启用服务
                disabled: 禁用服务
        """
        params = {"option": option, "value": value, "wait_input_yes": wait_input_yes}
        result = self.dispatcher.dispatch("cli_set_service_state", params=params)[0]["parser"]
        return result

    @validate_param(option=str, port1value=str)
    def cli_set_service_port_number(self, option, port1value, port2value=None):
        """4.3.19 设置指定服务的端口号（service -d port）
            命令功能: service -d port命令用于设置iBMC指定服务的端口号。
            命令格式: ipmcset -t service -d port -v <option> <port1value> [port2value]
        Args:
            option: 服务类型
            port1value: 服务的端口号
            port2value: 服务的端口号，只有RMCP服务可以设置此端口
        """
        params = {"option": option, "port1value": port1value, "port2value": port2value}
        result = self.dispatcher.dispatch("cli_set_service_port_number", params=params)[0]["parser"]
        return result

    def cli_query_service(self):
        """4.3.20 查询服务状态（service -d list）
        命令功能: service -d list命令用于查询服务状态。
        命令格式: ipmcget -t service -d list
        """
        result = self.dispatcher.dispatch("cli_query_service")[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_enablement_status_login_security_message(self, value):
        """4.3.21 设置登录安全性信息功能的使能状态（securitybanner -d state）
            命令功能: securitybanner -d state命令用于设置是否在iBMC登录界面显示安全信息。
            命令格式: ipmcset -t securitybanner -d state -v <enabled | disabled>
        Args:
            value:
                enabled: 表示在登录界面显示安全信息。
                disabled: 表示不在登录界面显示安全信息。
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_enablement_status_login_security_message", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(value=str)
    def cli_customiz_login_security_message(self, value):
        """4.3.22 定制登录安全信息（securitybanner -d content）
            命令功能: securitybanner -d content命令用于设置在iBMC登录界面显示的安全信息的具体内容。
            命令格式: ipmcset -t securitybanner -d content -v < default | “option”>
        Args:
            value:
                default: 表示使用默认的安全信息，不做修改。
                option: 表示安全信息的具体内容
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_customiz_login_security_message", params=params)[0]["parser"]
        return result

    def cli_query_login_security_message(self):
        """4.3.23 查询登录安全信息（securitybanner -d info）
        命令功能: securitybanner -d info命令用于查询iBMC登录界面显示的安全信息的详细内容。
        命令格式: ipmcget -t securitybanner -d info
        """
        result = self.dispatcher.dispatch("cli_query_login_security_message")[0]["parser"]
        return result

    @validate_param(value=str, type=str)
    def cli_import_ssl_certificate(self, value, ssl_type, passphrase=None):
        """4.3.24 导入SSL证书（certificate -d import）
            命令功能: certificate -d import命令用于导入SSL证书到iBMC系统。
            命令格式: ipmcset -t certificate -d import -v <filepath | file_URL> <type> [passphrase]
        Args:
            value:
                filepath: 待导入的SSL证书的路径
                file_URL: 待导入的远程SSL证书文件的URL
            ssl_type: SSL证书类型
            passphrase: 生成SSL证书时的密码
        """
        params = {"value": value, "ssl_type": ssl_type, "passphrase": passphrase}
        result = self.dispatcher.dispatch("cli_import_ssl_certificate", params=params)[0]["parser"]
        return result

    def cli_query_ssl_certificate(self):
        """4.3.25 查询SSL证书信息（certificate -d info）
        命令功能: certificate -d info命令用于查询SSL证书的信息。
        命令格式: ipmcget -t certificate -d info
        """
        result = self.dispatcher.dispatch("cli_query_ssl_certificate")[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_export_configuration_file(self, value):
        """4.3.26 导出配置文件（config -d export）
            命令功能: config -d export命令用于导出iBMC、BIOS和RAID控制器当前配置文件。
            命令格式: ipmcget -t config -d export -v <filepath | file_URL>
        Args:
            value:
                filepath: 配置文件导出后的本地存放路径
                file_URL: 配置文件导出后的远程存放路径
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_export_configuration_file", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_import_configuration_file(self, value):
        """4.3.27 导入配置文件（config -d import）
            命令功能: config -d import命令用于导入iBMC、BIOS和RAID控制器配置文件。
            命令格式: ipmcset -t config -d import -v <filepath | file_URL>
        Args:
            value:
                filepath: 待导入的配置文件所在本地路径。
                file_URL: 待导入的配置文件所在远程路径。
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_import_configuration_file", params=params)[0]["parser"]
        return result

    @validate_param(value=str, type=str)
    def cli_import_crl_file(self, value, crl_type):
        """4.3.28 导入CRL文件（crl）
            命令功能: crl命令用于导入升级包完整性校验所使用的证书撤销列表文件。
            命令格式: ipmcset -d crl -v <localpath/URL> <type>
        Args:
            value:
                localpath: 待导入的CRL文件的路径
                URL: 待导入的远程CRL文件的URL
            crl_type: CRL文件类型
        """
        params = {"value": value, "crl_type": crl_type}
        result = self.dispatcher.dispatch("cli_import_crl_file", params=params)[0]["parser"]
        return result

    @validate_param(file_URL=str)
    def cli_mount_file_virtual_cd_rom_drive(self, file_URL):
        """4.3.29 挂载文件到虚拟光驱（vmm -d connect）
            命令功能: vmm -d connect命令用于挂载文件到虚拟光驱。
            命令格式: ipmcset -t vmm -d connect -v <file_URL>
        Args:
            file_URL: 待挂载的文件所在的远程路径。
        """
        params = {"file_URL": file_URL}
        result = self.dispatcher.dispatch("cli_mount_file_virtual_cd_rom_drive", params=params)[0]["parser"]
        return result

    def cli_disconnect_virtual_cd_rom_drive(self):
        """4.3.30 中断虚拟光驱的连接（vmm -d disconnect）
        命令功能: vmm -d disconnect命令用于断开虚拟光驱的连接。
        命令格式: ipmcset -t vmm -d disconnect
        """
        result = self.dispatcher.dispatch("cli_disconnect_virtual_cd_rom_drive")[0]["parser"]
        return result

    def cli_query_virtual_media(self):
        """4.3.31 查询虚拟媒体信息（vmm -d info）
        命令功能: vmm -d info命令用于查询iBMC虚拟媒体信息。
        命令格式: ipmcget -t vmm -d info
        """
        result = self.dispatcher.dispatch("cli_query_virtual_media")[0]["parser"]
        return result

    @validate_param(slotid=str)
    def cli_restore_default_set_fpga_card_golden_firmware(self, slotid, position=None):
        """4.3.32 将FPGA卡的Golden固件恢复出厂设置（fpgagoldenfwrestore）
            命令功能: fpgagoldenfwrestore命令用于FPGA卡无法正常工作时，将FPGA卡的Golden固件恢复出厂设置。
            命令格式: ipmcset -d fpgagoldenfwrestore -v <slotid> [position]
        Args:
            slotid: FPGA卡的槽位号
            position: FPGA卡所处的位置
        """
        params = {"slotid": slotid, "position": position}
        result = self.dispatcher.dispatch("cli_restore_default_set_fpga_card_golden_firmware", params=params)[0][
            "parser"
        ]
        return result

    def cli_uname_info(self):
        """uname查看bmc系统信息"""
        result = self.dispatcher.dispatch("uname_info")[0]["parser"]
        return result

    @validate_param(busybox_value=bool, loopback_ip=bool)
    def cli_enter_telnet_mode(self, busybox_value, loopback_ip=False):
        """进入telnet模式
        Args:
            busybox_value：1711机型telnet连接命令标志符
            loopback_ip：单板环回IP127.0.0.1，使用环回IP进行telnet连接的标志符
        """
        local_ip = self.cli_query_ibmc_ip()
        localip = local_ip.get("ethgroup_id_1").get("ip_address")
        params = {
            "localip": localip,
            "busybox_value": busybox_value,
            "loopback_ip": loopback_ip,
        }
        result = self.dispatcher.dispatch("enter_telnet_mode", params=params)[0]["parser"]
        return result
