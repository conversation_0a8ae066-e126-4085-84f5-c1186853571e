#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Web 固件升级 API

提供登录相关功能:
- 用户登录
- 登录状态检查
- 登出操作
"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import web_ns


@web_ns.dispatchertype(HostType.BMC)
class Fwupgrade(ApiBase):
    """电源管理 API"""

    def web_fw_upgrade(self, file_path, confirm_option, wait_timeout=300, wait_finish=True) -> None:
        """升级管理"""
        params = {
            "file_path": file_path,
            "confirm_option": confirm_option,
            "wait_timeout": wait_timeout,
            "wait_finish": wait_finish,
        }

        return self.dispatcher.dispatch("web_upgrade_firmware", params=params)

    def web_get_fw_version_list(self):
        """升级管理"""
        return self.dispatcher.dispatch("web_get_fw_version_list")
