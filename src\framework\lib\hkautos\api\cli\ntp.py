"""
Description: 4.10 NTP命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Ntp(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Ntp, self).__init__()
        self.name = "ntp"

    def cli_query_ntp(self):
        """4.10.1 查询NTP信息（ntpinfo）
        命令功能: ntpinfo命令用于查询iBMC的NTP信息。
        命令格式: ipmcget -d ntpinfo
        """
        result = self.dispatcher.dispatch("cli_query_ntp")[0]["parser"]
        return result

    @validate_param(status=str)
    def cli_set_ntp_state(self, status):
        """4.10.2 设置NTP状态（ntp -d status）
            命令功能: ntp -d status命令用于设置NTP功能的使能状态。
            命令格式: ipmcset -t ntp -d status -v status
        Args:
            status: 表示NTP功能的使能状态
        """
        params = {"status": status}
        result = self.dispatcher.dispatch("cli_set_ntp_state", params=params)[0]["parser"]
        return result

    @validate_param(mode=str)
    def cli_set_method_obtain_ntp(self, mode):
        """4.10.3 设置NTP信息获取方式（ntp -d mode）
            命令功能: ntp -d mode命令用于设置NTP信息获取方式。
            命令格式: ipmcset -t ntp -d mode -v mode
        Args:
            mode: 表示NTP信息获取方式
        """
        params = {"mode": mode}
        result = self.dispatcher.dispatch("cli_set_method_obtain_ntp", params=params)[0]["parser"]
        return result

    @validate_param(addr=str)
    def cli_set_address_preferred_ntp_server(self, addr):
        """4.10.4 设置首选NTP服务器地址（ntp -d preferredserver）
            命令功能: ntp -d preferredserver命令用于设置首选NTP服务器地址信息。
            命令格式: ipmcset -t ntp -d preferredserver -v addr
        Args:
            addr: 表示首选NTP服务器地址
        """
        params = {"addr": addr}
        result = self.dispatcher.dispatch("cli_set_address_preferred_ntp_server", params=params)[0]["parser"]
        return result

    @validate_param(addr=str)
    def cli_set_address_alternate_ntp_server(self, addr):
        """4.10.5 设置备用NTP服务器地址（ntp -d alternativeserver）
            命令功能: ntp -d alternativeserver命令用于设置备用NTP服务器地址信息。
            命令格式: ipmcset -t ntp -d alternativeserver -v addr
        Args:
            addr: 表示备用NTP服务器地址
        """
        params = {"addr": addr}
        result = self.dispatcher.dispatch("cli_set_address_alternate_ntp_server", params=params)[0]["parser"]
        return result

    @validate_param(addr=str)
    def cli_set_address_extra_ntp_server(self, addr):
        """4.10.6 设置拓展NTP服务器地址（ntp -d extraserver）
            命令功能: ntp -d extraserver命令用于设置拓展NTP服务器地址信息。
            命令格式: ipmcset -t ntp -d extraserver -v addr
        Args:
            addr: 表示拓展NTP服务器地址
        """
        params = {"addr": addr}
        result = self.dispatcher.dispatch("cli_set_address_extra_ntp_server", params=params)[0]["parser"]
        return result

    @validate_param(status=str)
    def cli_set_ntp_server_authentication(self, status):
        """4.10.7 设置服务器身份认证状态（ntp -d authstatus）
            命令功能: ntp -d authstatus命令用于设置服务器身份认证状态。
            命令格式: ipmcset -t ntp -d authstatus -v status
        Args:
            status: 表示服务器身份认证状态
        """
        params = {"status": status}
        result = self.dispatcher.dispatch("cli_set_ntp_server_authentication", params=params)[0]["parser"]
        return result

    @validate_param(filepath=str)
    def cli_upload_ntp_group_key(self, filepath):
        """4.10.8 上传NTP组密钥（ntp -d groupkey）
            命令功能: ntp -d groupkey命令可将用户自行获取的NTP组密钥上传到iBMC，此时，
                    iBMC与NTP服务器通信时将使用该密钥进行身份校验。
            命令格式: ipmcset -t ntp -d groupkey -v filepath
        Args:
            filepath: 密钥文件的名称
        """
        params = {"filepath": filepath}
        result = self.dispatcher.dispatch("cli_upload_ntp_group_key", params=params)[0]["parser"]
        return result
