
"""
功   能：Redfish查询内存信息测试

修改信息：
    日期：   2025/07/31
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt

class FRAME_MEM_INFO_FUNC_1100(Case):
    """
    CaseId:
        FRAME_MEM_INFO_FUNC_1100
    RunLevel:
        1(#1)
    CaseName:
        Redfish查询内存信息测试
    PreCondition:
        1、环境正常
        2、安装postman工具
    TestStep:
        1._打开postman工具，redfish接口：/redfish/v1/Systems/1/Memory，查询内存相关信息，有预期1
        2._redfish接口：/redfish/v1/Systems/1/Memory/CpuBoard1DIMMXXX，查询内存条详细信息
        3._遍历所有内存条，有预期3
    ExpectedResult:
        1._查询成功，状态码为200，内存条数量与web界面在位数量一致，且在位连接名称显示正确
        2._查询成功，状态码为200，且web界面内存条相关的关键信息在redfish接口上报的内存信息中均可查，且信息一致
        3._均查询成功，状态码为200
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.mem_mgt = self.device_dut.find(HardwareMgt.Mem)
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.logger.step("1. 打开postman工具，redfish接口：/redfish/v1/Systems/1/Memory，查询内存相关信息，有预期1")
        res = self.mem_mgt.check_mem_info(self.hardware_json, "redfish")
        self.assertTrue(res, "查询成功，状态码为200，且web界面内存条相关的关键信息在redfish接口上报的内存信息中均可查，且信息一致")

        self.logger.step("2. redfish接口：/redfish/v1/Systems/1/Memory/CpuBoard1DIMMXXX，查询内存条详细信息")
        # 已在步骤1中实现
        
        self.logger.step("3. 遍历所有内存条，有预期3")
        # 已在步骤1中实现
        

    def post_test_case(self):
        self.logger.info("post test case.... ")
        