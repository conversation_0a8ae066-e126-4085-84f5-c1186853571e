"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""
from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class ChassisDevice(ApiBase):
    def __init__(self):
        super(ChassisDevice, self).__init__()

    def get_chassis_status(self, params=None):
        """
        获取机箱状态信息
        Args:
            params: 参数字典
        Returns:
            dict: 返回解析后的机箱状态信息
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_chassis_status", params=params)[0]["parser"]

    def set_chassis_power(self, params=None):
        """
        设置机箱电源状态
        Args:
            params: 参数字典
                   例如: {'action': 'on'}
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_chassis_power", params=params)[0]["parser"]

    def get_chassis_policy(self, params=None):
        """
        获取机箱电源策略
        Args:
            params: 参数字典
        Returns:
            dict: 返回解析后的电源策略信息
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_chassis_policy", params=params)[0]["parser"]

    def set_chassis_policy(self, params=None):
        """
        设置机箱电源策略
        Args:
            params: 参数字典
                   例如: {'policy': 'always-on'}
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_chassis_policy", params=params)[0]["parser"]

    def chassis_identify(self, params=None):
        """
        设置机箱LED标识
        Args:
            params: 参数字典
                   例如: {'interval': '30'}
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_chassis_identify", params=params)[0]["parser"]

    def chassis_selftest(self, params=None):
        """
        执行机箱自检
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_chassis_selftest", params=params)[0]["parser"]

    def chassis_raw_selftest(self, params=None):
        """
        执行机箱自检, raw命令
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_raw_chassis_selftest", params=params)[0]["parser"]

    def chaassis_device_guid(self, params=None):
        """
        获取机箱GUID
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_chassis_device_guid", params=params)[0]["parser"]

    def get_system_guid(self, params=None):
        """
        获取系统GUID
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_system_guid", params=params)[0]["parser"]

    def get_chassis_status_raw(self, params=None):
        """
        raw，raw命令获取机箱状态信息
        Args:
            params: 参数字典
        Returns:
            dict: 返回解析后的机箱状态信息
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_chassis_status_raw", params=params)[0]["parser"]

    def set_chassis_status_raw(self, params=None):
        """
        raw，raw命令获取机箱状态信息
        Args:
            params: 参数字典
        Returns:
            dict: 返回解析后的机箱状态信息
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_chassis_status_raw", params=params)[0]["parser"]

    def set_chassis_identify_raw(self, params=None):
        """
        raw，raw命令设置机箱LED标识
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_chassis_identify_raw", params=params)[0]["parser"]

    def get_bootparam(self, params=None):
        """
        查看启动顺序
        Returns:
             None
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_bootparam", params=params)[0]["parser"]

    def ipmi_get_system_boot_options(self, params=None):
        """
        查询服务器启动项信息
        Args:
        {"boot_option":value}
        value = {
        "Progress": "'0x00'",
        "Selector": "'0x01'",
        "Scan": "'0x02'",
        "Clearing": "'0x03'",
        "Acknowledge": "'0x04'",
        "Bootdev": "'0x05'",
    }
        Returns:
             直接原始响应信息
        Raises:
            None
        Examples:
        ret = self.ipmi_api.ipmi_get_system_boot_options('boot_options'='Progress')
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_system_boot_options", params=params)[
            0
        ]["parser"]

    def set_chassis_power_cycle_raw(self, params=None):
        """
        查看启动顺序
        Returns:
             None
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_chassis_power_cycle_raw", params=params)[0]["parser"]

    def set_chassis_bootdev(self, params=None):
        """
        设置启动项
        Returns:
             None
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_chassis_bootdev", params=params)[0]["parser"]
