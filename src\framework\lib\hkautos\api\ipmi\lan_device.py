"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class LANDevice(ApiBase):
    def __init__(self):
        super(LANDevice, self).__init__()

    def get_lan_config(self, params=None):
        """
        获取LAN配置信息

        Args:
            params: 参数字典
                   例如: {'channel': 1}

        Returns:
            dict: 返回解析后的LAN配置信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_print", params=params)[0]["parser"]

    def set_lan_ipaddr(self, params=None):
        """
        设置IP地址

        Args:
            params: 参数字典
                   例如: {
                       'channel': 1,
                       'ipaddr': '***********00'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_ipaddr", params=params)[0]["parser"]

    def set_lan_netmask(self, params=None):
        """
        设置子网掩码

        Args:
            params: 参数字典
                   例如: {
                       'channel': 1,
                       'netmask': '*************'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_netmask", params=params)[0]["parser"]

    def set_lan_gateway(self, params=None):
        """
        设置网关

        Args:
            params: 参数字典
                   例如: {
                       'channel': 1,
                       'gateway': '***********'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_gateway", params=params)[0]["parser"]

    def set_lan_access(self, params=None):
        """
        设置访问模式

        Args:
            params: 参数字典
                   例如: {
                       'channel': 1,
                       'access': 'on'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_access", params=params)[0]["parser"]

    def set_lan_ipsrc(self, params=None):
        """
        设置IP获取方式

        Args:
            params: 参数字典
                   例如: {
                       'channel': 1,
                       'ipsrc': 'static'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_ipsrc", params=params)[0]["parser"]

    def lan_get_addr(self, params=None):
        """
        获取ip地址

        Args:

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_get_addr", params=params)[0]["parser"]

    def lan_set_state(self, params=None):
        """
        设置IP src
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_state", params=params)[0]["parser"]

    def lan_set_ipv4(self, params=None):
        """
        设置IP addr
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_ipv4", params=params)[0]["parser"]

    def lan_set_netmask(self, params=None):
        """
        设置net mask
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_netmask", params=params)[0]["parser"]

    def get_current_ipsrc(self, params=None):
        """
        获取ip地址来源
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_current_ipsrc", params=params)[0]["parser"]

    def set_lan_macaddr(self, params=None):
        """
        设置mac地址
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_macaddr", params=params)[0]["parser"]

    def lan_set_macaddr(self, params=None):
        """
        设置mac地址
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_macaddr", params=params)[0]["parser"]

    def lan_get_netmask(self, params=None):
        """
        获取子网掩码
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_get_netmask", params=params)[0]["parser"]

    def lan_set_gateway(self, params=None):
        """
        获取网关
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_gateway", params=params)[0]["parser"]

    def lan_get_gateway(self, params=None):
        """
        获取网关
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_get_gateway", params=params)[0]["parser"]

    def lan_set_addr_state(self, params=None):
        """
        获取网络协议
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_addr_state", params=params)[0]["parser"]

    def set_lan_addr_state(self, params=None):
        """
        获取网络协议
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_addr_state", params=params)[0]["parser"]

    def get_lan6_print(self, params=None):
        """
        获取lan6 信息
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_lan6_print", params=params)[0]["parser"]

    def lan_set_config(self, params=None):
        """
        设置网络信息
        Args:
        Returns:
            str: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_config", params=params)[0]["parser"]

    def lan_get_config(self, params=None):
        """
        获取网络信息
        Args:
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_lan_config", params=params)[0]["parser"]

    def lan_set_arp(self, params=None):
        """
        设置arp
        Args:
        Returns:
            str: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_lan_set_arp", params=params)[0]["parser"]

    def set_lan_ipv6addr(self, params=None):
        """
        设置ipv6
        Args:
        Returns:
            str: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_ipv6addr", params=params)[0]["parser"]

    def set_lan_ipv6(self, params=None):
        """
        设置ipv6
        Args:
        Returns:
            str: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_lan_ipv6", params=params)[0]["parser"]