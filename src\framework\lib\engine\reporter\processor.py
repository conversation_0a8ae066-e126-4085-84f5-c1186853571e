"""数据处理器模块"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List
from datetime import datetime
import os
import re

class DataProcessor(ABC):
    """数据处理器基类"""
    
    @abstractmethod
    def process_case(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用例数据"""
        pass
        
    @abstractmethod
    def process_log(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理日志数据"""
        pass

class DefaultProcessor(DataProcessor):
    """默认数据处理器"""
    
    def process_case(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用例数据，默认直接返回原数据"""
        return case_data
        
    def process_log(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理日志数据，默认直接返回原数据"""
        return log_data

    def _simplify_path(self, path: str) -> str:
        """简化路径，只保留项目相关部分"""
        if not path:
            return ""
        
        # 使用正则表达式匹配常见项目路径模式
        # 匹配模式: 任何字符 + (src|framework|lib|tests) + 路径分隔符 + 剩余路径
        pattern = r'.*?(src|framework|lib|tests)[/\\](.+)'
        match = re.search(pattern, path)
        
        if match:
            # 返回匹配的组合: 如 "src/path/to/file.py"
            return os.path.join(match.group(1), match.group(2)).replace('\\', '/')
        
        # 如果没有匹配到项目路径模式，则返回文件名
        return os.path.basename(path)