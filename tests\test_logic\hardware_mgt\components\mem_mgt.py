"""
Description: Memory Managerment

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 17:15

"""

from typing import Any

from hkautos import log
from hkautos.config.enum import HostType

from tests.test_logic.component import Component


class MemMgt(Component):
    def __init__(self, owning_device: Any) -> None:
        """初始化Mem组件

        Args:
            device: 设备实例
        """
        super().__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.os_host = self.owning_device.get_host(host_type=HostType.HostOS)
        self.bmc_host = self.owning_device.get_host(host_type=HostType.BMC)
        self.web = self.owning_device.get_api(ns_name="Web")
        self.redfish = self.owning_device.get_api(ns_name="Redfish")

