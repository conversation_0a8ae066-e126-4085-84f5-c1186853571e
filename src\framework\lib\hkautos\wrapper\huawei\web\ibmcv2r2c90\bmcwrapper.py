# """
# 功    能:
#
# 版权信息: ©2023-2028 四川华鲲振宇智能科技有限责任公司
#
# """
import re
import sys
from types import MethodType

from hkautos.command.connection.web.web_connection import WebConnection

# from types import MethodType
# from hkautos.utils.type_check import validate_dict
# from hkautos.exception.hktest_exception import WrapperException
# from hkautos.utils.units import Units
# from hkautos.utils.concurrent_utils import sleep
# # from hkautos.wrapper.gui.web.uniwebs.uniwebbase import UniWebBase
# from hkautos.wrapper.gui.web.uniwebs.common.browsertype import BrowserType
# from hkautos.wrapper.gui.web.ibmc.ibmcv2r2c90.common.ssohtml import SSOHtmlStr
from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.login_page import LoginPage


class BmcWebWrapper:
    modules = [
        # "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.login",
        "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.home",
        "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.maintenance",
        "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.services_manage",
        "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.setting",
        "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.system_manage",
        "hkautos.wrapper.huawei.web.ibmcv2r2c90.function.user_security",
    ]

    def __init__(self, params=None):
        super(BmcWebWrapper, self).__init__()
        self.connection = None
        self.host = params.get("host")
        self.bmc_ip = None
        self.username = None
        self.password = None
        self._elements = {}

        def setFuncattr():
            if _i.find("__") < 0:
                method = getattr(sys.modules[m], _i)
                if re.search("function", str(type(method))):
                    setattr(self, _i, MethodType(method, self))
                else:
                    setattr(self, _i, method)

        for m in self.modules:
            __import__(m)
            for _i in dir(sys.modules[m]):
                setFuncattr()

    def web_init_driver(self, url):
        connection = WebConnection(url)
        return connection

    def web_login(self, params):
        # todo 修改login登录的端口
        # bmc = params["bmc"]
        _username = params.get("username")
        _password = params.get("password")
        # login_method = params.get("login_method")
        # web_protocol = params.get("web_protocol")

        # web_port = params.get("web_port")
        _ip_address = params.get("ip_address")

        # webDriver = self.web_generate_web_driver(
        #     params.get("browser_type", random.choice([BrowserType.FIREFOX]))
        # )
        # self.UniWebDrivers.append(webDriver)
        # self.UniWebBase = UniWebBase(self.UniWebDrivers, bmc_branch)
        # self.bmc_ip = bmc.local_ip
        # ipv4_address = ip_address if ip_address else self.bmc_ip
        # self.username = username if username else bmc.username
        # self.password = password if password else bmc.password
        # login_protocol = web_protocol if web_protocol else "https"
        # login_port = web_port if web_port else 443
        # url = "%s://%s:%d/#/login" % (login_protocol, ipv4_address, login_port)
        # self.loginPage = LoginPage(params)
        # self.loginPage.web_login(params)
        #     bmc, username, password, login_method, update_pwd
        # )
        url = f"https://{params.get('ip_address')}"
        self.connection = self.web_init_driver(url)
        self.loginPage = LoginPage(self.connection)
        self.loginPage.web_login(params)
        return self.connection

    def web_logout(self, web_handle):
        if web_handle._page:
            if web_handle._page.is_closed():
                return
            if web_handle.get_elements(f'//*[@id="localUserIcon"]'):
                web_handle.hover(f'//*[@id="localUserIcon"]')
                web_handle.wait_for_selector(f'//*[@id="loginOut"]')
                web_handle.click(f'//*[@id="loginOut"]')
                web_handle.wait_for_selector(f'//*[@id="btLogin"]')
            web_handle.close()
