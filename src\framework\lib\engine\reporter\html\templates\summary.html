<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试报告摘要</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container">
        <!-- 两列布局容器 -->
        <div class="two-column-layout">
            <!-- 左侧列：环境信息 -->
            <div class="left-column">
                <div class="env-card">
                    <div class="card-header">
                        <h2>环境信息</h2>
                    </div>
                    <div class="card-body">
                        <table class="env-table">
                            <tr>
                                <td>报告生成时间</td>
                                <td>{{ timestamp }}</td>
                            </tr>
                            <tr>
                                <td>报告目录</td>
                                <td>{{ summary.report_dir|default('未指定') }}</td>
                            </tr>
                            <tr>
                                <td>操作系统</td>
                                <td>{{ summary.os_info|default('未知') }}</td>
                            </tr>
                            <tr>
                                <td>Python版本</td>
                                <td>{{ summary.python_version|default('未知') }}</td>
                            </tr>
                            <tr>
                                <td>主机名</td>
                                <td>{{ summary.hostname|default('未知') }}</td>
                            </tr>
                            <tr>
                                <td>IP地址</td>
                                <td>{{ summary.ip_address|default('未知') }}</td>
                            </tr>
                            <tr>
                                <td>开始时间</td>
                                <td>{{ summary.start_time }}</td>
                            </tr>
                            <tr>
                                <td>更新时间</td>
                                <td>{{ summary.update_time }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- 可以在左侧添加更多卡片 -->
                <div class="stats-card">
                    <div class="card-header">
                        <h2>执行统计</h2>
                    </div>
                    <div class="card-body">
                        <div class="stat-chart">
                            <div class="chart-container">
                                <canvas id="pieChart" width="150" height="150"></canvas>
                            </div>
                            <div class="chart-legend">
                                <div class="legend-item">
                                    <span class="legend-color passed"></span>
                                    <span class="legend-label">通过: {{ stats.passed }}</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color failed"></span>
                                    <span class="legend-label">失败: {{ stats.failed }}</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color running"></span>
                                    <span class="legend-label">运行中: {{ stats.running }}</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color stopped"></span>
                                    <span class="legend-label">已停止: {{ stats.stopped }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧列：测试概览和用例列表 -->
            <div class="right-column">
                <!-- 测试概览 -->
                <div class="summary-card">
                    <div class="summary-header">
                        <h2>测试概览</h2>
                    </div>
                    
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-value">{{ stats.total }}</div>
                            <div class="stat-label">总用例数</div>
                        </div>
                        <div class="stat-item passed">
                            <div class="stat-value">{{ stats.passed }}</div>
                            <div class="stat-label">通过</div>
                        </div>
                        <div class="stat-item failed">
                            <div class="stat-value">{{ stats.failed }}</div>
                            <div class="stat-label">失败</div>
                        </div>
                        <div class="stat-item running">
                            <div class="stat-value">{{ stats.running }}</div>
                            <div class="stat-label">运行中</div>
                        </div>
                        <div class="stat-item stopped">
                            <div class="stat-value">{{ stats.stopped }}</div>
                            <div class="stat-label">已停止</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ "%.2f"|format(stats.pass_rate) }}%</div>
                            <div class="stat-label">通过率</div>
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-segment passed" style="width: {{ (stats.passed / stats.total * 100) if stats.total > 0 else 0 }}%"></div>
                            <div class="progress-segment failed" style="width: {{ (stats.failed / stats.total * 100) if stats.total > 0 else 0 }}%"></div>
                            <div class="progress-segment running" style="width: {{ (stats.running / stats.total * 100) if stats.total > 0 else 0 }}%"></div>
                            <div class="progress-segment stopped" style="width: {{ (stats.stopped / stats.total * 100) if stats.total > 0 else 0 }}%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 用例列表 -->
                <div class="cases-card">
                    <div class="card-header">
                        <h2>测试用例列表</h2>
                        <div class="filter-controls">
                            <input type="text" id="caseFilter" placeholder="搜索用例...">
                            <select id="statusFilter">
                                <option value="all">所有状态</option>
                                <option value="passed">通过</option>
                                <option value="failed">失败</option>
                                <option value="running">运行中</option>
                                <option value="stopped">已停止</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="cases-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用例名称</th>
                                    <th>状态</th>
                                    <th>执行时长</th>
                                    <th>开始时间</th>
                                    <th>结束时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for case in cases %}
                                <tr class="case-row {{ case.status }}">
                                    <td>{{ case.id }}</td>
                                    <td>{{ case.name }}</td>
                                    <td class="status-cell">
                                        <span class="status-badge {{ case.status }}">{{ case.status }}</span>
                                    </td>
                                    <td>{{ case.duration }}秒</td>
                                    <td>{{ case.start_time_display }}</td>
                                    <td>{{ case.end_time_display }}</td>
                                    <td>
                                        <a href="{{ case.report_link }}" class="view-btn">查看详情</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取饼图数据
        const stats = {
            passed: {{ stats.passed }},
            failed: {{ stats.failed }},
            running: {{ stats.running }},
            stopped: {{ stats.stopped }},
            total: {{ stats.total }}
        };
        
        // 绘制饼图
        const canvas = document.getElementById('pieChart');
        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) * 0.9;
        
        // 定义颜色
        const colors = {
            passed: '#28a745',
            failed: '#dc3545',
            running: '#007bff',
            stopped: '#fd7e14'
        };
        
        // 计算各部分的角度
        let startAngle = 0;
        const data = [
            { label: 'passed', value: stats.passed },
            { label: 'failed', value: stats.failed },
            { label: 'running', value: stats.running },
            { label: 'stopped', value: stats.stopped }
        ];
        
        // 绘制饼图
        data.forEach(item => {
            if (stats.total > 0 && item.value > 0) {
                const sliceAngle = 2 * Math.PI * item.value / stats.total;
                
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
                ctx.closePath();
                
                ctx.fillStyle = colors[item.label];
                ctx.fill();
                
                startAngle += sliceAngle;
            }
        });
        
        // 用例过滤功能
        const caseFilter = document.getElementById('caseFilter');
        const statusFilter = document.getElementById('statusFilter');
        const caseRows = document.querySelectorAll('.case-row');
        
        function filterCases() {
            const searchText = caseFilter.value.toLowerCase();
            const statusValue = statusFilter.value;
            
            caseRows.forEach(row => {
                const caseName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const caseStatus = row.classList[1]; // 第二个类名是状态
                
                const nameMatch = caseName.includes(searchText);
                const statusMatch = statusValue === 'all' || caseStatus === statusValue;
                
                row.style.display = nameMatch && statusMatch ? '' : 'none';
            });
        }
        
        caseFilter.addEventListener('input', filterCases);
        statusFilter.addEventListener('change', filterCases);
    });
    </script>
</body>
</html> 