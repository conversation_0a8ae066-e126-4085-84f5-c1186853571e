"""
Description: FAN Managerment

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/2/28 17:15

"""

from hkautos import log
from hkautos.config.enum import HostType

from tests.test_logic.component import Component


class FanMgt(Component):
    def __init__(self, owning_device):
        super(FanMgt, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.bmc_host = self.owning_device.get_host(HostType.BMC)
        self.os_host = self.owning_device.get_host(HostType.HostOS)
        self.host_os = self.owning_device.get_host("HostOS", host_id="1")
        self.host_os_object = self.owning_device.get_host(host_type="HostOS", host_id="1")
        self.redfish_api = self.owning_device.get_api(ns_name="Redfish")
        self.ipmi_api = self.owning_device.get_api(ns_name="Ipmi")
        self.web_api = self.owning_device.get_api(ns_name="Web")

