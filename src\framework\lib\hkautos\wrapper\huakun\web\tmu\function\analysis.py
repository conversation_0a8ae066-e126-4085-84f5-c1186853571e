"""
功    能:

版权信息: ©2023-2028 四川华鲲振宇智能科技有限责任公司

"""

# from hkautos.utils.time import sleep
# from hkautos.wrapper.huawei.web.ibmcv2r2c90.common import pageenum
from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.system_page import SystemPage


def web_set_power_control(self, params):
    """
    设置服务器上下电状态
    Args:
        params (dict):
            state (int):
                      0 下电 安全下电,与下电时限有关
                      1 上电
                      2 强制下电
                      3 重启 无上下电状态变化
                      4 先下电再上电 先强制下电再上电
                      5 NMI 中断
              confirm(bool):
                     True 点击确定
                     False 点击取消
    Returns:
        成功返回：True 失败 raise
    """
    syspage = SystemPage(self.connection)
    syspage.web_power_mgt_page.web_set_power_control(params)
