"""
Description: Upgrade Managerment

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 16:33 created

"""

from time import sleep
from typing import Any, Dict, List
from hkautos import log
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException

from tests.test_logic.alias import BMCMgt
from tests.test_logic.bmc_mgt.components.upgrade_mgt import UpgradeMgt
from tests.test_logic.logic import Logic
from tests.test_logic.services_mgt.logics.nfs_server_mgt_logic import NfsServerLogic
from tests.test_data.base import DictListQuery

class UpgradeMgtLogic(Logic):
    alias = BMCMgt.Firmware

    def __init__(self, owning_device):
        super(UpgradeMgtLogic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.upgradeComponent = UpgradeMgt(owning_device)
        self.redfish_api = self.owning_device.get_api(ns_name="Redfish")
        self.web_api = self.owning_device.get_api(ns_name="Web")
        self.bmc_host = self.owning_device.get_host(HostType.BMC, host_id="1")
        self.dst_device = self.owning_device.dsts[0]
        self.nfs_host = self.dst_device.get_host(HostType.HostOS, alias="nfs")
        self.bmc_obj = self.owning_device.get_host(host_type=HostType.BMC)
        self.bmc_localhost = self.owning_device.get_host("BMC")
        self.nfs_server = NfsServerLogic(owning_device, "bmctoolpath")

    def get_firmware_file(self, file_name: str, property_name: str = "bmc_fw_path", target_path: str = "/tmp") -> str:
        """从NFS服务器获取固件文件

        Args:
            file_name: 固件文件名
            property_name: 属性名
            target_path: 目标存放路径，默认为/tmp

        Returns:
            str: 下载后的文件完整路径

        Raises:
            HKTestException: 文件获取失败时抛出异常
        """
        # 从NFS服务器获取文件路径
        file_paths = self.nfs_server.get_target_file(
            testobj_name="huakun_fw_upgrade", property_name=property_name, file_names=[file_name]
        )

        if "TMU" in file_name or "PMU" in file_name:
            self.nfs_host.rm({"path": "/root/.ssh/known_hosts"})

        if file_paths:
            nfs_server_ip = self.nfs_host.local_ip
            nfs_share_path = file_paths[0]
            nfs_url = f"nfs://{nfs_server_ip}{nfs_share_path}"
            return nfs_url
        raise HKTestException(f"未在NFS服务器找到固件文件: {file_name}")
        # 下载文件到BMC
        # desc_path = self.nfs_server.nfs_download_to_target(
        #     path_colls=file_paths,
        #     desc_path=target_path,
        #     host_type="BMC"
        # )

    def upgrade_firmware_version(
        self,
        file_path=None,
        remote_file_path=None,
        firmware_type=None,
        is_get_progress=True,
        mode="redfish",
        protocol="HTTPS",
        asyn=False,
    ):
        """
        固件升级
        Args:
            file_path           (str): 固件包文件存放的绝对路径 (固件包已在设备或local对应目录)
            remote_file_path    (str): 共享文件服务器上固件包存放的绝对路径
            firmware_type       (str): 固件类型
            is_get_progress     (bool): 是否进行升级进度查询
            mode               (str): 升级方式
        Returns:
            True/False
        """
        try:
            # 参数检查
            if not any([file_path, remote_file_path, firmware_type]):
                raise HKTestException("固件升级传入参数组合有误, 请检查")

            # 如果提供了文件名而不是完整路径，从NFS服务器获取
            # if file_path and not os.path.isfile(file_path):
            #     file_path = self.get_firmware_file(file_path)

            # 执行升级
            upload_ret = self.upgradeComponent.upgrade_firmware(
                file_path=file_path, is_get_progress=is_get_progress, mode=mode,
                protocol=protocol, asyn=asyn
            )

            return upload_ret

        except Exception as e:
            self.logger.error(f"固件升级失败: {str(e)}")
            return False

    def wait_redfish_task_finish(self, task_id, timeout=60, check_info=False):
        """查询服务器指定任务资源的信息

        Args:
            task_id: 待查询的任务id
            timeout: 超时时间(秒)
            check_info: 是否返回任务查询结果

        Returns:
            任务状态或任务信息

        Raises:
            HKTestException: 任务执行异常时抛出
        """
        for _ in range(timeout // 5):
            sleep(5)
            task_result = self.redfish_api.redfish_task_query_task_collection_id(taskid=task_id)
            self.logger.info(f"更新任务进度: {task_result}")

            if check_info:
                return task_result

            task_state = task_result.get("TaskState")
            if task_state == "Completed":
                self.logger.info("任务执行完成")
                return True
            elif task_state == "Running":
                self.logger.info("任务还在执行...")
                continue
            else:
                raise HKTestException("任务执行异常")

        raise HKTestException("任务执行超时")

    def get_firmware_version(self, firmware_type):
        """
        获取固件版本
        args:
            firmware_type: 固件类型 取值范围：ActiveBMC、BackupBMC、AvailableBMC、ActiveUboot、BackupUboot......
                                        具体可查看redfish接口：/redfish/v1/UpdateService/FirmwareInventory
        return: str 固件版本信息
            固件版本
        """
        params = {"firmware_id": firmware_type}
        response = self.redfish_api.get_firmware(params)
        return response.get("Version")

    def web_check_firmware_version(self, hardware_info, firmware_type=None) -> bool:
        """
        通过Web获取固件版本，检查版本是否正确
        args:

        return:
            True:   固件版本正确
            False:   固件版本不正确
        """
        version_list = self.web_api.web_get_fw_version_list()
        version_obj = DictListQuery(version_list)
        if firmware_type is not None:
            for key in hardware_info['versionInfo'][firmware_type].keys():
                version = version_obj.query("name", key)[0]["version"]
                if hardware_info['versionInfo'][firmware_type][key] != version:
                    self.logger.error(f"Web获取的{key}版本：{version}，与硬件配置文件中的版本："
                                      f"{hardware_info['versionInfo'][firmware_type][key]}不匹配")
                    return False
                else:
                    self.logger.info(f"Web获取的{key}版本：{version}，与硬件配置文件中的版本："
                                     f"{hardware_info['versionInfo'][firmware_type][key]}匹配")
        else:
            version_dict_list = hardware_info['versionInfo'].values()
            version_dict = {k: v for d in version_dict_list for k, v in d.items()}
            for item in version_dict.items():
                version_get = version_obj.query("name", item[0])[0]["version"]
                if item[1] != version_get:
                    self.logger.error(f"Web获取的{item[0]}版本：{version_get}，与硬件配置文件中的版本：{item[1]}不匹配")
                    return False
                else:
                    self.logger.info(f"Web获取的{item[0]}版本：{version_get}，与硬件配置文件中的版本：{item[1]}匹配")
        return True
