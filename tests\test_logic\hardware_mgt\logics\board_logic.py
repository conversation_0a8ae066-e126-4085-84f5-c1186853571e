"""
Description: Disk BackPlanes Managerment

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 16:33 created

"""
from typing import Any, Dict, List
from hkautos import log

from tests.test_logic.alias import HardwareMgt
from tests.test_logic.hardware_mgt.components.board_mgt import Boards_Mgt
from tests.test_logic.logic import Logic
from tests.test_data import common_fun as CommonFun


class Boards_Logic(Logic):
    alias = HardwareMgt.Boards

    def __init__(self, owning_device):
        super(Boards_Logic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.boards_Component = Boards_Mgt(owning_device)

    def redfish_check_diskbps_info(self, hardware_info):
        """
        通过redfish读取硬盘背板信息，检查信息是否正确
        Args:
            hardware_info：硬件配置表
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        redfish_diskbps_info = self.boards_Component.redfish.redfish_get_chassis_boards("DiskBackplane")
        self.logger.info(f"redfish获取的信息: {redfish_diskbps_info}")
        for diskbps_info in redfish_diskbps_info:
            if diskbps_info['Status']['Health'] != 'OK':
                return False
            del diskbps_info['Status']
            for hardwareinfo in hardware_info['Others']['Drive_Backplanes']:
                diskbps_info = CommonFun.convert_values_to_str(diskbps_info)
                if set(hardwareinfo.values()) & set(diskbps_info.values()) == set(hardwareinfo.values()):
                    self.logger.info(f"redfish获取的值与硬件配置文件相匹配")
                    break
                self.logger.error(f"redfish获取的值与硬件配置文件不匹配，redfish获取的信息: {diskbps_info}")
                self.logger.error(f"配置文件中硬盘背板信息: {hardware_info['Others']['Drive_Backplanes']}")
                return False
        return True

    def web_check_board_info(self, hardware_info: Dict, board_type: str) -> bool:
        """
        通过Web读取组件信息，检查信息是否正确，只适用于”系统管理“->”系统信息“->”其它“中大多数组件
        Args:
            hardware_info：硬件配置表
            board_type:    板类型，取值范围：硬件配置表中['Others'][0].keys()
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        web_boards_info = self.boards_Component.web_get_boards_info(board_type)
        self.logger.info(f"web获取的信息: {web_boards_info}")
        for board_info in web_boards_info:
            for hardwareinfo in hardware_info['Others'][board_type]:
                if set(hardwareinfo.values()) & set(board_info.values()) == set(hardwareinfo.values()):
                    self.logger.info(f"【信息一致】：\nWeb获取的值{board_info}\n硬件配置文件信息：{hardwareinfo}")
                    break

                # hardware_info配置文件中最后一个元素都没有匹配到，匹配失败
                if hardwareinfo == hardware_info['Others'][board_type][-1]:
                    self.logger.error(f"【信息不一致】：\nWeb获取的信息: {board_info}\n"
                                      f"硬件配置文件信息: {hardware_info['Others'][board_type]}")
                    return False
        return True
