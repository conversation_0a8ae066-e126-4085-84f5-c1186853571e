#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 任务管理 API

提供任务管理相关功能:
- 任务服务配置
- 任务队列管理
- 任务状态监控
- 任务操作控制

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Task(ApiBase):
    """任务管理 API"""

    def get_task_service(self) -> Dict[str, Any]:
        """获取任务服务信息"""
        return self.dispatcher.dispatch("redfish_get_task_service")

    def get_tasks(self) -> List[Dict[str, Any]]:
        """获取任务列表"""
        return self.dispatcher.dispatch("redfish_get_tasks")

    def get_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取任务信息

        URL: /redfish/v1/TaskService/Tasks/{task_id}
        请求方式: GET

        参数:
            params: 参数字典，常用字段:
                - task_id: 任务ID

        返回:
            任务详细信息
        """
        return self.dispatcher.dispatch("redfish_get_task", params=params)

    def delete_task(self, params: Dict[str, Any]) -> None:
        """
        删除任务

        URL: /redfish/v1/TaskService/Tasks/{task_id}
        请求方式: DELETE

        参数:
            params: 参数字典，常用字段:
                - task_id: 任务ID
        """
        return self.dispatcher.dispatch("redfish_delete_task", params=params)

    def get_task_monitor(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取任务监控信息

        URL: /redfish/v1/TaskService/Tasks/{task_id}/Monitor
        请求方式: GET

        参数:
            params: 参数字典，常用字段:
                - task_id: 任务ID

        返回:
            任务监控信息，包含:
            - TaskState: 任务状态
            - Messages: 状态消息
            - PercentComplete: 完成百分比
            等
        """
        return self.dispatcher.dispatch("redfish_get_task_monitor", params=params)
