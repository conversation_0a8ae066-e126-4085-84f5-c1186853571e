from functools import wraps
from typing import Any, Callable, Dict, List


class HookRegistry:
    """钩子注册器"""

    def __init__(self):
        """初始化钩子注册表"""
        self._hooks: Dict[str, List[Callable]] = {
            # 套件级别钩子
            "before_suite": [],
            "after_suite": [],
            # 用例级别钩子
            "before_test": [],
            "after_test": [],
            # 结果钩子
            "before_pass": [],  # 通过前
            "before_fail": [],  # 失败前
            "before_skip": [],  # 跳过前
            "after_pass": [],  # 通过后
            "after_fail": [],  # 失败后
            "after_skip": [],  # 跳过后
            # 清理钩子
            "before_cleanup": [],  # 清理前
            "after_cleanup": [],  # 清理后
        }
        self._registered_funcs = set()  # 用于追踪已注册的函数

    def register(self, hook_name: str, func: Callable = None) -> Callable:
        """注册钩子函数"""

        def decorator(func: Callable) -> Callable:
            if hook_name not in self._hooks:
                raise ValueError(f"Unknown hook: {hook_name}")

            # 使用函数的id作为唯一标识
            func_id = id(func)
            if func_id not in self._registered_funcs:

                @wraps(func)
                def wrapper(*args, **kwargs):
                    return func(*args, **kwargs)

                self._hooks[hook_name].append(wrapper)
                self._registered_funcs.add(func_id)
            return func

        if func is None:
            return decorator
        return decorator(func)

    def unregister(self, hook_name: str, func: Callable) -> None:
        """取消注册钩子函数"""
        if hook_name not in self._hooks:
            raise ValueError(f"Unknown hook: {hook_name}")
        if func in self._hooks[hook_name]:
            self._hooks[hook_name].remove(func)
            self._registered_funcs.remove(id(func))

    def clear(self, hook_name: str = None) -> None:
        """清除钩子"""
        if hook_name is None:
            for hooks in self._hooks.values():
                hooks.clear()
            self._registered_funcs.clear()
        elif hook_name in self._hooks:
            for func in self._hooks[hook_name]:
                self._registered_funcs.remove(id(func))
            self._hooks[hook_name].clear()
        else:
            raise ValueError(f"Unknown hook: {hook_name}")

    def get_hooks(self, hook_name: str = None) -> Dict[str, List[Callable]]:
        """获取已注册的钩子

        Args:
            hook_name: 要获取的钩子名称,如果为None则返回所有钩子
        """
        if hook_name is None:
            return self._hooks
        if hook_name not in self._hooks:
            raise ValueError(f"Unknown hook: {hook_name}")
        return {hook_name: self._hooks[hook_name]}

    def call(self, hook_name: str, *args: Any, **kwargs: Any) -> None:
        """调用钩子函数"""
        if hook_name not in self._hooks:
            raise ValueError(f"Unknown hook: {hook_name}")
        for func in self._hooks[hook_name]:
            func(*args, **kwargs)
