"""
Description: Snmp api - Maintenance

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.validation_utils import validate_dict

from . import snmp_ns


@snmp_ns.dispatchertype(HostType.Local)
class Maintenance(ApiBase):
    def __init__(self):
        super(Maintenance, self).__init__()

    def snmp_trap_enable(self, method="snmpget"):
        """5.4 trap详细规格
        节点名称: trapEnable
        节点OID实例: *******.4.1.2011.*********.4.1.0
        描述: Trap开关
        类型: INTEGER
        权限: read-write
        """
        params = {"method": method}
        validate_dict(
            params,
            {
                "method": {
                    "types": None,
                    "optional": True,
                    "enum": [
                        "'snmpget'",
                        "'snmpset'",
                        "'smnmpwalk'",
                        "'snmpgetnext'",
                        "'snmpgetbulk'",
                    ],
                }
            },
        )
        result = self.dispatcher.dispatch("snmp_trap_enable", params=params)
        return result
