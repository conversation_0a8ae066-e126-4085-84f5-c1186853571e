
"""
功   能：web_查询内存温度信息测试

修改信息：
    日期：   2025/07/31
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from tests.test_logic.alias import HardwareMgt, BMCMgt

class FRAME_MEM_INFO_FUNC_1080(Case):
    """
    CaseId:
        FRAME_MEM_INFO_FUNC_1080
    RunLevel:
        1(#1)
    CaseName:
        web_查询内存温度信息测试
    PreCondition:
        1、环境正常
    TestStep:
        1._登录BMC_Web，进入系统管理->系统信息->传感器，查询内存温度信息，有预期1
        2._在OS下，执行ipmitool_sensor_list命令，查询内存温度信息，有预期2
    ExpectedResult:
        1._支持内存温度相关传感器信息获取，对应传感器的当前温度值正常，状态为OK，无异常显示，每个CPU上报1个内存温度信息到web网页
        2._查询成功，无异常显示，对应名称与web界面保持一致，温度值正常，状态为OK
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.web_api = self.device_dut.get_api(ns_name="Web")

        self.boards_mgt = self.device_dut.find(HardwareMgt.Boards)
        self.sensor_mgt = self.device_dut.find(BMCMgt.Sensor)

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }
        self.logger.step("1. 登录BMC Web，进入系统管理->系统信息->传感器，查询内存温度信息，有预期1")
        self.web_handle = self.web_api.login(self.web_login_params)
        res = self.sensor_mgt.check_sensor_temp("MEM TEMP")
        self.assertTrue(res, "支持内存温度相关传感器信息获取，对应传感器的当前温度值正常，状态为OK，无异常显示，每个CPU上报1个内存温度信息到web网页")

        self.logger.step("2. 在OS下，执行ipmitool sensor list命令，查询内存温度信息，有预期2")
        res = self.sensor_mgt.check_sensor_temp("MEM TEMP", "ipmi")
        self.assertTrue(res, "查询成功，无异常显示，对应名称与web界面保持一致，温度值正常，状态为OK")


    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.web_api.logout(self.web_handle)
