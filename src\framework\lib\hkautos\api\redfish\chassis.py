#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 机箱管理 API

提供机箱管理相关功能:
- 机箱资源配置
- 传感器管理
- 电源管理
- 散热管理
- LED指示灯管理

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Chassis(ApiBase):
    """机箱管理 API"""

    def get_chassis(self) -> List[Dict[str, Any]]:
        """获取机箱列表"""
        return self.dispatcher.dispatch("redfish_get_chassis")

    def get_chassis_info(self) -> Dict[str, Any]:
        """获取机箱信息"""
        return self.dispatcher.dispatch("redfish_get_chassis_info")

    def get_sensors(self) -> List[Dict[str, Any]]:
        """获取传感器列表"""
        return self.dispatcher.dispatch("redfish_get_sensors")

    def get_chassis_power(self) -> Dict[str, Any]:
        """获取电源信息"""
        return self.dispatcher.dispatch("redfish_get_chassis_power")

    def get_indicator_led(self) -> Dict[str, Any]:
        """获取指示灯状态"""
        return self.dispatcher.dispatch("redfish_get_indicator_led")

    def set_indicator_led(self, params: Dict[str, Any]) -> None:
        """
        设置指示灯状态

        参数:
            params: 参数字典，常用字段:
                - State: 指示灯状态(Lit/Blinking/Off)
                - Color: 指示灯颜色(Red/Green/Blue等)
        """
        return self.dispatcher.dispatch("redfish_set_indicator_led", params=params)

    def redfish_get_chassis_pcie_devices(self) -> List[Dict[str, Any]]:
        """查询PCIE设备资源信息列表"""
        return self.dispatcher.dispatch("redfish_get_chassis_pcie_devices")

    def redfish_get_chassis_drives(self) -> List[Dict[str, Any]]:
        """查询chassis_drives盘的资源信息列表"""
        return self.dispatcher.dispatch("redfish_get_chassis_drives")

    def redfish_get_chassis_thermal(self) -> List[Dict[str, Any]]:
        """查询chassis_thermal风扇的资源信息列表"""
        return self.dispatcher.dispatch("redfish_get_thermal")

    def redfish_get_fan_info(self) -> List[Dict[str, Any]]:
        """查询chassis/thermal/风扇模块的资源信息列表"""
        return self.dispatcher.dispatch("redfish_get_fan_info")

    def redfish_get_chassis_boards(self, board_type=None) -> List[Dict[str, Any]]:
        """查询chassis boards板卡的资源信息列表"""
        return self.dispatcher.dispatch("redfish_get_chassis_boards", params=board_type)

    def redfish_get_chassis_bps(self, params_bp_id) -> List[Dict[str, Any]]:
        """查询chassis 指定BP板卡的资源信息列表"""
        params_bp_id = {"params_bp_id": params_bp_id}
        result = self.dispatcher.dispatch("redfish_get_chassis_bps", params=params_bp_id)
        return result

    def redfish_patch_indicator_led(self, params: Dict[str, Any]) -> None:
        """
        设置UID操作
        参数:
            params: 参数字典，例如常用字段:'IndicatorLED': "Blinking", 'Duration': 30
            - IndicatorLED: 指示灯状态(Lit/Blinking/Off)
            - Duration: 指示灯闪烁时长
        """
        return self.dispatcher.dispatch("redfish_patch_id_indicator_led", params=params)

    def redfish_get_indicator_led(self) -> None:
        """
        查询UID状态
        """
        return self.dispatcher.dispatch("redfish_get_jd_indicator_led")
