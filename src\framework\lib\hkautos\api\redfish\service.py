#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 服务根目录 API

提供服务根目录相关功能:
- 服务版本信息
- 服务能力查询
- 服务资源导航
- 服务健康状态

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Service(ApiBase):
    """服务管理 API"""

    def get_service_root(self) -> Dict[str, Any]:
        """获取服务根信息"""
        return self.dispatcher.dispatch("redfish_get_service_root")

    def get_service_enabled(self) -> bool:
        """获取服务启用状态"""
        return self.dispatcher.dispatch("redfish_get_service_enabled")

    def get_service_version(self) -> str:
        """获取服务版本"""
        return self.dispatcher.dispatch("redfish_get_service_version")

    def get_service_time(self) -> str:
        """获取服务时间"""
        return self.dispatcher.dispatch("redfish_get_service_time")

    def set_service_time(self, params: Dict[str, Any]) -> None:
        """
        设置服务时间

        参数:
            params: 参数字典，常用字段:
                - DateTime: 时间字符串(ISO 8601格式)
                - DateTimeLocalOffset: 时区偏移
        """
        return self.dispatcher.dispatch("redfish_set_service_time", params=params)

    def get_odata(self) -> Dict[str, Any]:
        """
        获取OData服务文档

        URL: /redfish/v1/odata
        请求方式: GET

        返回:
            OData服务文档，包含:
            - value: 服务列表
            - @odata.context: 上下文URI
            等
        """
        return self.dispatcher.dispatch("redfish_get_odata")

    def get_metadata(self) -> str:
        """
        获取元数据文档

        URL: /redfish/v1/$metadata
        请求方式: GET

        返回:
            CSDL格式的元数据文档
        """
        return self.dispatcher.dispatch("redfish_get_metadata")

    def get_version(self) -> Dict[str, Any]:
        """
        获取协议版本信息

        URL: /redfish
        请求方式: GET

        返回:
            版本信息，包含:
            - v1: 协议版本URI
            等
        """
        return self.dispatcher.dispatch("redfish_get_version")
        
    def get_system_assets(self) -> Dict[str, Any]:
        """
        获取系统资产信息
        
        返回:
            包含系统信息、内存信息、PSU FRU信息等的字典
        """
        return {
            "system": self.get_system_info(),
            "memory": self.get_memory_info(),
            "psu": self.get_psu_info(),
            "processor": self.get_processor_info(),
            "bmc": self.get_bmc_info(),
            "logs": self.get_log_info(),
            "storage": self.get_storage_info(),
            "ethernet": self.get_ethernet_info()
        }
        
    def get_system_info(self, params) -> Dict[str, Any]:
        """
        获取系统信息
        
        返回:
            系统信息字典
        """
        return self.dispatcher.dispatch("redfish_get_system_info", params=params)
        
    def get_memory_set(self, params) -> Dict[str, Any]:
        """
        获取内存信息
        
        返回:
            内存信息字典
        """
        return self.dispatcher.dispatch("redfish_get_memory_set", params=params)

    def get_memory_info(self, params) -> Dict[str, Any]:
        """
        获取指定内存信息

        返回:
            内存信息字典
        """
        return self.dispatcher.dispatch("redfish_get_memory_info", params=params)

    def get_psu_info(self) -> Dict[str, Any]:
        """
        获取PSU信息
        
        返回:
            PSU信息字典
        """
        return self.dispatcher.dispatch("redfish_get_psu_info")
        
    def get_processor_info(self) -> Dict[str, Any]:
        """
        获取处理器信息
        
        返回:
            处理器信息字典
        """
        return self.dispatcher.dispatch("redfish_get_processor_info")
        
    def get_bmc_info(self) -> Dict[str, Any]:
        """
        获取BMC FRU信息
        
        返回:
            BMC FRU信息字典
        """
        return self.dispatcher.dispatch("redfish_get_bmc_info")
        
    def get_log_info(self) -> Dict[str, Any]:
        """
        获取日志信息
        
        返回:
            日志信息字典
        """
        return self.dispatcher.dispatch("redfish_get_log_info")
        
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        返回:
            存储信息字典
        """
        return self.dispatcher.dispatch("redfish_get_storage_info")
        
    def get_ethernet_info(self) -> Dict[str, Any]:
        """
        获取网口信息
        
        返回:
            网口信息字典
        """
        return self.dispatcher.dispatch("redfish_get_ethernet_info")
