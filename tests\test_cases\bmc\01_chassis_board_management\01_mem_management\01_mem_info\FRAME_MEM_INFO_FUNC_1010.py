
"""
功   能：BMC识别内存测试

修改信息：
    日期：   2025/07/31
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt

class FRAME_MEM_INFO_FUNC_1010(Case):
    """
    CaseId:
        FRAME_MEM_INFO_FUNC_1010
    RunLevel:
        1(#1)
    CaseName:
        BMC识别内存测试
    PreCondition:
        1、单板配置普通内存条
        2、系统处于上电状态，且业务侧正常启动
    TestStep:
        1._登录BMC，在WEB的BMC管理界面查询内存基本信息，检查是否正确。
    ExpectedResult:
        1._BMC_web管理界面可以查询内存的基本信息且与OS下查询到的一致。（包括不限于：型号、容量、频率、参考电压）
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.mem_mgt = self.device_dut.find(HardwareMgt.Mem)
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.web_api = self.device_dut.get_api(ns_name="Web")
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }
        self.logger.step("1. 登录BMC，在WEB的BMC管理界面查询内存基本信息，检查是否正确。")
        self.web_handle = self.web_api.login(self.web_login_params)
        res = self.mem_mgt.check_mem_info(self.hardware_json)
        self.assertTrue(res, "BMC_web管理界面可以查询内存的基本信息且与OS下查询到的一致。（包括不限于：型号、容量、频率、参考电压）")
        

    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.web_api.logout(self.web_handle)
