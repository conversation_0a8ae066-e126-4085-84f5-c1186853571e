"""
功    能:

版权信息: ©2023-2028 四川华鲲振宇智能科技有限责任公司

"""

# from hkautos.utils.time import sleep
# from hkautos.wrapper.huawei.web.ibmcv2r2c90.common import pageenum
from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.system_page import SystemPage


def web_set_power_control(self, params):
    """
    设置服务器上下电状态
    Args:
        params (dict):
            state (int):
                      0 下电 安全下电,与下电时限有关
                      1 上电
                      2 强制下电
                      3 重启 无上下电状态变化
                      4 先下电再上电 先强制下电再上电
                      5 NMI 中断
              confirm(bool):
                     True 点击确定
                     False 点击取消
    Returns:
        成功返回：True 失败 raise
    """
    syspage = SystemPage(self.connection)
    syspage.web_power_mgt_page.web_set_power_control(params)

def web_get_psu_info(self):
    """
    获取PSU信息
    Args:
        None
    Returns:
        result: List[Dict[str, Any]]，PSU信息
    """
    syspage = SystemPage(self.connection)
    return syspage.web_power_mgt_page.web_get_psu_info()

def web_get_fan_info(self):
    """
    获取PSU信息
    Args:
        None
    Returns:
        result: List[Dict[str, Any]]，FAN信息
    """
    syspage = SystemPage(self.connection)
    return syspage.web_fan_mgt_page.web_get_fan_info()

def web_get_system_lut(self):
    """
    获取web system页面的关键字对照表（Look-Up Table）
    Args:
        None
    Returns:
        result: Dict[str, Any]，web system页面的关键字对照表
    """
    syspage = SystemPage(self.connection)
    return syspage.web_system_info_page.web_get_system_lut()

def web_get_cpu_info(self):
    """
    获取处理器信息
    Args:
        None
    Returns:
        result: List[Dict[str, Any]]，CPU信息
    """
    syspage = SystemPage(self.connection)
    return syspage.web_system_info_page.web_get_cpu_info()


def web_get_mem_info(self):
    """
    获取内存信息
    Args:
        None
    Returns:
        result: List[Dict[str, Any]]，内存信息
    """
    syspage = SystemPage(self.connection)
    return syspage.web_system_info_page.web_get_mem_info()


def web_get_board_info(self):
    """
    获取单板信息
    Args:
        params (dict):
    Returns:
        成功返回：True 失败 raise
    """
    syspage = SystemPage(self.connection)
    return syspage.web_system_info_page.web_get_board_info()

def web_get_sensor_info(self):
    """
    WEB获取传感器信息
    Args:
        None:
    Returns:
        result: Dict[str, Any], 传感器信息
    """
    syspage = SystemPage(self.connection)
    return syspage.web_system_info_page.web_get_sensor_info()

def web_get_system_others_info(self, model):
    """
    WEB获取”系统管理“->”系统信息“->”其它“信息
    Args:
        model:  模块名称
    Returns:
        result: List[Dict[str, Any]]，组件信息
    """
    syspage = SystemPage(self.connection)
    return syspage.web_system_info_page.web_get_system_others_info(model)
