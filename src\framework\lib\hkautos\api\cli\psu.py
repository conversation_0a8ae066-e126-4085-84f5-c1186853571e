"""
Description: 4.14 电源命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Psu(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Psu, self).__init__()
        self.name = "psu"

    @validate_param(option=str)
    def cli_set_psu_work_mode(self, option, active_psuid=None):
        """4.14.1 设置电源工作模式（psuworkmode）
            命令功能: psuworkmode命令用来设置电源工作模式。
            命令格式: ipmcset -d psuworkmode -v <option> [active_psuid]
        Args:
            option: 电源工作模式
            active_psuid: 电源工作模式为主备模式时，主电源的ID。
        """
        params = {"option": option, "active_psuid": active_psuid}
        result = self.dispatcher.dispatch("cli_set_psu_work_mode", params=params)[0]["parser"]
        return result

    def cli_query_basic_psu(self):
        """4.14.2 查询电源具体信息（psuinfo）
        命令功能: psuinfo命令用来获取电源信息。
        命令格式: ipmcget -d psuinfo
        """
        result = self.dispatcher.dispatch("cli_query_basic_psu")[0]["parser"]
        return result

    def cli_set_power_state(self, state=None, confirm="y", module=None, operation=None):
        """4.14.3 控制设备上/下电（powerstate）
            命令功能: powerstate命令用来控制设备上/下电。
            命令格式: ipmcset -d powerstate -v <state>
                     ipmcset -l <module> -d powerstate -v <operation>
        Args:
            Args:
                state(str) 可选[0/1/2]（0:正常下电 1:正常上电 2:强制下电）
                confirm(str) 可选[y/n]
                module(str) 可选[bladeN]。
                operation(str) 可选[poweron/poweroff/forcepoweroff]。
        """
        params = {
            "state": "state",
            "confirm": "confirm",
            "module": "module",
            "operation": "operation",
        }
        result = self.dispatcher.dispatch("cli_set_power_state", params=params)[0]["parser"]
        return result
