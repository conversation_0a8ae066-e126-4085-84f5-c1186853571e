"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class SensorDevice(ApiBase):
    def __init__(self):
        super(SensorDevice, self).__init__()

    def get_sensor_list(self, params=None):
        """
        获取所有传感器列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的传感器列表信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sensor_list", params=params)[0]["parser"]

    def get_sensor(self, params=None):
        """
        获取指定传感器信息

        Args:
            params: 参数字典
                   例如: {'sensor_id': 'CPU1 Temp'}

        Returns:
            dict: 返回解析后的传感器信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sensor_get", params=params)[0]["parser"]

    def get_sensor_thresh(self, params=None):
        """
        获取传感器阈值

        Args:
            params: 参数字典
                   例如: {'sensor_id': 'CPU1 Temp'}

        Returns:
            dict: 返回解析后的传感器阈值信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sensor_thresh", params=params)[0]["parser"]

    def set_sensor_thresh(self, params=None):
        """
        设置传感器阈值

        Args:
            params: 参数字典
                   例如: {
                       'sensor_id': 'CPU1 Temp',
                       'thresh_type': 'ucr',
                       'value': '85'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sensor_set_thresh", params=params)[0]["parser"]

    def get_sensor_device_info(self, params=None):
        """
        获取指定传感器信息

        Args:
            params: 参数字典
                   例如: {'sensor_id': '0x01'}

        Returns:
            dict: 返回解析后的传感器信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sensor_device_info", params=params)[0]["parser"]

    def set_sensor_upper(self, params=None):
        """
        获取指定传感器信息

        Args:
            params: 参数字典
                   例如: {'sensor_id': '0x01'}

        Returns:
            dict: 返回解析后的传感器信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sensor_upper", params=params)[0]["parser"]

    def get_sensor_read(self, params=None):
        """
        获取指定传感器信息

        Args:
            params: 参数字典
                   例如: {'sensor_id': '0x01'}

        Returns:
            str: 返回解析后的传感器信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sensor_read", params=params)[0]["parser"]

    def sensor_read_get(self, params=None):
        """
        获取指定传感器信息

        Args:
            params: 参数字典
                   例如: {'sensor_id': 'CPU1 Temp'}

        Returns:
            str: 返回解析后的传感器信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sensor_read_get", params=params)[0]["parser"]

    def get_sensor_type(self, params=None):
        """
        获取指定传感器信息

        Args:
            params: 参数字典
                   例如: {'sensor_id': '0x01'}

        Returns:
            str: 返回解析后的传感器信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sensor_type", params=params)[0]["parser"]

    def get_sensor_id_list(self, params=None):
        """
        获取传感器id列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sensor_id_list", params=params)[0]["parser"]

    def set_sensor(self, params=None):
        """
        设置传感器信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sensor", params=params)[0]["parser"]

    def set_sensor_threshold(self, params=None):
        """
        获取传感器id列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sensor_threshold", params=params)[0]["parser"]
