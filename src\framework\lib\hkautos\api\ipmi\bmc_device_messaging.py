"""
Description: Ipmi API

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/03/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.api.ipmi import ipmi_ns
from hkautos.config.enum import HostType


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class BMCDeviceMessaging(ApiBase):
    def __init__(self):
        super(BMCDeviceMessaging, self).__init__()

    def ipmi_set_bmc_global_enables(self, params=None):
        """
        3.1 设置BMC全局启用应用程序06h 2Eh管理员
            Set BMC Global Enables App 06h 2Eh Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_bmc_global_enables", params=params)[
            0
        ]["parser"]

    def ipmi_get_bmc_global_enables(self, params=None):
        """
        3.2 获取BMC全局启用应用程序06h 2Fh用户
            Get BMC Global Enables App 06h 2Fh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_bmc_global_enables", params=params)[
            0
        ]["parser"]

    def ipmi_clear_message_flags(self, params=None):
        """
        3.3 清除消息标志应用06h 30h用户
            Clear Message Flags App 06h 30h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_clear_message_flags", params=params)[0][
            "parser"
        ]

    def ipmi_get_message_flags(self, params=None):
        """
        3.4 获取消息标志应用程序06h 31h用户
            Get Message Flags App 06h 31h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_message_flags", params=params)[0][
            "parser"
        ]

    def ipmi_get_message(self, params=None):
        """
        3.5 获取消息应用程序06h 33h用户
            Get Message App 06h 33h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_message", params=params)[0]["parser"]

    def ipmi_send_message(self, params=None):
        """
        3.6 发送消息应用程序06h 34h用户
            Send Message App 06h 34h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_send_message", params=params)[0]["parser"]

    def ipmi_read_message(self, params=None):
        """
        3.7 阅读消息应用程序06h 35h用户
            Read Message App 06h 35h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_read_message", params=params)[0]["parser"]

    def ipmi_get_system_guid(self, params=None):
        """
        3.8 获取系统GUID应用程序06h 37h保留
            Get System GUID App 06h 37h RESERVED
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_system_guid", params=params)[0][
            "parser"
        ]

    def ipmi_get_channel_authentication_capabilities(self, params=None):
        """
        3.9 获取通道身份验证功能应用程序06h 38h保留
            Get Channel Authentication Capabilities App 06h 38h RESERVED
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_channel_authentication_capabilities", params=params
        )[0]["parser"]

    def ipmi_get_session_challenge(self, params=None):
        """
        3.10 获取会话挑战应用程序06h 39h保留
            Get Session Challenge App 06h 39h RESERVED
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_session_challenge", params=params)[0][
            "parser"
        ]

    def ipmi_activate_session(self, params=None):
        """
        3.11    激活会话应用程序06h 3Ah用户
                Activate Session App 06h 3Ah User
        IPMI方式:SOL(去)激活命令
        Args:
            None
        Returns:
            None
        Raises:
            None
        """
        _templates = {
            "opt": {
                "types": None,
                "optional": False,
                "enum": ["activate", "deactivate"],
            }
        }
        return self.dispatcher.dispatch("ipmi_activate_session", params=params)[0][
            "parser"
        ]

    def ipmi_set_session_privilege_level(self, params=None):
        """
        3.12 设置会话权限级别应用06h 3Bh用户
            Set Session Privilege Level App 06h 3Bh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_set_session_privilege_level", params=params
        )[0]["parser"]

    def ipmi_close_session(self, params=None):
        """
        3.13 关闭会话应用程序06h 3Ch用户
            Close Session App 06h 3Ch User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_close_session", params=params)[0][
            "parser"
        ]

    def ipmi_get_session_info(self, params=None):
        """
        3.14 获取会话信息应用程序06h 3Dh用户
            Get Session Info App 06h 3Dh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_session_info", params=params)[0][
            "parser"
        ]

    def ipmi_set_channel_access(self, params=None):
        """
        3.15 设置通道访问应用程序06h 40h管理员
            Set Channel Access App 06h 40h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_channel_access", params=params)[0][
            "parser"
        ]

    def ipmi_get_channel_access(self, params=None):
        """
        3.16 获取频道访问应用程序06h 41h用户
            Get Channel Access App 06h 41h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_channel_access", params=params)[0][
            "parser"
        ]

    def ipmi_get_channel_info(self, params=None):
        """
        3.17 获取频道信息应用06h 42h用户
            Get Channel Info App 06h 42h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_channel_info", params=params)[0][
            "parser"
        ]

    def ipmi_set_user_access_command(self, params=None):
        """
        3.18 设置用户访问命令应用06h 43h管理员
            Set User Access Command App 06h 43h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_user_access_command", params=params)[0]["parser"]

    def ipmi_get_user_access_command(self, params=None):
        """
        3.19 获取用户访问命令应用程序06h 44h管理员
            Get User Access Command App 06h 44h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_user_access_command", params=params)[
            0
        ]["parser"]

    def ipmi_set_user_name_command(self, params=None):
        """
        3.20 设置用户名命令应用06h 45h管理员
            Set User Name Command App 06h 45h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_user_name_command", params=params)[0][
            "parser"
        ]

    def ipmi_get_user_name_command(self, params=None):
        """
        3.21 获取用户名命令应用程序06h 46h管理员
            Get User Name Command App 06h 46h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_user_name_command", params=params)[0][
            "parser"
        ]

    def ipmi_set_user_password_command(self, params=None):
        """
        3.22 设置用户密码命令应用06h 47h管理员
            Set User Password Command App 06h 47h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_set_user_password_command", params=params
        )[0]["parser"]

    def ipmi_activate_payload(self, params=None):
        """
        3.23 激活有效负载应用程序06h 48h操作员
            Activate Payload App 06h 48h Operator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_activate_payload", params=params)[0][
            "parser"
        ]

    def ipmi_deactivate_payload(self, params=None):
        """
        3.24 停用有效负载应用06h 49h操作员
            Deactivate Payload App 06h 49h Operator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_deactivate_payload", params=params)[0][
            "parser"
        ]

    def ipmi_get_payload_activation_status(self, params=None):
        """
        3.25 获取有效负载激活状态应用06h 4Ah用户
            Get Payload Activation Status App 06h 4Ah User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_payload_activation_status", params=params
        )[0]["parser"]

    def ipmi_get_payload_instance_info(self, params=None):
        """
        3.26 获取有效负载实例信息应用06h 4Bh用户
            Get Payload Instance Info App 06h 4Bh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_payload_instance_info", params=params
        )[0]["parser"]

    def ipmi_set_user_payload_access(self, params=None):
        """
        3.27 设置用户有效负载访问应用程序06h 4Ch管理员
            Set User Payload Access App 06h 4Ch Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_user_payload_access", params=params)[
            0
        ]["parser"]

    def ipmi_get_user_payload_access(self, params=None):
        """
        3.28 获取用户有效负载访问应用程序06h 4Dh用户
            Get User Payload Access App 06h 4Dh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_user_payload_access", params=params)[
            0
        ]["parser"]

    def ipmi_get_channel_payload_support(self, params=None):
        """
        3.29 获取频道有效负载支持应用程序06h 4Eh用户
            Get Channel Payload Support App 06h 4Eh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_channel_payload_support", params=params
        )[0]["parser"]

    def ipmi_get_channel_payload_version(self, params=None):
        """
        3.30 获取频道有效负载版本应用程序06h 4Fh用户
            Get Channel Payload Version App 06h 4Fh User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_channel_payload_version", params=params
        )[0]["parser"]

    def ipmi_get_channel_cipher_suites(self, params=None):
        """
        3.31 获取通道密码套件应用程序06h 54h用户
            Get Channel Cipher Suites App 06h 54h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_channel_cipher_suites", params=params
        )[0]["parser"]

    def ipmi_suspend_resume_payload_encryption(self, params=None):
        """
        3.32 暂停/恢复有效负载加密应用06h 55h用户
            Suspend/Resume Payload Encryption App 06h 55h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_suspend_resume_payload_encryption", params=params
        )[0]["parser"]

    def ipmi_set_channel_security_keys(self, params=None):
        """
        3.33 设置通道安全密钥应用程序06h 56h管理员
            Set Channel Security Keys App 06h 56h Administrator
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_set_channel_security_keys", params=params
        )[0]["parser"]

    def ipmi_get_system_interface_capabilities(self, params=None):
        """
        3.34 获取系统接口功能应用程序06h 57h用户
            Get System Interface Capabilities App 06h 57h User
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch(
            "ipmi_get_system_interface_capabilities", params=params
        )[0]["parser"]

    def ipmi_set_system_info_parameters_block_data(self, params=None):
        """
        3.35.1  设置系统信息参数App 06h 58h操作员
                Set System Info Parameters App 06h 58h Operator
        IPMI方式:block写入数据
        Args:
            params（dict）：
            {'number':为第number个block，取值范围0-255
            'data':写入的数据
            }
        Returns:
            None
        Raises:
            None
        """
        _templates = {
            "number": {"types": str, "optional": False},
            "data": {"types": str, "optional": False},
        }

        return self.dispatcher.dispatch(
            "ipmi_set_system_info_parameters_block_data", params=params
        )[0]["parser"]

    def ipmi_set_system_info_parameters_report_pcie_spec_info(self, params=None):
        """
        3.35.2 设置系统信息参数App 06h 58h操作员
            Set System Info Parameters App 06h 58h Operator
        上报芯片故障状态或芯片温度
        Args:
            params    (dict):{'cpu_num':str, 'bus_num':str,
                            'device_num':str, 'function_num':str,
                            'info_type':str, 'chip_amount':str,
                            'data_detail':str}
                            cpu_num PCIe卡所属CPU编号，例如‘0’
                            bus_num PCIe卡的BusNum
                            device_num PCIe卡的DeviceNum
                            function_num PCIe卡的FunctionNum
                            info_type 上报芯片温度：'0'
                                    上报芯片故障状态：'1'
                            chip_amount PCIe卡芯片个数，与后面的data_detail对应
                            data_detail：
                                    当info_type为0时：
                                    data10       芯片个数
                                    data11:12  芯片0的温度，其中0x7fff表示温度读取失败，
                                    0x7fffd表示当前温度不可读（此处是为了
                                    与PCIe设备带外管理接口规范保持统一）；
                                    data13:14  芯片1的温度，参考data11:12定义
                                    data15:16  芯片2的温度，参考data11:12定义
                                    data16:N    按照上述定义类推

                                    当info_type为1时：
                                    data10     芯片个数
                                    data11     芯片0的故障状态，
                                                    0：表示芯片状态正常
                                                    1：表示芯片出现故障（BMC统一处理为严重告警事件）
                                                    其它：保留
                                    data12     芯片1的故障状态，参考data11定义
                                    data13     芯片2的故障状态，参考data11定义
                                    data14:N  按照上述定义类推
        Returns:
                成功返回空
        Raises:
            None
        """
        _templates = {
            "cpu_num": {"types": str, "optional": False},
            "bus_num": {"types": str, "optional": False},
            "device_num": {"types": str, "optional": False},
            "function_num": {"types": str, "optional": False},
            "info_type": {"types": str, "optional": False},
            "chip_amount": {"types": str, "optional": False},
            "data_detail": {"types": str, "optional": False},
        }
        return self.dispatcher.dispatch(
            "ipmi_set_system_info_parameters_report_pcie_spec_info", params=params
        )[0]["parser"]

    def ipmi_set_system_info_parameters_sys_info_param(self, params=None):
        """
        3.35.3  设置系统信息参数App 06h 58h操作员
                Set System Info Parameters App 06h 58h Operator
        发送Set System Info Parameters命令
        Args:
            region(str)：    区号
            Data(str)：      数据
        Returns:
            None
        Raises:
            None
        """
        return self.dispatcher.dispatch(
            "ipmi_set_system_info_parameters_sys_info_param", params=params
        )[0]["parser"]

    def ipmi_get_system_info_parameters_block_data(self, params=None):
        """
        3.36.1 获取系统信息参数App 06h 59h用户
            Get System Info Parameters App 06h 59h User
        IPMI方式:block读取数据
        Args:
            opt  (str)  为第opt个block，取值范围0-255
        Returns:
            None
        Raises:
            None
        """
        return self.dispatcher.dispatch(
            "ipmi_get_system_info_parameters_block_data", params=params
        )[0]["parser"]

    def ipmi_get_system_info_parameters_sys_info_param(self, params=None):
        """
        3.36.2  获取系统信息参数App 06h 59h用户
                Get System Info Parameters App 06h 59h User
        发送Get System Info Parameters命令
        Args:
            region(str)：    区号
        Returns:
            None
        Raises:
            None
        """
        return self.dispatcher.dispatch(
            "ipmi_get_system_info_parameters_sys_info_param", params=params
        )[0]["parser"]

    def get_session_active(self, params=None):
        """
        获取当前活跃的会话
        """
        return self.dispatcher.dispatch("ipmi_get_session_active", params=params)[0]["parser"]

    def get_session_all(self, params=None):
        """
        获取当前活跃的会话
        """
        return self.dispatcher.dispatch("ipmi_get_session_all", params=params)[0]["parser"]

    def ipmi_user_test(self, params=None):
        """
        3.37 用户测试
            User Test
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_user_test", params=params)[0]["parser"]

    def set_channel_access(self, params=None):
        """
        3.38 设置通道访问
            Set Channel Access
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_channel_access", params=params)[0]["parser"]

    def get_acpi_power_state(self, params=None):
        """
        3.39 获取ACPI电源状态
            Get ACPI Power State
        Args:
            params:(dict)     无
        """
        return self.dispatcher.dispatch("ipmi_get_acpi_power_state", params=params)[0][
            "parser"
        ]

    def ipmi_set_acpi_power_state(self, params=None):
        """
        3.40 设置ACPI电源状态
            Set ACPI Power State
        Args:
            params:(dict)     无
        """
        return self.dispatcher.dispatch("ipmi_set_acpi_power_state", params=params)[0][
            "parser"
        ]

    def get_mc_system_info(self, params=None):
        """
        3.40 设置ACPI电源状态
            Set ACPI Power State
        Args:
            params:(dict)     无
        """
        return self.dispatcher.dispatch("ipmi_mc_get_system_info", params=params)[0][
            "parser"
        ]
