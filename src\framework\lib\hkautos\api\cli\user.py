"""
Description: 4.9 用户管理命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class User(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(User, self).__init__()
        self.name = "user"

    def cli_query_all_users(self):
        """4.9.1 查询所有用户信息（userlist/list）
        命令功能: userlist命令用来查询所有用户信息。
        命令格式: ipmcget -d userlist
                ipmcget -t user -d list
        """
        result = self.dispatcher.dispatch("cli_query_all_users")[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, password=str)
    def cli_add_user(self, login_user_password, username, password):
        """4.9.2 添加新用户（adduser）
            命令功能: adduser用于添加新用户。
            命令格式: ipmcset [-t user] -d adduser -v <username>
        Args:
            username: 表示待添加的用户名。
        """
        params = {
            "login_user_password": login_user_password,
            "username": username,
            "password": password,
        }
        result = self.dispatcher.dispatch("cli_add_user", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, password=str)
    def cli_chang_user_password(self, login_user_password, username, password):
        """4.9.3 修改用户密码（password）
            命令功能: password命令用来修改用户密码。
            命令格式: ipmcset [-t user] -d password -v username
        Args:
            username: 表示已存在的待修改密码的用户名。
        """
        params = {
            "login_user_password": login_user_password,
            "username": username,
            "password": password,
        }
        result = self.dispatcher.dispatch("cli_chang_user_password", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str)
    def cli_delete_user(self, login_user_password, username):
        """4.9.4 删除用户（deluser）
            命令功能: deluser用来删除用户。
            命令格式: ipmcset [-t user] -d deluser -v username
        Args:
            username: 表示当前存在的待删除的用户名。
        """
        params = {"login_user_password": login_user_password, "username": username}
        result = self.dispatcher.dispatch("cli_delete_user", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, privalue=str)
    def cli_set_user_rights(self, login_user_password, username, privalue):
        """4.9.5 设置用户权限（privilege）
            命令功能: privilege命令用来设置用户权限。
            命令格式: ipmcset [-t user] -d privilege -v <username> <privalue>
        Args:
            username: 表示当前存在的待设置权限的用户名。
            privalue: 用户权限
        """
        params = {
            "login_user_password": login_user_password,
            "username": username,
            "privalue": privalue,
        }
        result = self.dispatcher.dispatch("cli_set_user_rights", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_complexity_check(self, method, value=None):
        """4.9.6 查询和设置密码检查功能（passwordcomplexity）
            命令功能: passwordcomplexity命令用来查询和设置密码复杂度检查功能的启用状态。
            命令格式: ipmcget [-t user] -d passwordcomplexity
                    ipmcset [-t user] -d passwordcomplexity -v <enabled | disabled>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            value:
                enabled: 启用密码复杂度检查功能
                disabled: 禁用密码复杂度检查功能
        """
        params = {"method": method, "value": value}
        result = self.dispatcher.dispatch("cli_query_set_complexity_check", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str)
    def cli_lock_user(self, login_user_password, username):
        """4.9.7 锁定用户（user -d lock）
            命令功能: lock命令用于锁定指定的用户，而用户在被锁定之后将不能登录。
            命令格式: ipmcset -t user -d lock -v username
        Args:
            username: 待锁定用户的用户名
        """
        params = {"login_user_password": login_user_password, "username": username}
        result = self.dispatcher.dispatch("cli_lock_user", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str)
    def cli_unlock_user(self, login_user_password, username):
        """4.9.8 解除用户锁定状态（user -d unlock）
            命令功能: unlock命令用于解锁被手动锁定或因密码重试次数用完而锁定的用户。
            命令格式: ipmcset -t user -d unlock -v username
        Args:
            username: 待解锁用户的用户名
        """
        params = {"login_user_password": login_user_password, "username": username}
        result = self.dispatcher.dispatch("cli_unlock_user", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_minimum_password_age(self, method, time=None):
        """4.9.9 查询和设置密码最短使用期（minimumpasswordage）
            命令功能: minimumpasswordage命令用于查询和设置密码的最短使用期。
                    密码最短使用期，是指设置一个密码后，要使用的最短时间，在此期间不能修改此密码。
            命令格式: ipmcget -d minimumpasswordage
                    ipmcset -d minimumpasswordage -v time
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            time: 密码最短使用期
        """
        params = {"method": method, "time": time}
        result = self.dispatcher.dispatch("cli_query_set_minimum_password_age", params=params)[0]["parser"]
        return result

    @validate_param(username=str)
    def cli_set_emergency_user(self, username):
        """4.9.10 设置紧急用户（emergencyuser）
            命令功能: emergencyuser命令用于设置不受登录规则限制的紧急用户。
            命令格式: ipmcset [-t user] -d emergencyuser -v username
        Args:
            username: 紧急用户的用户名
        """
        params = {"username": username}
        result = self.dispatcher.dispatch("cli_set_emergency_user", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, filepath=str)
    def cli_import_ssh_public_key_user(self, login_user_password, username, filepath):
        """4.9.11 为用户添加SSH公钥（addpublickey）
            命令功能: addpublickey命令为用户添加SSH公钥。
            命令格式: ipmcset -t user -d addpublickey -v username filepath|file URL
        Args:
            username: 待导入SSH公钥的用户名
            filepath: 待导入的保存于本地的SSH公钥文件路径
            file URL: 待导入的远程SSH公钥文件的URL
        """
        params = {
            "login_user_password": login_user_password,
            "username": username,
            "filepath": filepath,
        }
        result = self.dispatcher.dispatch("cli_import_ssh_public_key_user", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str)
    def cli_delete_ssh_public_key_user(self, login_user_password, username):
        """4.9.12 删除用户的SSH公钥（delpublickey）
            命令功能: delpublickey命令为用户删除SSH公钥。
            命令格式: ipmcset -t user -d delpublickey -v username
        Args:
            username: 待删除SSH公钥的用户的用户名
        """
        params = {"login_user_password": login_user_password, "username": username}
        result = self.dispatcher.dispatch("cli_delete_ssh_public_key_user", params=params)[0]["parser"]
        return result

    @validate_param(method=str, value=str)
    def cli_query_set_ssh_user_authentication_status(self, method, value=None):
        """4.9.13 查询和设置SSH用户密码认证使能状态（sshpasswordauthentication）
            命令功能: sshpasswordauthentication命令用于查询和设置SSH用户密码认证功能的使能状态。
            命令格式: ipmcget -t user -d sshpasswordauthentication
                    ipmcset -t user -d sshpasswordauthentication -v<enabled | disabled>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            value:
                enabled: 使能SSH用户密码认证功能
                disabled: 禁止SSH用户密码认证功能
        """
        params = {"method": method, "value": value}
        result = self.dispatcher.dispatch("cli_query_set_ssh_user_authentication_status", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, option=str, value=str)
    def cli_set_user_interfaces_log_ibmc(self, login_user_password, username, option, value):
        """4.9.14 设置用户登录iBMC的接口类型（interface）
            命令功能: interface命令用于设置指定用户登录iBMC的接口类型。
            命令格式: ipmcset -t user -d interface -v username <enabled | disabled> <option1 option2 ... optionN>
        Args:
            username: 待配置的用户
            option:
                enabled: 使能指定的接口类型
                disabled: 禁止指定的接口类型
            option1 option2 ... optionN: 可设置的接口类型
        """
        params = {
            "login_user_password": "login_user_password",
            "username": "username",
            "option": "option",
            "value": "value",
        }
        result = self.dispatcher.dispatch("cli_set_user_interfaces_log_ibmc", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_weak_password_check_state(self, value):
        """4.9.15 设置弱口令字典认证使能状态（weakpwddic）
            命令功能: weakpwddic命令用于设置弱口令字典认证功能的使能状态。
                    出现在弱口令字典中的字符串不能被设置为
            命令格式: ipmcset -t user -d weakpwddic -v <enabled | disabled>
        Args:
            value:
                enabled: 使能弱口令字典认证功能
                disabled: 禁止弱口令字典认证功能
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_weak_password_check_state", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_export_weak_password_dictionary(self, value):
        """4.9.16 导出弱口令字典（weakpwddic -v export）
            命令功能: weakpwddic -v export命令用于导出iBMC的弱口令字典。
            命令格式: ipmcset -t user -d weakpwddic -v export <filepath | file_URL>
        Args:
            value:
                filepath: 将弱口令字典导出到iBMC文件系统时，在iBMC件系统中的存放路径。
                file_URL: 将弱口令字典导出到远程设备时，在远程设备上的存放路径。
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_export_weak_password_dictionary", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_import_weak_password_dictionary(self, value):
        """4.9.17 导入弱口令字典（weakpwddic -v import）
            命令功能: weakpwddic -v import命令用于导入iBMC的弱口令字典。
            命令格式: ipmcset -t user -d weakpwddic -v import <filepath | file_URL>
        Args:
            value:
                filepath: 将弱口令字典导入iBMC时，待导入的文件在iBMC文件系统中的存放路径。
                file_URL: 将弱口令字典导入iBMC时，待导入的文件在远程设备上的存放路径。
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_import_weak_password_dictionary", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, password=str)
    def cli_set_snmpv3_user_encryption_password(self, login_user_password, username, password):
        """4.9.18 设置SNMPv3用户的加密密码（snmpprivacypassword）
            命令功能: snmpprivacypassword命令用于设置指定用户使用SNMPv3连接iBMC的数据加密密码。
            命令格式: ipmcset -t user -d snmpprivacypassword -v username
        Args:
            username: 待配置的用户
        """
        params = {
            "login_user_password": login_user_password,
            "username": username,
            "password": password,
        }
        result = self.dispatcher.dispatch("cli_set_snmpv3_user_encryption_password", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_user_inactive_period(self, method, value=None):
        """4.9.19 查询和设置用户不活动期限（securityenhance -d inactivetimelimit）
            命令功能: securityenhance -d inactivetimelimit命令用于设置用户不活动期限。
                     超过设定期限内未活动的用户会被禁用。
            命令格式: ipmcset -t securityenhance -d inactivetimelimit -v <value>
                    ipmcget -t securityenhance -d inactivetimelimit
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            value: 表示不活动期限
        """
        params = {"method": method, "value": value}
        result = self.dispatcher.dispatch("cli_query_set_user_inactive_period", params=params)[0]["parser"]
        return result

    @validate_param(login_user_password=str, username=str, value=str)
    def cli_set_user_status(self, login_user_password, username, value):
        """4.9.20 设置用户启用状态（user -d state）
            命令功能: user -d state命令用于设置用户的启用状态。
            命令格式: ipmcset -t user -d state -v <username> [enabled | disabled]
                    ipmcget -d userlist
        Args:
            username: 表示待设置的用户
            enabled: 表示启用该用户
            disabled: 表示禁用该用户
        """
        params = {
            "login_user_password": login_user_password,
            "username": username,
            "value": value,
        }
        result = self.dispatcher.dispatch("cli_set_user_status", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_band_user_management_status(self, method, value=None):
        """4.9.21 查询和设置带内用户管理使能状态（user -d usermgmtbyhost）
            命令功能: user -d usermgmtbyhost命令用于查询和设置带内用户管理功能的使能状态。
            命令格式: ipmcset -t user -d usermgmtbyhost -v <option>
                    ipmcget -t user -d usermgmtbyhost
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            <option>: 表示待设置的带内用户管理使能状态
        """
        params = {"method": method, "value": value}
        result = self.dispatcher.dispatch("cli_query_set_band_user_management_status", params)[0]["parser"]
        return result
