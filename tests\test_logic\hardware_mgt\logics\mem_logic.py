"""
Description: Memery LOGIC

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2025/07/31 16:33 created

"""
import copy
import re

from hkautos import log

from tests.test_logic.alias import HardwareMgt
from tests.test_logic.hardware_mgt.components.mem_mgt import MemMgt
from tests.test_logic.logic import Logic
from tests.test_data import common_fun as CommonFun

class MemLogic(Logic):
    alias = HardwareMgt.Mem

    def __init__(self, owning_device):
        super(MemLogic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.MemComponent = MemMgt(owning_device)

    def check_mem_info(self, hardware_info, mode="web"):
        """通过web/redfish等接口读取Memory信息，检查信息是否正确
        Args:
            hardware_info：硬件配置表
            mode:   接口模式，可选”web“、”redfish“、”dmidecode“缺省值为“web”
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        if mode == "web":
            mems_info = self.MemComponent.web.web_get_mem_info()
            lut = self.MemComponent.web.web_get_system_lut()["memory_info"]["memory_info"]
        elif mode == "redfish":
            mems_info = self.MemComponent.redfish.get_memory()
        elif mode == "dmidecode":
            mems_info = []
            res = self.MemComponent.os_host.get_dmidecode(code='17')['Memory Device']
            for mem_info in res:
                if mem_info['Size'] != 'No Module Installed':
                    mems_info.append(mem_info)
        else:
            self.logger.error(f"参数mode当前只支持”web“、”redfish“，暂不支持{mode}")
            return False
        self.logger.info(f"{mode}获取的信息: {mems_info}")
        mems_hardware_info = copy.deepcopy(hardware_info['Memory'])

        if len(mems_hardware_info) != len(mems_info):
            self.logger.error(f"【Memory数量不正确】：\n{mode}获取的数量: {len(mems_info)}\n硬件配置文件数量: "
                              f"{len(mems_hardware_info)}")
            return False

        for mem_info in mems_info:
            if mode == "redfish":
                mem_info = CommonFun.flatten_dict(mem_info)
                mem_info = CommonFun.convert_values_to_str(mem_info)
                mem_info.update({"cfg_speed": mem_info['OperatingSpeedMhz'] + " MT/s",
                                 "max_speed": mem_info['AllowedSpeedsMHz'][0] + " MT/s",
                                 "capacity": mem_info['CapacityMiB'] + " MB",
                                 "ranks": mem_info['RankCount'] + " rank",
                                 "bit_width": mem_info['DataWidthBits'] + " bit"})
                del mem_info['AllowedSpeedsMHz']
                if mem_info['State'] != 'Enabled' or mem_info['Health'] != 'OK':
                    self.logger.error(f"Memory状态异常，State：{mem_info['State']}， Health：{mem_info['Health']}")
                    return False
            if mode == "dmidecode":
                mem_info.update({"capacity": str(int(mem_info['Size'].split()[0]) * 1024) + " MB",
                                 "bit_width": mem_info['Total Width'][:-1],
                                 "ranks": mem_info['Rank'] + " rank",
                                 'Part Number': mem_info['Part Number'].strip()})
            for mem_hw_info in mems_hardware_info:
                if set(mem_hw_info.values()) & set(mem_info.values()) == set(mem_hw_info.values()):
                    self.logger.info(f"【信息一致】：\n{mode}获取的值：{mem_info}\n硬件配置文件信息：{mem_hw_info}")
                    # 删除配置表中已经匹配到元素，防止反复对同一个元素进行匹配
                    mems_hardware_info.remove(mem_hw_info)
                    break

                # hardware_info配置文件中最后一个元素都没有匹配到，匹配失败
                if mem_hw_info == mems_hardware_info[-1]:
                    self.logger.error(f"【信息不一致】：\n{mode}获取的值: {mem_info}\n硬件配置文件信息: {hardware_info['Memory']}")
                    return False
        return True
    