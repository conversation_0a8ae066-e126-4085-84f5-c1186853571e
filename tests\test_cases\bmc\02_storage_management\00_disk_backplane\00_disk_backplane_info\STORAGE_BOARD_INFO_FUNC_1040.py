
"""
功   能：硬盘背板温度传感器测试

修改信息：
    日期：   2025/07/18
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""
import time

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from tests.test_logic.alias import HardwareMgt, BMCMgt
from tests.test_data.base import DictListQuery

class STORAGE_BOARD_INFO_FUNC_1040(Case):
    """
    CaseId:
        STORAGE_BOARD_INFO_FUNC_1040
    RunLevel:
        1(#1)
    CaseName:
        硬盘背板温度传感器测试
    PreCondition:
        1、环境正常
    TestStep:
        1._正常上电状态web查询硬盘背板温度
        2._带内ipmi查询硬盘背板温度
        3._系统下电后查询硬盘背板温度
    ExpectedResult:
        1._存在硬盘背板传感器Disk_BP1_Temp_(℃)，温度正常，状态为OK
        2._带内查询结果与web一致
        3._系统下电后，硬盘背板温度为--
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.redfish_api = self.device_dut.get_api(ns_name="Redfish")
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.host_obj = self.device_dut.get_host(host_type=HostType.HostOS)
        self.web_api = self.device_dut.get_api(ns_name="Web")

        self.boards_mgt = self.device_dut.find(HardwareMgt.Boards)
        self.sensor_mgt = self.device_dut.find(BMCMgt.Sensor)

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }

        self.logger.step("1. 正常上电状态web查询硬盘背板温度")
        self.web_handle = self.web_api.login(self.web_login_params)
        res = self.sensor_mgt.check_sensor_temp("DISK BP TEMP")
        self.assertTrue(res, "存在硬盘背板传感器Disk_BP1_Temp_(℃)，温度正常，状态为OK")

        self.logger.step("2. 带内ipmi查询硬盘背板温度")
        res = self.sensor_mgt.check_sensor_temp("Disk BP1 Temp", "ipmi")
        self.assertTrue(res, "带内查询结果与web一致")

        self.logger.step("3. 系统下电后查询硬盘背板温度")
        self.redfish_api.reset_system({"ResetType": "ForceOff"})
        self.host_obj.wait_for_shutdown(timeout=120)

        time.sleep(30)
        sensor_info = self.web_api.web_get_sensor_info()
        sensor_obj = DictListQuery(sensor_info["info"])
        sensor_temps = sensor_obj.fuzzy_query("sensor name", 'DISK BP TEMP')
        self.assertEqual(sensor_temps[0].get('value'), "--", "系统下电后，硬盘背板温度为--")

    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.redfish_api.reset_system({"ResetType": "On"})
        self.host_obj.wait_for_reboot(wait_for_shutdown=False)
        self.web_api.logout(self.web_handle)
