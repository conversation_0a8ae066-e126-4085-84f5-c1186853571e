import logging
from dataclasses import dataclass
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


# 注意：此配置类已废弃，请使用 HKTestConfig
# 为保持向后兼容暂时保留
@dataclass
class EngineConfig:
    """已废弃：请使用 HKTestConfig

    此类将在后续版本中移除，请迁移到 HKTestConfig
    """

    def __init__(self, *args, **kwargs):
        logger.warning("EngineConfig 已废弃，请使用 HKTestConfig。此类将在后续版本中移除。")

    def _load_test_cases(self) -> List[Dict[str, Any]]:
        """加载测试用例配置"""
        test_cases = []
        for case in self._config.get("test_cases", []):
            # 确保每个用例都有基本信息
            if isinstance(case, str):
                case = {"name": case, "id": case}
            elif isinstance(case, dict) and not case.get("id"):
                case["id"] = case.get("name", f"test_{len(test_cases)}")
            test_cases.append(case)
        return test_cases
