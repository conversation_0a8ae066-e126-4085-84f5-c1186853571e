#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish tmu管理 API

提供tmu管理相关功能:

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Tmu(ApiBase):
    """tmu管理 API"""

    def get_sensor_value(self, sensor) -> Dict[str, Any]:
        """
        获取tmu的传感器信息

        URL: /redfish/v1/Chassis/chassis/Sensors/{sensor}
        请求方式: GET

        返回:
            账户服务信息
        """
        return self.dispatcher.dispatch("redfish_get_tmu_sensor", sensor)

    def get_pump_pid_value(self) -> Dict[str, Any]:
        """
        获取tmu的泵pid信息

        URL: /redfish/v1/Hkzy/SystemParams
        请求方式: GET

        返回:
            账户服务信息
        """
        return self.dispatcher.dispatch("redfish_get_pump_pid_value")
