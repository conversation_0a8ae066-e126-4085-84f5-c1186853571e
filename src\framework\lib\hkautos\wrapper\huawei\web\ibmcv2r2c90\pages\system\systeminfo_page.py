import os, time
import re
import json
from typing import Any, Dict, List

from hkautos import log
from hkautos.wrapper.uniwebs.pagebase import PageBase
from tests.test_data import common_fun as CommonFun

class SystemInfoPage(PageBase):
    LocatorFile = os.path.join("system", "system_info.json")

    def __init__(self, params):
        super(SystemInfoPage, self).__init__(params)
        self.logger = log.get_logger(__name__)
        self.browser_language = self.WebConnection.page.evaluate("() => navigator.language")
        self.web_init_elements_dict(self.LocatorFile)

    def web_get_system_lut(self) -> Dict[str, Any]:
        """
        获取web system页面的关键字对照表（Look-Up Table）
        """
        current_file_path = os.path.abspath(__file__)
        root_path = os.path.dirname(os.path.dirname(os.path.dirname(current_file_path)))
        lut_path = os.path.join(root_path, "elements", "system", "system_keywords_lut.json")
        with open(lut_path, "rb") as file:
            contents = file.read()
            lut = json.loads(contents)
        if self.browser_language.lower().startswith('en'):
            index = 0
        elif self.browser_language.lower().startswith('zh'):
            index = 1
        lut = CommonFun.replace_list_with_index_element(lut, index)
        return lut

    def web_get_cpu_info(self) -> List[Dict[str, Any]]:
        """
        获取cpu信息
        Args:
            无:
        Returns:
            result: List[Dict[str, Any]]，CPU信息
        """
        self.switch_page("processor_page")
        self.WebConnection.wait_for_selector(self.locator_dict['processor_link'])
        count = self.WebConnection.get_table_rows(self.locator_dict['processor_link'])
        result = []
        for x in range(count):
            result_dict = {}
            self.WebConnection.click(f"{self.locator_dict['processor_link']}[{x + 1}]/td[1]/ti-details-icon")

            rows = self.WebConnection.get_all_selector(f'//*[@id="CPU{x + 1}Detail"]/table/tbody/tr')
            for row in rows:
                items = self.clean_and_parse(row.inner_text())
                result_dict.update(dict(zip(items[::2], items[1::2])))

            result.append(result_dict)
            self.WebConnection.click(f"{self.locator_dict['processor_link']}[{x + 1}]/td[1]/ti-details-icon")

        return result

    def web_get_mem_info(self):
        """
        获取内存信息
        Args:
            params (dict):

        Returns:
            成功返回：True 失败 raise
        """
        self.switch_page("mem_page")

        result = []
        self.WebConnection.wait_for_selector(self.locator_dict['mem_link'])
        count = self.WebConnection.get_table_rows(self.locator_dict['mem_link'])

        for x in range(count):
            result_dict = {}
            self.WebConnection.click(f"{self.locator_dict['mem_link']}[{x + 1}]/td[1]")

            # 遍历下面的div
            divs = self.WebConnection.get_all_selector(f'//*[@id="detailInfo{x}"]/div')
            for div in divs:
                spans = div.locator('span').all()

                key = spans[0].text_content().strip()
                value = spans[1].text_content().strip()
                result_dict[key] = value

            result.append(result_dict)
            self.WebConnection.click(f"{self.locator_dict['mem_link']}[{x + 1}]/td[1]")

        return result

    def web_get_board_info(self):
        """
        获取单板信息
        Args:
        Returns:
        """
        self.switch_page("system_page")
        board_info = dict()
        board_info["ProductName"] = self.WebConnection.get_inner_text(self.locator_dict["product_name"]).strip()
        board_info["Manufacturer"] = self.WebConnection.get_inner_text(self.locator_dict["manufacturer"]).strip()
        return board_info

    def web_get_sensor_info(self) -> Dict[str, Any]:
        """
        通过WEB获取Sensor信息
        Args:
            无:
        Returns:
            result: List[Dict[str, Any]]，sensor信息，如下：
            {'total': 104,
            'info': [{'index': '1', 'sensor name': '1711 Core Temp ', 'unit': '℃', 'value': '46',
                    'status': 'OK', 'lnr': '--', 'lc': '--', 'lnc': '--', 'unc': '105', 'uc': '--', 'unr': '--'},
                    {'index': '2', 'sensor name': '5902L_Temp2 ', 'unit': '℃', 'value': '34',
                    'status': 'OK', 'lnr': '--', 'lc': '--', 'lnc': '--', 'unc': '105', 'uc': '--', 'unr': '--'},
                    .....
                    ]
            }
        """
        result = {"total": "", "info": []}
        keys_list = [
            "index",
            "sensor name",
            "unit",
            "value",
            "status",
            "lnr",
            "lc",
            "lnc",
            "unc",
            "uc",
            "unr",
        ]
        self.switch_page("sensor_page")
        # 针对脚本中一次登录多次查询的情况，需要reload刷新页面，确保每次从sensor_page的第一页开始获取
        self.WebConnection.reload()
        self.WebConnection.wait_for_selector(f'//*[@id="sensorPagination_total_items"]')
        time.sleep(1)
        ret = self.WebConnection.get_inner_text(f'//*[@id="sensorPagination_total_items"]')
        total = int(ret.split(":")[1])
        result["total"] = total

        count_per_page = self.WebConnection.get_inner_text(f'//*[@id="sensorPagination"]//section/section')
        i = 0
        while i < total/int(count_per_page):
            rows = self.WebConnection.get_table_rows(f'//*[@id="sensorTable"]//tbody/tr')
            for row in range(rows):
                ret = self.WebConnection.get_all_selector(f'//*[@id="sensorTable"]//tbody/tr')[row].inner_text()
                items_temp = [x for x in re.split(r'[\t\n()]', ret) if x]
                items = [s.strip() for s in items_temp if s.strip()]
                result_dict = {}
                result_dict.update(dict(zip(keys_list, items)))
                result["info"].append(result_dict)
            i = i + 1
            self.WebConnection.click(f'//*[@id="sensorPagination_next_page"]')
        return result

    def web_get_system_others_info(self, model) -> List[Dict[str, Any]]:
        """
        通过WEB获取”系统管理“->”系统信息“->”其它“信息
        Args:
            model:  模块名称，取值范围: index.keys()
        Returns:
            result: List[Dict[str, Any]]，组件信息
        """
        result = []
        index = {"BBU": {"link": "others_bbumodule_link", "table_id": "bbuTable"},
                 "CPU_Board": {"link": "others_cpuboard_link", "table_id": "cpuTable"},
                 "Drive_Backplanes": {"link": "others_diskbackplane_link", "table_id": "diskBackPlaneTable"},
                 "Expander": {"link": "others_expandboard_link", "table_id": "expandtable"},
                 "Fan_BackPlane": {"link": "others_fanbackplane_link", "table_id": "fanBackPlaneTable"},
                 "M.2_Adapter": {"link": "others_m2transformcard_link", "table_id": "m2TransformTable"},
                 "OCP_Cards": {"link": "others_ocpcard_link", "table_id": "ocpTable"},
                 "Riser_Cards": {"link": "others_pcierisercard_link", "table_id": "pCIERiserTable"},
                 "Security_Cards": {"link": "others_securitymodule_link", "table_id": "securityTable"},
                 "Switch_Boards": {"link": "others_switchboard_link", "table_id": "switchtable"}
                 }
        if model not in index.keys():
            self.logger.error(f"Web获取的值与硬件配置文件不匹配，Web获取的信息: {index.keys()}")
            return result

        self.switch_page("others_page")
        self.WebConnection.wait_for_selector(self.locator_dict[index[model]["link"]], timeout=10000)
        self.WebConnection.click(self.locator_dict[index[model]["link"]])
        self.WebConnection.wait_for_selector(f'//*[@id="{index[model]["table_id"]}"]/div/table/thead/tr')

        title = self.WebConnection.get_inner_text(f'//*[@id="{index[model]["table_id"]}"]/div/table/thead/tr')
        keys = title.split('\t')

        rows = self.WebConnection.get_table_rows(f'//*[@id="tableBody"]/tr')
        for row in range(rows):
            items = self.WebConnection.get_all_selector(f'//*[@id="tableBody"]/tr')[row].inner_text()
            result_dict = {}
            result_dict.update(dict(zip(keys, items.split("\t"))))
            result.append(result_dict)
        return result

    @staticmethod
    def clean_and_parse(text):
        """清洗并解析单条文本记录"""
        return [item.strip()
                for item in text.replace('\t', '').split('\n')
                if item.strip()]
