"""
Description: 4.16 SOL命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Sol(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Sol, self).__init__()
        self.name = "sol"

    @validate_param(option=str, mode=str)
    def cli_creat_sol_session(self, option, mode):
        """4.16.1 建立SOL会话（sol -d activate）
            命令功能: sol -d activate命令用于建立SOL会话连接系统或iBMC串口。
            命令格式: ipmcset -t sol -d activate -v <option> <mode>
        Args:
            option: 表示要连接的串口，系统串口或iBMC串口。
            mode: 表示SOL会话模式。
        """
        params = {"option": option, "mode": mode}
        result = self.dispatcher.dispatch("cli_creat_sol_session", params=params)[0]["parser"]
        return result

    @validate_param(index=str)
    def cli_deactivat_sol_session(self, index):
        """4.16.2 注销SOL会话（sol -d deactivate）
            命令功能: sol -d deactivate命令用于强制注销SOL会话。
            命令格式: ipmcset -t sol -d deactivate -v <index>
        Args:
            index: 表示SOL会话序号。
        """
        params = {"index": index}
        result = self.dispatcher.dispatch("cli_deactivat_sol_session", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_set_sol_session_timeout_period(self, value):
        """4.16.3 设置SOL会话超时时间（sol -d timeout）
            命令功能: sol -d timeout命令用于设置SOL会话超时时间。设置超时时间后，用户在SOL会话中无输入并达到超时时间后，
                    SOL会话将退出并返回iBMC命令行界面。
            命令格式: ipmcset -t sol -d timeout -v <value>
        Args:
            value: 表示SOL会话用户无输入时，退出SOL会话的时间。
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_sol_session_timeout_period", params=params)[0]["parser"]
        return result

    def cli_query_sol_session_list(self):
        """4.16.4 查询SOL会话列表（sol -d session）
        命令功能: sol -d session命令用于查询SOL会话列表。
        命令格式: ipmcget -t sol -d session
        """
        result = self.dispatcher.dispatch("cli_query_sol_session_list")[0]["parser"]
        return result

    def cli_query_sol_session_configuration(self):
        """4.16.5 查询SOL会话配置信息（sol -d info）
        命令功能: sol -d info命令用于查询SOL会话配置信息，如查询SOL会话超时时间。
        命令格式: ipmcget -t sol -d info
        """
        result = self.dispatcher.dispatch("cli_query_sol_session_configuration")[0]["parser"]
        return result
