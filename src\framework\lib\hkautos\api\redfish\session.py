#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 会话管理 API

提供会话管理相关功能:
- 会话服务配置
- 会话创建销毁
- 会话状态监控
- 会话权限控制

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Session(ApiBase):
    """会话管理 API"""

    def get_session_service(self) -> Dict[str, Any]:
        """获取会话服务配置"""
        return self.dispatcher.dispatch("redfish_get_session_service")

    def set_session_service(self, params: Dict[str, Any]) -> None:
        """
        设置会话服务配置

        参数:
            params: 参数字典，常用字段:
                - SessionTimeout: 会话超时时间(秒)
                - ServiceEnabled: 是否启用会话服务
        """
        return self.dispatcher.dispatch("redfish_set_session_service", params=params)

    def get_sessions(self) -> List[Dict[str, Any]]:
        """获取会话列表"""
        return self.dispatcher.dispatch("redfish_get_sessions")

    def create_session(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建会话

        参数:
            params: 参数字典，常用字段:
                - UserName: 用户名
                - Password: 密码
        """
        return self.dispatcher.dispatch("redfish_create_session", params=params)

    def delete_session(self, params: Dict[str, Any]) -> None:
        """
        删除会话

        参数:
            params: 参数字典，常用字段:
                - session_id: 会话ID
        """
        return self.dispatcher.dispatch("redfish_delete_session", params=params)

    def get_session(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取会话信息

        URL: /redfish/v1/SessionService/Sessions/{session_id}
        请求方式: GET

        参数:
            params: 参数字典，常用字段:
                - session_id: 会话ID

        返回:
            会话详细信息
        """
        return self.dispatcher.dispatch("redfish_get_session", params=params)

    def redfish_login(self, params: Dict[str, Any]):
        """
        登录redfish
        """
        return self.dispatcher.dispatch("redfish_login", params=params)
