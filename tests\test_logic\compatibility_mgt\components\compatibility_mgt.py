"""
Description: Settings-Upgrade

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 17:15

"""
from time import sleep
from typing import Any
from hkautos import log
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException
from tests.test_logic.component import Component
from tests.utils.decorator_utils import dict_judge_decorator


class CompatibilityMgt(Component):
    def __init__(self, owning_device: Any) -> None:
        """初始化Redfish组件

        Args:
            device: 设备实例
        """
        super().__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.redfish_api = self.owning_device.get_api(ns_name="Redfish")
        self.ipmi_api = self.owning_device.get_api(ns_name="Ipmi")
        self.web = self.owning_device.get_api(ns_name="Web")
        self.bmc_host = self.owning_device.get_host(HostType.BMC)
        self.os_host = self.owning_device.get_host(HostType.HostOS)

    def web_get_cpu_info(self):
        """
        获取处理器信息
        Args:
        Returns:
            各接口的返回值
        """
        return self.web.web_get_cpu_info()
