"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class UserDevice(ApiBase):
    def __init__(self):
        super(UserDevice, self).__init__()

    def get_user_list(self, params=None):
        """
        获取用户列表

        Args:
            params: 参数字典
                   例如: {'channel': 1}

        Returns:
            dict: 返回解析后的用户列表信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_list", params=params)[0]["parser"]

    def set_user_name(self, params=None):
        """
        设置用户名

        Args:
            params: 参数字典
                   例如: {
                       'userid': 2,
                       'username': 'admin'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_set_name", params=params)[0]["parser"]

    def set_user_password(self, params=None):
        """
        设置用户密码

        Args:
            params: 参数字典
                   例如: {
                       'userid': 2,
                       'password': 'password123'
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_set_password", params=params)[0]["parser"]

    def set_user_privilege(self, params=None):
        """
        设置用户权限

        Args:
            params: 参数字典
                   例如: {
                       'userid': 2,
                       'privilege': '4',
                       'channel': 1
                   }

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_priv", params=params)[0]["parser"]

    def enable_user(self, params=None):
        """
        启用用户

        Args:
            params: 参数字典
                   例如: {'userid': 2}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_enable", params=params)[0]["parser"]

    def disable_user(self, params=None):
        """
        禁用用户

        Args:
            params: 参数字典
                   例如: {'userid': 2}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_disable", params=params)[0]["parser"]

    def test_user(self, params=None):
        """
        测试用户
        Args:
            params: 参数字典
                   例如: {'userid': 2}
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_user_test_cmd", params=params)[0]["parser"]
