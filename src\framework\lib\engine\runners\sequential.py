import logging
from typing import Any, Dict, List

from .base import BaseRunner

logger = logging.getLogger(__name__)


class SequentialRunner(BaseRunner):
    """串行测试运行器"""

    def run(self, test_paths: List[str]) -> List[Dict[str, Any]]:
        """串行执行测试用例"""
        results = []
        total = len(test_paths)

        for index, path in enumerate(test_paths, 1):
            logger.info(f"执行测试 [{index}/{total}]: {path}")

            try:
                result = self._handle_retry(path, retry_count=self.config.retry_count)
                parsed_result = self.adapter.parse_results(result["raw_result"])
                results.append({"path": path, **parsed_result})

                logger.info(f"测试完成: {path} - {parsed_result['status']}")

            except Exception as e:
                logger.error(f"测试执行失败: {path} - {str(e)}")
                results.append({"path": path, "status": "error", "error": str(e)})

        return results
