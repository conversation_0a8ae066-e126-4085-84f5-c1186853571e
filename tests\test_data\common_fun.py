import os
import re
from typing import Dict, Any

def get_all_data(dst_dir, data_type):
    """获取所有目录下的xml文件

    :param dst_dir:
    :return:
    """
    return get_files(dst_dir, file_pattern=".*\\.xml")


def get_files(dir_path, file_pattern=None, exclude_folders=None, root_dir=None):
    """Get files under given folder path that match given file pattern, exclude dirs by given

    :param dir_path: Dir path
    :param file_pattern: File pattern. Regular expressions. None by default
    :param exclude_folders: Folders name in list. [] by default
    :param root_dir: 文件的相对路径，相对于dir_path, eg: dir_path: /root, root_dir: test/001,
    组装后的start_path: /root/test/001.
    :return: Full file paths list
    """

    def __fileter_files(file_name, pattern):
        if file_name:
            if re.search(pattern, file_name, re.IGNORECASE):
                return os.path.join(_root, file_name)
        else:
            return os.path.join(_root, file_name)

    match_files = []
    if not exclude_folders:
        exclude_folders = list()
    for _root, sub_folders, files in os.walk(dir_path):
        for folder in exclude_folders:
            if folder in sub_folders:
                sub_folders.remove(folder)
        for f in files:
            config_path = __fileter_files(f, file_pattern)
            if config_path:
                match_files.append(config_path)
    if not root_dir:
        return match_files
    start_dir = os.path.join(dir_path, root_dir)
    for _f in match_files[::-1]:
        try:
            if not _f.startswith(start_dir):
                match_files.remove(_f)
        except UnicodeDecodeError:
            match_files.remove(_f)
            continue
    return match_files

def flatten_dict(input_dict: Dict[str, Any]):
    """
    递归地将嵌套字典扁平化，提取所有叶子节点的键值对到顶层。

    参数:
        input_dict (dict): 输入的嵌套字典

    返回:
        dict: 扁平化后的字典
    """
    result = {}
    for key, value in input_dict.items():
        if isinstance(value, dict):
            # 如果值是字典，递归扁平化子字典
            flattened_subdict = flatten_dict(value)
            # 将子字典的键值对合并到当前结果
            for k, v in flattened_subdict.items():
                result[k] = v
        else:
            # 如果值不是字典，直接添加到结果
            result[key] = value
    return result

def convert_values_to_str(obj):
    """
    将字典的value转化为字符串，方便对比
    Args:
        obj（dict）
    Returns:
        obj（dict）
    """
    if isinstance(obj, dict):
        return {k: convert_values_to_str(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_values_to_str(item) for item in obj]
    else:
        return str(obj)

def replace_list_with_index_element(data, index):
    """
    递归处理嵌套字典，将末节点的列表值替换为列表的第index个元素
    参数:
        data: 原始数据（字典或列表）
        index: 替换元素索引
    返回:
        处理后的数据
    """
    if isinstance(data, dict):
        # 字典类型：递归处理每个值
        return {key: replace_list_with_index_element(value, index) for key, value in data.items()}

    elif isinstance(data, list):
        # 列表类型：如果是末节点列表，返回第一个元素；否则递归处理
        if all(not isinstance(item, (dict, list)) for item in data):
            return data[index] if data else None  # 非空列表返回第一个元素，空列表返回None
        else:
            return [replace_list_with_index_element(item, index) for item in data]

    else:
        # 基础类型（字符串、数字等）：直接返回
        return data
