"""
Description: Snmp API模块初始化

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from pathlib import Path

from hkautos.api.namespace import NameSpace
from hkautos.utils.system_utils import import_sub_dir

# 创建Snmp命名空间
snmp_ns = NameSpace("Snmp")


def init_module(api):
    # 获取当前目录路径
    base_dir = Path(__file__).parent
    # 导入子目录模块
    import_sub_dir(base_dir)
    # 注册Snmp命名空间
    api.add_namespace(snmp_ns)
