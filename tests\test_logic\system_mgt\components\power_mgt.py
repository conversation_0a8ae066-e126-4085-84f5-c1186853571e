"""
Description: POWER MGT

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/2/28 17:15

"""

from hkautos import log
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException

from tests.test_logic.component import Component
from tests.utils.decorator_utils import dict_judge_decorator


class PowerMgt(Component):
    def __init__(self, owning_device):
        super(PowerMgt, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.bmc_host = self.owning_device.get_host(HostType.BMC)
        self.os_host = self.owning_device.get_host(HostType.HostOS)
        self.host_os = self.owning_device.get_host("HostOS", host_id="1")
        self.host_os_object = self.owning_device.get_host(host_type="HostOS", host_id="1")
        self.redfish_api = self.owning_device.get_api(ns_name="Redfish")
        self.ipmi_api = self.owning_device.get_api(ns_name="Ipmi")
        self.web_api = self.owning_device.get_api(ns_name="Web")

    def set_power_state(self, mode="ipmi", host="local", type="raw", params=None):
        """设置上电和下电
        操作类型：SET
        Args:
        mode取ipmi:
            state(str): on/off/reset/cycle/softa
            host_type(str):
                    local:带外发送ipmi命令
                    hostos:带内发送ipmi命令
        """
        test_dict = {"mode": mode}
        args_judge = dict_judge_decorator(test_dict)

        @args_judge(mode="ipmi")
        def set_power_state_info():
            try:
                self.ipmi_api = self.getHostObj(mode=host)
                if type == "raw":
                    set_res = self.ipmi_api.set_chassis_power_cycle_raw(params)
                set_res = self.ipmi_api.set_chassis_power(params)
                return set_res
            except Exception as e:
                raise HKTestException("ipmi设置上/下电失败") from e

        @args_judge(mode="redfish")
        def set_power_state_info():
            response = self.redfish_api.reset_system(params)
            self.logger.info(response)
            if response.get("@Message.ExtendedInfo")[0].get("Message") == "Successfully Completed Request":
                return
            self.logger.warn("ipmi设置上/下电失败", response)
            raise HKTestException("ipmi设置上/下电失败")

        return set_power_state_info()

    def get_power_state(self, mode="ipmi", host_type="local"):
        """获取机箱电源状态
        操作类型：GET
        Args:
        mode取ipmi:
            state(str): on/off/reset/cycle/softa
            host_type(str):
                    local:带外发送ipmi命令
                    hostos:带内发送ipmi命令
        """
        test_dict = {"mode": mode}
        args_judge = dict_judge_decorator(test_dict)

        @args_judge(mode="ipmi")
        def get_power_state_info():
            if host_type == "hostos":
                self.ipmi_api.set_dispatcher(self.host_os)
            get_res = self.ipmi_api.get_chassis_status()
            return get_res

        return get_power_state_info()

    def set_power_policy_interface(self, mode="ipmi", host="local", type="raw", params=None):
        """
        设置电源策略
        Args:
            params: (dict)
        """
        test_dict = {"mode": mode}
        args_judge = dict_judge_decorator(test_dict)

        @args_judge(mode="ipmi")
        def set_power_policy(host=host, type=type, params=params):
            self.ipmi_api = self.getHostObj(mode=host)
            if type == "raw":
                return self.ipmi_api.set_chassis_status_raw(params)
            else:
                if params is None or params.get("Policy") is None:
                    return self.ipmi_api.set_chassis_policy()
                return self.ipmi_api.set_chassis_policy(params)

        return set_power_policy()

    def set_acpi_power_state_interface(self, cmd_mode="ipmi", host="local", params=None):
        """
        设置ACPI电源状态
        Args:
            params: (dict)
        """
        test_dict = {"cmd_mode": cmd_mode}
        args_judge = dict_judge_decorator(test_dict)

        @args_judge(cmd_mode="ipmi")
        def set_acpi_power_state(host=host, params=params):
            self.ipmi_api = self.getHostObj(mode=host)
            return self.ipmi_api.ipmi_set_acpi_power_state(params)

        return set_acpi_power_state()

    def get_acpi_power_state_interface(self, **kwargs):
        """
        获取ACPI电源状态
        Args:
        """
        ipmi_job = self.getHostObj(**kwargs)
        params = self.get_ipmi_params(**kwargs)
        return ipmi_job.get_acpi_power_state(params=params)
