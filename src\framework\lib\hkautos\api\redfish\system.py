#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 系统管理 API

提供系统管理相关功能:
- 系统资源配置
- 处理器管理
- 内存管理
- 存储管理
- 网络管理
- 电源管理
- 散热管理
- 启动管理

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class System(ApiBase):
    """系统管理 API"""

    def get_systems(self) -> List[Dict[str, Any]]:
        """获取系统列表"""
        return self.dispatcher.dispatch("redfish_get_systems")

    def get_system(self) -> Dict[str, Any]:
        """获取系统信息"""
        return self.dispatcher.dispatch("redfish_get_system")

    def reset_system(self, params: Dict[str, Any]) -> None:
        """
        重启系统

        参数:
            params: 参数字典，常用字段:
                - ResetType: 重置类型(On/ForceOff/GracefulShutdown/ForceRestart/Nmi等)
        """
        return self.dispatcher.dispatch("redfish_reset_system", params=params)

    def get_processors(self) -> List[Dict[str, Any]]:
        """获取处理器列表"""
        return self.dispatcher.dispatch("redfish_get_processors")

    def get_memory(self) -> List[Dict[str, Any]]:
        """获取内存列表"""
        return self.dispatcher.dispatch("redfish_get_memory")

    def get_storage(self) -> List[Dict[str, Any]]:
        """获取存储列表"""
        return self.dispatcher.dispatch("redfish_get_storage")

    def get_ethernet_interfaces(self) -> List[Dict[str, Any]]:
        """获取网络接口列表"""
        return self.dispatcher.dispatch("redfish_get_ethernet_interfaces")

    def get_power(self) -> Dict[str, Any]:
        """获取电源信息"""
        return self.dispatcher.dispatch("redfish_get_power")

    def get_thermal(self) -> Dict[str, Any]:
        """获取散热信息"""
        return self.dispatcher.dispatch("redfish_get_thermal")

    def get_boot(self) -> Dict[str, Any]:
        """获取启动配置"""
        return self.dispatcher.dispatch("redfish_get_boot")

    def set_boot(self, params: Dict[str, Any]) -> None:
        """
        设置启动配置

        参数:
            params: 参数字典，常用字段:
                - BootSourceOverrideEnabled: 启动覆盖使能(Once/Continuous/Disabled)
                - BootSourceOverrideTarget: 启动源(None/Pxe/Floppy/Cd/Usb/Hdd等)
                - BootSourceOverrideMode: 启动模式(UEFI/Legacy)
        """
        return self.dispatcher.dispatch("redfish_set_boot", params=params)

    def get_resource_info(self, params: Dict[str, Any]) -> None:
        """
        设置启动配置

        参数:
            params: 参数字典，常用字段:
                - BootSourceOverrideEnabled: 启动覆盖使能(Once/Continuous/Disabled)
                - BootSourceOverrideTarget: 启动源(None/Pxe/Floppy/Cd/Usb/Hdd等)
                - BootSourceOverrideMode: 启动模式(UEFI/Legacy)
        """
        return self.dispatcher.dispatch("get_resource", params=params)

    def redfish_systems_query_log_service_collection_information(self) -> Dict[str, Any]:
        """
        查询服务器当前日志服务集合资源信息。
        操作类型：GET
        URL：https://device_ip/redfish/v1/Systems/system_id/LogServices
        """
        return self.dispatcher.dispatch("redfish_systems_query_log_service_collection_information")

    def redfish_systems_query_log_service(self, logservices_id):
        """
            查询服务器当前日志服务资源信息。
            操作类型：GET
            URL：https://device_ip/redfish/v1/Systems/system_id/LogServices/logservices_id

        Args:
            logservices_id:日志服务ID
        """
        params = {"logservices_id": logservices_id}
        result = self.dispatcher.dispatch("redfish_systems_query_log_service", params=params)
        return result

    def redfish_mock_precise_alarm(self, params: Dict[str, Any]) -> None:
        """
            模拟精准告警
            操作类型：post
            URL：https://device_ip/redfish/v1/EventService/Actions/Oem/Huawei/EventService.MockPreciseAlarm

        Args:
            params: 需要包含以下信息：
                EventCode: 事件码
                SubjectIndex: 事件索引
                Type: 操作类型
        """
        result = self.dispatcher.dispatch("redfish_mock_alarm", params=params)
        return result

    def redfish_get_sel_log(self, params: Dict[str, Any]) -> None:
        """
            获取sel日志
            操作类型：post
            URL：https://device_ip/redfish/v1/Systems/system_id/LogServices/logservices_id/Actions/Oem/Huawei/LogService.QuerySelLogEntries

        Args:
            params: 需要包含以下信息：
                system_id: 系统ID
                logservices_id: 日志服务ID
        """
        result = self.dispatcher.dispatch("redfish_get_sel", params=params)
        return result

    def redfish_clear_sel_log(self, params: Dict[str, Any]) -> None:
        """
            清除sel日志
            操作类型：post
            URL：https://device_ip/redfish/v1/Systems/system_id/LogServices/log_services_id/Actions/LogService.ClearLog

        Args:
            params: 需要包含以下信息：
                system_id: 系统ID
                log_services_id: 日志服务ID
        """
        result = self.dispatcher.dispatch("redfish_clear_sel", params=params)
        return result

    def redfish_systems_query_system_id(self, system_id=None):
        """
            查询服务器指定系统资源信息，当前仅可查询服务器本身系统资源信息。
            注：查询BIOS配置信息时，查询的是BIOS配置项实时生效信息。BIOS配置信息查询受License控制。
            操作类型：GET
            URL：https://device_ip/redfish/v1/Systems/system_id

        Args:
            system_id: 系统资源的ID
        """
        params = {"system_id": system_id}
        result = self.dispatcher.dispatch("redfish_systems_query_system_id", params=params)
        return result

    def redfish_systems_query_system(self):
        """
        查询服务器当前系统集合资源的信息。
        操作类型：GET
        URL：https://device_ip/redfish/v1/Systems
        """
        result = self.dispatcher.dispatch("redfish_systems_query_system")
        return result

    def redfish_systems_get_processors_cpu_info(self, cpu_id):
        """
            查询服务器当前CPU详细信息。
            操作类型：GET
            cpu_id: CPU0-x, type:str
            URL：https://device_ip//redfish/v1/Systems/1/Processors/CPUX

        Args:
            logservices_id:日志服务ID
        """
        params = {"cpu_id": cpu_id}
        result = self.dispatcher.dispatch("redfish_get_processors_details", params=params)
        return result
