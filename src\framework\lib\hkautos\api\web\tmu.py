#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Web tmu API

提供登录相关功能:
- 用户登录
- 登录状态检查
- 登出操作
"""

from typing import Any, Dict

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import web_ns


@web_ns.dispatchertype(HostType.BMC)
class Tmu(ApiBase):
    """登录管理 API"""

    def tmu_get_run_mode(self):
        """
        获取tmu运行模式
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("tmu_web_get_run_mode")

    def tmu_set_run_mode(self, params):
        """
        设置tmu运行模式
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        self.dispatcher.dispatch("tmu_web_set_run_mode", params)
