"""
Description: Cpu Managerment

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 16:33 created

"""
import copy
import re

from hkautos import log

from tests.test_logic.alias import HardwareMgt
from tests.test_logic.hardware_mgt.components.cpu_mgt import CpuMgt
from tests.test_logic.logic import Logic
from tests.test_data import common_fun as CommonFun

class CpuLogic(Logic):
    alias = HardwareMgt.Cpu

    def __init__(self, owning_device):
        super(CpuLogic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.CpuComponent = CpuMgt(owning_device)

    def check_cpu_info(self, hardware_info, mode="web"):
        """通过web/redfish等接口读取CPU信息，检查信息是否正确
        Args:
            hardware_info：硬件配置表
            mode:   接口模式，可选”web“、”redfish“、”dmidecode“缺省值为“web”
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        if mode == "web":
            cpus_info = self.CpuComponent.web_get_cpu_info()
            lut = self.CpuComponent.web.web_get_system_lut()["processor_info"]["cpu_info"]
        elif mode == "redfish":
            res = self.CpuComponent.redfish.get_processors()
            # redfish获取Processors的返回值中，去掉GPU
            cpus_info = [d for d in res if d["ProcessorType"] == "CPU"]
        elif mode == "dmidecode":
            cpus_info = self.CpuComponent.os_host.get_dmidecode(code='4')['Processor Information']
            cache_info = self.CpuComponent.os_host.get_dmidecode(code='7')['Cache Information']
            cache_dict = {}
            for cache in cache_info:
                size = int(cache["Installed Size"].split()[0]) * 1024
                cache_dict.update({cache["Handle"]: str(size)})
        else:
            self.logger.error(f"参数mode当前只支持”web“、”redfish“、”dmidecode“，暂不支持{mode}")
            return False
        self.logger.info(f"web获取的信息: {cpus_info}")
        cpus_hardware_info = copy.deepcopy(hardware_info['CPU'])

        if len(cpus_hardware_info) != len(cpus_info):
            self.logger.error(f"【CPU数量不正确】：\n{mode}获取的数量: {len(cpus_info)}\n硬件配置文件数量: "
                              f"{len(cpus_hardware_info)}")
            return False

        for cpu_info in cpus_info:
            # 配置文件中的值是按照WEB的返回格式编写的，redfish获取的值先做一下格式转化
            if mode == "redfish":
                cpu_info = CommonFun.flatten_dict(cpu_info)
                cpu_info = CommonFun.convert_values_to_str(cpu_info)
                if cpu_info['State'] != 'Enabled' or cpu_info['Health'] != 'OK':
                    self.logger.error(f"CPU状态异常，State：{cpu_info['State']}， Health：{cpu_info['Health']}")
                    return False
            if mode == "web":
                cache_info = re.split(r'[ /]', cpu_info[lut["cache_info"]])
                core_info = re.split(r'[ /]', cpu_info[lut["cores_info"]])
                speed = cpu_info[lut["speed"]].split()[0]
                cpu_info.update({"L1CacheKiB": cache_info[0], "L2CacheKiB": cache_info[1], "L3CacheKiB": cache_info[2],
                                 "TotalCores": core_info[0], "TotalThreads": core_info[2], "cpuMaxspeed": speed})
                if "Kunpeng" in cpu_info[lut["model"]]:
                    cpu_info.update({"cpuProcessor": "ARM"})
            if mode == "dmidecode":
                # "Characteristics"这一项的值为list，不方便统一对比，直接删除，如果后续需要检查此项，则检查后再删除
                del cpu_info['Characteristics']
                cpu_info.update({"L1CacheKiB": cache_dict[cpu_info["L1 Cache Handle"]],
                                 "L2CacheKiB": cache_dict[cpu_info["L2 Cache Handle"]],
                                 "L3CacheKiB": cache_dict[cpu_info["L3 Cache Handle"]],
                                 "cpuMaxspeed": cpu_info["Max Speed"].split()[0],
                                 "cpu_name": cpu_info["Socket Designation"].replace("0", "", 1)})

            for cpu_hw_info in cpus_hardware_info:
                if set(cpu_hw_info.values()) & set(cpu_info.values()) == set(cpu_hw_info.values()):
                    self.logger.info(f"【信息一致】：\n{mode}获取的值：{cpu_info}\n硬件配置文件信息：{cpu_hw_info}")
                    # 删除配置表中已经匹配到元素，防止反复对同一个元素进行匹配
                    cpus_hardware_info.remove(cpu_hw_info)
                    break

                # hardware_info配置文件中最后一个元素都没有匹配到，匹配失败
                if cpu_hw_info == cpus_hardware_info[-1]:
                    self.logger.error(f"【信息不一致】：\n{mode}获取的值: {cpu_info}\n硬件配置文件信息: {hardware_info['CPU']}")
                    return False
        return True
    