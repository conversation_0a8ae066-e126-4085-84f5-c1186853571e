from typing import Any, Dict

from ...utils.exceptions import AdapterError


class PytestResultParser:
    """Pytest 测试结果解析器"""

    @staticmethod
    def parse_result(result: int) -> Dict[str, Any]:
        """解析 pytest 返回的结果码"""
        status_map = {
            0: "passed",  # 所有测试通过
            1: "failed",  # 有测试失败
            2: "interrupted",  # 测试被中断
            3: "internal_error",  # pytest 内部错误
            4: "usage_error",  # 命令行用法错误
            5: "no_tests",  # 没有收集到测试
        }

        status = status_map.get(result, "unknown")
        return {"status": status, "exit_code": result}

    @staticmethod
    def parse_test_report(report: Any) -> Dict[str, Any]:
        """解析 pytest 的测试报告对象"""
        try:
            return {
                "name": report.nodeid,
                "outcome": report.outcome,
                "duration": getattr(report, "duration", 0),
                "error": str(report.longrepr) if report.failed else None,
                "stdout": report.capstdout,
                "stderr": report.capstderr,
            }
        except Exception as e:
            raise AdapterError(f"Failed to parse pytest report: {str(e)}") from e
