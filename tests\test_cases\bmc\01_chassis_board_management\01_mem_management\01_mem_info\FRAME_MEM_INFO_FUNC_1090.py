
"""
功   能：web_查询内存信息测试

修改信息：
    日期：   2025/07/31
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt


class FRAME_MEM_INFO_FUNC_1090(Case):
    """
    CaseId:
        FRAME_MEM_INFO_FUNC_1090
    RunLevel:
        1(#1)
    CaseName:
        web_查询内存信息测试
    PreCondition:
        1、环境正常
    TestStep:
        1._登录BMC_Web，进入系统管理->系统信息->内存，查询内存相关信息，有预期1
        2._点击内存的折叠按键，展开内存相关的详细信息，有预期2
    ExpectedResult:
        1._内存相关信息显示正确，包含总数、在位、名称、厂商、容量、配置速度、最大速度、类型、位置
        2._内存相关信息显示正确，包含名称、位置、厂商、容量、配置速度、最大速度、类型、最小电压、类型详细信息、位宽、部件编码、Rank数、序列号、内存温度_
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.mem_mgt = self.device_dut.find(HardwareMgt.Mem)
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.web_api = self.device_dut.get_api(ns_name="Web")
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }
        self.logger.step("1. 登录BMC_Web，进入系统管理->系统信息->内存，查询内存相关信息，有预期1")
        self.web_handle = self.web_api.login(self.web_login_params)
        res = self.mem_mgt.check_mem_info(self.hardware_json)
        self.assertTrue(res, "内存相关信息显示正确，包含总数、在位、名称、厂商、容量、配置速度、最大速度、类型、位置")

        self.logger.step("2. 点击内存的折叠按键，展开内存相关的详细信息，有预期2")
        # 已在步骤1中实现

    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.web_api.logout(self.web_handle)
