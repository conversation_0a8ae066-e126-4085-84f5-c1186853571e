import inspect
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from types import MethodType
from typing import Any, Dict, List

from engine.utils.assert_tool import AssertTool


class TestLogger(logging.Logger):
    """自定义测试日志类"""

    def step(self, message: str):
        """记录测试步骤"""
        # 定义 STEP 级别
        STEP_LEVEL = 25  # 在 INFO(20) 和 WARNING(30) 之间
        logging.addLevelName(STEP_LEVEL, "STEP")
        # 直接使用 _log 方法记录 STEP 级别的日志
        self._log(STEP_LEVEL, message, ())

    def check(self, message: str):
        """记录检查点"""
        self.info(f"Check: {message}")


# 注册自定义logger类
logging.setLoggerClass(TestLogger)


@dataclass
class Case(ABC,AssertTool):
    """测试用例基类"""

    resource: Any
    parameters: Dict[str, Any] = field(default_factory=dict)

    # 元数据
    name: str = field(init=False)
    description: str = field(init=False)
    priority: int = field(default=0)
    tags: List[str] = field(default_factory=list)
    timeout: int = field(default=300)

    def __post_init__(self):
        """初始化"""
        super(Case, self).__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.start_time = None
        self.end_time = None
        self._load_metadata()

    def _load_metadata(self):
        """加载测试用例元数据"""
        doc = self.__class__.__doc__ or ""
        metadata = self._parse_docstring(doc)

        self.name = metadata.get("name", self.__class__.__name__)
        self.description = metadata.get("description", "")
        self.priority = int(metadata.get("priority", 0))
        self.tags = metadata.get("tags", [])
        self.timeout = int(metadata.get("timeout", 300))

    @staticmethod
    def _parse_docstring(doc: str) -> Dict[str, Any]:
        """解析文档字符串中的元数据"""
        metadata = {}
        current_key = None

        for line in doc.split("\n"):
            line = line.strip()
            if not line:
                continue

            if line.endswith(":"):
                current_key = line[:-1].lower()
                metadata[current_key] = []
            elif current_key:
                metadata[current_key].append(line)

        # 处理特殊字段
        if "tags" in metadata:
            metadata["tags"] = [tag.strip() for tag in " ".join(metadata["tags"]).split(",")]

        return metadata

    @abstractmethod
    def create_meta_data(self):
        """创建测试用例元数据"""
        pass

    @abstractmethod
    def pre_test_case(self):
        """测试前置处理"""
        pass

    @abstractmethod
    def procedure(self):
        """测试步骤"""
        pass

    @abstractmethod
    def post_test_case(self):
        """测试后置处理"""
        pass

    def run(self):
        """执行测试用例"""
        try:
            self.create_meta_data()
            self.pre_test_case()
            self.procedure()
            return True
        finally:
            self.post_test_case()
