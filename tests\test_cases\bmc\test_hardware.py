"""
功   能：硬件信息对比

修改信息：2025/02/21 创建

版权信息：
    ©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt


class test_hardware(Case):
    """
    CaseId:
        test_hardware
    RunLevel:
        3(#3)
    CaseName:
        硬件信息对比
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.web_api = self.device_dut.get_api(ns_name="Web")
        self.cpu_mgt = self.device_dut.find(HardwareMgt.Cpu)
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }
        self.web_handle = self.web_api.login(self.web_login_params)
        self.logger.step("1，对比检查cpu部件信息")
        res = self.cpu_mgt.check_cpu_info(self.hardware_json, "web")
        self.assertTrue(res, "WEB获取的CPU相关信息检查通过")
        res = self.cpu_mgt.check_cpu_info(self.hardware_json, "redfish")
        self.assertTrue(res, "Redfish获取的CPU相关信息检查通过")


    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.web_api.logout(self.web_handle)

