
"""
功   能：SMBIOS_type17查询内存信息测试

修改信息：
    日期：   2025/07/31
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt

class FRAME_MEM_INFO_FUNC_1120(Case):
    """
    CaseId:
        FRAME_MEM_INFO_FUNC_1120
    RunLevel:
        1(#1)
    CaseName:
        SMBIOS_type17查询内存信息测试
    PreCondition:
        1、环境正常
    TestStep:
        1._进入OS，执行dmidecode_-t_17，查询内存相关信息，有预期1
    ExpectedResult:
        1._执行成功，对应在位数量与web一致，其他检查信息符合下面要求：
        Array_Handle:_
        Error_Information_Handle:_Not_Provided
        Total_Width:_与web一致
        Data_Width:_
        Size:_与web一致
        Form_Factor:_DIMM
        Set:_
        Locator:_DIMMXXX
        Bank_Locator:_SOCKET_X_CHANNEL_X_DIMM_X
        Type:_与web一致
        Type_Detail:_
        Speed:_与web一致
        Manufacturer:_与web一致
        Serial_Number:_与web一致
        Asset_Tag:_行业惯例，透传SPD的内存YearWeek
        Part_Number:_与web一致
        Rank:__与web一致
        Configured_Memory_Speed:__与web一致
        Minimum_Voltage:_严格要求精确数据（如1.2v）
        Maximum_Voltage:_严格要求精确数据（如1.2v）
        Configured_Voltage:_严格要求精确数据（如1.2v）
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.mem_mgt = self.device_dut.find(HardwareMgt.Mem)
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.logger.step("1. 进入OS，执行dmidecode -t 17，查询内存相关信息，有预期1")
        res = self.mem_mgt.check_mem_info(self.hardware_json, "dmidecode")
        self.assertTrue(res, "执行成功，对应在位数量与web一致，其他检查信息符合要求")

    def post_test_case(self):
        self.logger.info("post test case.... ")
        