"""
Description: 4.13 传感器命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Sensor(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Sensor, self).__init__()
        self.name = "sensor"

    def cli_query_all_sensor(self):
        """4.13.1 查询所有传感器的所有信息（sensor -d list）
        命令功能: sensor -d list命令用来查询所有传感器信息。
        命令格式: ipmcget -t sensor -d list
        """
        result = self.dispatcher.dispatch("cli_query_all_sensor")[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_sensor_test_command(self, option, value=None):
        """4.13.2 传感器测试命令（sensor -d test）
            命令功能: test命令用于模拟传感器状态或读数。
            命令格式: ipmcset -t sensor -d test -v <sensorname/stopall> [value/stop]
        Args:
            sensorname/stopall: 传感器名称
            value/stop: 模拟值
        """
        params = {"option": option, "value": value}
        result = self.dispatcher.dispatch("cli_sensor_test_command", params=params)[0]["parser"]
        return result

    def cli_simulate_events(self, option, subjectindex=None, eventstatus=None):
        """4.13.3 模拟事件（precisealarm）
            命令功能: precisealarm命令用于模拟iBMC定义的事件。
            命令格式: ipmcset -t precisealarm -d mock -v {eventcode |stopall} [subjectindex] eventstatus
        Args:
            option:
                eventcode: 要模拟事件的事件码。
                stopall: 停止事件模拟动作，取消所有模拟的事件。
            subjectindex: 要模拟的指定事件的事件主体类型代表的事件序号。
            eventstatus: 模拟告警的状态。
        """
        params = {
            "option": option,
            "eventstatus": eventstatus,
            "subjectindex": subjectindex,
        }
        result = self.dispatcher.dispatch("cli_simulate_events", params=params)[0]["parser"]
        return result

    def get_sensor_id_list(self, params=None):
        """
        获取传感器id列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("cli_get_sensor_id_list", params=params)[0]["parser"]
