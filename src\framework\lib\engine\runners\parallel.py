import concurrent.futures
import logging
from typing import Any, Dict, List

from .base import BaseRunner

logger = logging.getLogger(__name__)


class ParallelRunner(BaseRunner):
    """并行测试运行器"""

    def run(self, test_paths: List[str]) -> List[Dict[str, Any]]:
        """并行执行测试用例"""
        total = len(test_paths)
        logger.info(f"开始并行执行 {total} 个测试，工作进程数: {self.config.workers}")

        with concurrent.futures.ProcessPoolExecutor(max_workers=self.config.workers) as executor:
            # 提交所有测试任务
            futures = []
            for path in test_paths:
                future = executor.submit(self._execute_test, path)
                futures.append((path, future))

            # 收集结果
            results = []
            completed = 0

            for path, future in futures:
                try:
                    result = future.result(timeout=self.config.timeout)
                    results.append(result)
                    completed += 1
                    logger.info(f"完成测试 [{completed}/{total}]: {path}")

                except concurrent.futures.TimeoutError:
                    logger.error(f"测试超时: {path}")
                    results.append({"path": path, "status": "error", "error": "Test execution timed out"})
                except Exception as e:
                    logger.error(f"测试失败: {path} - {str(e)}")
                    results.append({"path": path, "status": "error", "error": str(e)})

            return results

    def _execute_test(self, test_path: str) -> Dict[str, Any]:
        """执行单个测试"""
        try:
            result = self._handle_retry(test_path, retry_count=self.config.retry_count)
            parsed_result = self.adapter.parse_results(result["raw_result"])
            return {"path": test_path, **parsed_result}
        except Exception as e:
            logger.error(f"测试执行错误: {test_path} - {str(e)}")
            return {"path": test_path, "status": "error", "error": str(e)}
