"""
Description: 4.12 风扇命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_sol_ns


@ipmi_sol_ns.dispatchertype(HostType.Local)
class IpmiSol(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(IpmiSol, self).__init__()

    def ipmi_sol_test(self, params=None):
        """
        Args:
                NA
        """
        result = self.dispatcher.dispatch("ipmisoltest", params=params)[0]["parser"]
        return result

    def sol_ls(self, params=None):
        """
        Args:
                NA
        """
        result = self.dispatcher.dispatch("sol_ls", params=params)[0]["parser"]
        return result

    def ipmi_power_cycle(self, params=None):
        """
        Args:
                NA
        """
        result = self.dispatcher.dispatch("ipmi_power_cycle", params=params)[0]["parser"]
        return result
