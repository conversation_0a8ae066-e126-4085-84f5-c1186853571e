def format_section(title: str, content: str = "", width: int = 100) -> str:
    """格式化输出段落
    
    Args:
        title: 段落标题
        content: 段落内容
        width: 输出宽度
    """
    separator = "=" * width
    dash = "-" * width

    # 格式化标题部分
    header = (
        f"\n{separator}\n"
        f"{title:^{width}}\n"
        f"{separator}\n"
    )

    # 如果有内容，添加内容和底部分隔符
    if content:
        return f"{header}{content}\n{dash}"

    return header


def format_key_value(key: str, value: str, indent: int = 0) -> str:
    """格式化键值对输出
    
    Args:
        key: 键名
        value: 值
        indent: 缩进空格数
    """
    return f"{' ' * indent}{key:<15} {value}"


def format_list(items: list, indent: int = 0) -> str:
    """格式化列表输出
    
    Args:
        items: 列表项
        indent: 缩进空格数
    """
    return "\n".join(f"{' ' * indent}- {item}" for item in items)
