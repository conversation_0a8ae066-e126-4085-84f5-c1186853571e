"""
Description: 4.4 Trap命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Trap(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Trap, self).__init__()
        self.name = "trap"

    @validate_param(method=str)
    def cli_query_set_snmp_trap_state(self, method, value=None, destination=None):
        """4.4.1 查询SNMP和设置 trap状态（trap -d state）
            命令功能: trap -d state命令用于查询和设置iBMC的SNMP trap功能的使能和禁止状态。
            命令格式: ipmcget -t trap -d state [-v destination]
                    ipmcset -t trap -d state -v [destination] <disabled | enabled>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            destination: 表示SNMP trap目标项。
            value:
                disabled: 表示禁用SNMP trap功能。
                enabled: 表示启用SNMP trap功能。
        """
        params = {"method": method, "value": value, "destination": destination}
        result = self.dispatcher.dispatch("cli_query_set_snmp_trap_state", params=params)[0]["parser"]
        return result

    @validate_param(destination=str, portvalue=str)
    def cli_set_snmp_trap_port_number(self, destination, portvalue):
        """4.4.2 设置SNMP trap上报端口号（trap -d port）
            命令功能: trap -d port命令用于设置iBMC的SNMP trap上报端口号。
            命令格式: ipmcset -t trap -d port -v <destination> <portvalue>
        Args:
            destination: 表示SNMP trap目标项。
            portvalue: 表示SNMP trap端口号。
        """
        params = {"portvalue": portvalue, "destination": destination}
        result = self.dispatcher.dispatch("cli_set_snmp_trap_port_number", params=params)[0]["parser"]
        return result

    @validate_param(community=str)
    def cli_set_snmp_trap_community_name(self, community):
        """4.4.3 设置SNMP trap团体名称（trap -d community）
            命令功能: trap -d community命令用于设置iBMC的SNMP trap团体名称。
            命令格式: ipmcset -t trap -d community
        Args:
            Community: 表示SNMP trap团体名称。
        """
        params = {"community": community}
        result = self.dispatcher.dispatch("cli_set_snmp_trap_community_name", params=params)[0]["parser"]
        return result

    @validate_param(destination=str, ipaddr=str)
    def cli_set_snmp_trap_ip_address(self, destination, ipaddr):
        """4.4.4 设置SNMP trap目的IP地址（trap -d address）
            命令功能: trap -d address命令用于设置SNMP trap上报信息的目的IP地址。
            命令格式: ipmcset -t trap -d address -v <destination> <ipaddr>
        Args:
            destination: 表示SNMP trap目标项。
            ipaddr: 表示接收事件信息上报的IP地址。
        """
        params = {"ipaddr": ipaddr, "destination": destination}
        result = self.dispatcher.dispatch("cli_set_snmp_trap_ip_address", params=params)[0]["parser"]
        return result

    def cli_query_snmp_trap_destination(self):
        """4.4.5 查询Trap上报目的地址信息（trap -d trapiteminfo）
        命令功能: trap -d trapiteminfo命令用于查询SNMP trap上报信息的目的IP地址、上报端口、使能状态。
        命令格式: ipmcget -t trap -d trapiteminfo
        """
        result = self.dispatcher.dispatch("cli_query_snmp_trap_destination")[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_snmp_trap_version(self, method, value=None):
        """4.4.6 查询和设置SNMP trap版本信息（trap -d version）
            命令功能: trap -d version命令用于查询和设置SNMP trap版本信息。
            命令格式: ipmcget -t trap -d version
                    ipmcset -t trap -d version -v <V1 | V2C | V3>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            value:
                V1: 表示SNMP trap版本号为V1。
                V2C: 表示SNMP trap版本号为V2C。
                V3: 表示SNMP trap版本号为V3。
        """
        params = {"method": method, "value": value}
        result = self.dispatcher.dispatch("cli_query_set_snmp_trap_version", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_snmp_trap_alarm_severities(self, method, level=None):
        """4.4.7 查询和设置SNMP trap告警发送级别（trap -d severity）
            命令功能: trap -d severity命令用于查询和设置SNMP trap的告警发送级别。
            命令格式: ipmcget -t trap -d severity
                    ipmcset -t trap -d severity -v <level>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            level: 表示SNMP trap的告警发送级别。
        """
        params = {"method": method, "level": level}
        result = self.dispatcher.dispatch("cli_query_set_snmp_trap_alarm_severities", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_snmpv3_trap_user(self, method, username=None):
        """4.4.8 查询和设置SNMP trap V3用户（trap -d user）
            命令功能: trap -d user命令用于查询和设置SNMP trap V3用户。
            命令格式: ipmcget -t trap -d user
                    ipmcset -t trap -d user -v <username>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            username: 表示SNMP trap V3用户。
        """
        params = {"method": method, "username": username}
        result = self.dispatcher.dispatch("cli_query_set_snmpv3_trap_user", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_snmpv3_authentication_privacy_protocols(self, method, option=None):
        """4.4.9 查询和设置SNMP trap V3鉴权和加密协议（trap -d protocol）
            命令功能: trap -d protocol命令用于查询和设置SNMP trap V3的鉴权和加密协议。
            命令格式: ipmcget -t trap -d protocol
                    ipmcset -t trap -d protocol -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 表示SNMP trap V3采用的加密协议。
        """
        params = {"method": method, "option": option}
        result = self.dispatcher.dispatch("cli_query_set_snmpv3_authentication_privacy_protocols", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(method=str)
    def cli_query_set_snmp_trap_mode(self, method, option=None):
        """4.4.10 查询和设置SNMP trap模式（trap -d mode）
            命令功能: trap -d mode命令用于查询和设置SNMP trap模式。
            命令格式: ipmcget -t trap -d mode
                    ipmcset -t trap -d mode -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 表示SNMP trap模式类型。
        """
        params = {"method": method, "option": option}
        result = self.dispatcher.dispatch("cli_query_set_snmp_trap_mode", params=params)[0]["parser"]
        return result
