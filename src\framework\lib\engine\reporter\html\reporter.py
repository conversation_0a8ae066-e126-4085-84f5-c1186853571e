"""HTML报告实现"""
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
import os
import re

from ..base import BaseReporter
from ..template.engine import Jinja2Engine


@BaseReporter.register("html")
class HtmlReporter(BaseReporter):
    """HTML报告生成器"""

    # 定义常量
    FILE_SIZE_LIMIT = 1 * 1024 * 1024  # 10MB

    def __init__(self, config: Dict[str, Any]):
        self.current_logs: List[Dict[str, Any]] = []
        self.current_report_file: Optional[Path] = None
        self.summary_file: Optional[Path] = None
        
        # 设置默认模板目录
        if "template" not in config:
            config["template"] = {}
        if "dir" not in config["template"]:
            config["template"]["dir"] = str(Path(__file__).parent / "templates")
        
        # 现在调用父类初始化，此时配置已经包含了正确的模板目录
        super().__init__(config)

    # ===== 公共接口方法 =====
    
    def start_test(self, case_info: Dict[str, Any]) -> None:
        """开始测试用例"""
        processed_data = self.processor.process_case(case_info)
        self._handle_case_start(processed_data)
        # 更新摘要报告
        self._update_summary()

    def end_test(self, case_result: Dict[str, Any]) -> None:
        """结束测试用例"""
        processed_data = self.processor.process_case(case_result)
        self._handle_case_end(processed_data)
        # 更新摘要报告
        self._update_summary()

    def add_log(self, log_entry: Dict[str, Any]) -> None:
        """添加日志记录"""
        processed_log = self.processor.process_log(log_entry)
        self._handle_log(processed_log)
        
    def generate(self) -> None:
        """生成HTML报告"""
        if not self.report_dir:
            return
            
        try:
            # 生成摘要报告
            self._update_summary()
            
            # 复制静态资源
            self._copy_assets()
        except Exception as e:
            print(f"Failed to generate HTML report: {e}")
            
    def stop_test(self, case_id: str) -> None:
        """停止测试用例"""
        try:
            # 查找正在运行的用例
            for case in self.cases:
                if case.get("id") == case_id and case.get("status") == "running":
                    # 更新状态为停止
                    case["status"] = "stopped"
                    case["end_time"] = datetime.now()
                    # 计算持续时间
                    if "start_time" in case:
                        start_time = case["start_time"]
                        if isinstance(start_time, str):
                            try:
                                start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                            except ValueError:
                                start_time = datetime.now()
                        duration = (datetime.now() - start_time).total_seconds()
                        case["duration"] = round(duration, 2)
                    
                    if self.current_case and self.current_case.get("id") == case_id:
                        self.current_case["status"] = "stopped"
                        self.current_case["end_time"] = datetime.now()
                        # 计算持续时间
                        if "start_time" in self.current_case:
                            start_time = self.current_case["start_time"]
                            duration = (datetime.now() - start_time).total_seconds()
                            self.current_case["duration"] = round(duration, 2)
                        # 重新渲染完整页面
                        self._write_report_header()
                    # 更新摘要报告
                    self._update_summary()
                    break
        except Exception as e:
            import traceback
            print(f"Failed to stop test: {e}")
            print(traceback.format_exc())

    # ===== 用例处理方法 =====
    
    def _handle_case_start(self, case_data: Dict[str, Any]) -> None:
        """处理用例开始"""
        self.current_case = case_data
        self.current_case["start_time"] = datetime.now()
        self.current_case["status"] = "running"
        self.current_case["end_time"] = None  # 开始时结束时间为空
        self.current_case["duration"] = 0  # 初始化执行时长为0
        self.current_logs = []
        
        case_id = case_data.get("id", "unknown_test")
        self.current_report_file = self._get_report_file(case_id)
        
        # 立即写入初始报告
        self._write_report_header()
        
        # 将用例添加到cases列表并更新摘要
        # 检查是否已存在相同ID的用例
        existing_case = next((case for case in self.cases if case.get("id") == case_id), None)
        if existing_case:
            # 更新现有用例
            existing_case.update(self.current_case)
        else:
            # 添加新用例
            self.cases.append(self.current_case)
        
        # 更新摘要报告
        self._update_summary()

    def _handle_case_end(self, case_data: Dict[str, Any]) -> None:
        """处理用例结束"""
        if self.current_case:
            # 更新用例状态和结果
            self.current_case.update({
                "status": case_data.get("status"),
                "duration": case_data.get("duration"),
                "error": case_data.get("error"),
                "end_time": datetime.now(),
                "logs": self.current_logs  # 添加日志数据
            })
            
            # 重新渲染完整页面
            self._write_report_header()
            
            # 更新cases列表中的对应用例
            case_id = self.current_case.get("id")
            for case in self.cases:
                if case.get("id") == case_id:
                    case.update(self.current_case)
                    break
            
            # 更新摘要报告
            self._update_summary()
            
            self.current_case = None
            self.current_report_file = None

    def _handle_log(self, log_data: Dict[str, Any]) -> None:
        """处理日志"""
        try:
            self.current_logs.append(log_data)
            
            # 实时写入日志
            if self.current_report_file and self.template_engine and self.current_case:
                # 更新执行时长 - 实时计算
                if "start_time" in self.current_case:
                    start_time = self.current_case["start_time"]
                    duration = (datetime.now() - start_time).total_seconds()
                    self.current_case["duration"] = round(duration, 2)
                    
                    # 同步更新cases列表中的对应用例
                    case_id = self.current_case.get("id")
                    for case in self.cases:
                        if case.get("id") == case_id:
                            case["duration"] = self.current_case["duration"]
                            break
                    
                    # 每次日志更新时也更新摘要报告
                    self._update_summary()
                
                # 检查文件大小，如果超过限制则创建新文件
                if self.current_report_file.exists():
                    file_size = self.current_report_file.stat().st_size
                    if file_size >= self.FILE_SIZE_LIMIT:
                        # 创建新文件
                        case_id = self.current_case.get("id", "unknown_test")
                        case_dir = self.report_dir / case_id
                        existing_files = list(case_dir.glob(f"{case_id}--*.html"))
                        next_index = len(existing_files)
                        self.current_report_file = case_dir / f"{case_id}--{next_index}.html"
                        print(f"Creating new report file: {self.current_report_file}")
                        # 清空日志列表，为新文件准备
                        self.current_logs = [log_data]
                
                self.current_case["logs"] = self.current_logs
                self._write_report_header()
        except Exception as e:
            print(f"Failed to handle log: {e}")

    # ===== 文件操作方法 =====

    def _get_report_file(self, case_id: str) -> Path:
        """获取报告文件路径"""
        if not self.report_dir:
            raise ValueError("Report directory not set")
            
        case_dir = self.report_dir / case_id
        case_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建第一个文件
        return case_dir / f"{case_id}--0.html"

    def _write_report_header(self) -> None:
        """写入报告头部"""
        if not self.current_report_file or not self.template_engine or not self.current_case:
            return
        
        try:
            # 加载模板
            template = self.template_engine.load_template("case.html")
            
            # 获取当前用例的所有日志
            logs = self.current_logs
            
            # 从文件名中提取页码
            current_page = 1
            match = re.search(r"--(\d+)\.html$", str(self.current_report_file))
            if match:
                current_page = int(match.group(1)) + 1  # 因为文件索引从0开始，页码从1开始
            
            # 查找同一用例的所有报告文件
            case_id = self.current_case.get("id", "unknown_test")
            case_dir = self.current_report_file.parent
            report_files = sorted(list(case_dir.glob(f"{case_id}--*.html")))
            total_pages = len(report_files)
            
            # 计算上一页和下一页的链接
            prev_page = None
            next_page = None
            
            if current_page > 1:
                prev_index = current_page - 2  # 转换为0基索引
                prev_page = f"{case_id}--{prev_index}.html"
            
            if current_page < total_pages:
                next_index = current_page  # 当前页码对应的下一个文件索引
                next_page = f"{case_id}--{next_index}.html"
            
            # 渲染模板
            html_content = self.template_engine.render(template, {
                "case": self.current_case,
                "logs": logs,
                "current_page": current_page,
                "total_pages": total_pages,
                "prev_page": prev_page,
                "next_page": next_page
            })
            
            # 写入文件
            with open(self.current_report_file, "w", encoding="utf-8") as f:
                f.write(html_content)
                f.flush()
                os.fsync(f.fileno())
            
        except Exception as e:
            print(f"Failed to write report header: {e}")

    def _copy_assets(self) -> None:
        """复制静态资源文件"""
        if not self.report_dir:
            return

        try:
            assets_src = Path(__file__).parent / "templates" / "assets"
            if not assets_src.exists():
                return

            assets_dst = self.report_dir / "assets"  # 确保资源目录在根目录下
            if assets_dst.exists():
                import shutil
                shutil.rmtree(assets_dst)
            
            import shutil
            shutil.copytree(assets_src, assets_dst)

        except Exception as e:
            print(f"Failed to copy assets: {e}")

    # ===== 摘要报告方法 =====

    def _update_summary(self) -> None:
        """更新摘要报告"""
        if not self.report_dir or not self.template_engine:
            print("Cannot update summary: report_dir or template_engine is None")
            return
            
        try:
            # 确保摘要文件存在
            if not self.summary_file:
                self.summary_file = self.report_dir / "index.html"
                # print(f"Creating summary file at: {self.summary_file}")
            
            # 计算统计信息
            total_cases = len(self.cases)
            passed_cases = sum(1 for case in self.cases if case.get("status") == "passed")
            failed_cases = sum(1 for case in self.cases if case.get("status") == "failed")
            running_cases = sum(1 for case in self.cases if case.get("status") == "running")
            stopped_cases = sum(1 for case in self.cases if case.get("status") == "stopped")
            
            # 确保所有用例都有必要的字段并更新执行时长
            current_time = datetime.now()
            for case in self.cases:
                # 处理开始时间
                if "start_time" not in case or case["start_time"] is None:
                    case["start_time"] = current_time
                elif isinstance(case["start_time"], str):
                    try:
                        # 尝试将字符串转换为datetime对象
                        case["start_time"] = datetime.strptime(case["start_time"], "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # 如果格式不匹配，使用当前时间
                        case["start_time"] = current_time
                
                # 更新所有运行中用例的执行时长
                if case.get("status") == "running":
                    # 计算从开始到现在的时长
                    duration = (current_time - case["start_time"]).total_seconds()
                    case["duration"] = round(duration, 2)
                elif "duration" not in case or case["duration"] is None:
                    case["duration"] = 0
                
                # 处理结束时间
                if case.get("status") in ["passed", "failed", "stopped"]:
                    if "end_time" not in case or case["end_time"] is None:
                        case["end_time"] = current_time
                    elif isinstance(case["end_time"], str):
                        try:
                            case["end_time"] = datetime.strptime(case["end_time"], "%Y-%m-%d %H:%M:%S")
                        except ValueError:
                            case["end_time"] = current_time
                else:
                    # 运行中的用例不显示结束时间
                    case["end_time"] = None
                
                # 格式化时间为字符串用于显示
                if isinstance(case["start_time"], datetime):
                    case["start_time_display"] = case["start_time"].strftime("%Y-%m-%d %H:%M:%S")
                else:
                    case["start_time_display"] = str(case["start_time"])
                    
                if case["end_time"] and isinstance(case["end_time"], datetime):
                    case["end_time_display"] = case["end_time"].strftime("%Y-%m-%d %H:%M:%S")
                else:
                    case["end_time_display"] = "运行中"
                    
                # 添加报告链接
                self._update_case_report_link(case)
            
            # 获取环境信息
            import platform
            import socket
            
            # 创建摘要数据
            summary_data = {
                "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_cases": total_cases,
                "passed_cases": passed_cases,
                "failed_cases": failed_cases,
                "running_cases": running_cases,
                "stopped_cases": stopped_cases,
                "pass_rate": (passed_cases / total_cases * 100) if total_cases > 0 else 0,
                "report_dir": str(self.report_dir),  # 添加报告目录信息
                "update_time": current_time.strftime("%Y-%m-%d %H:%M:%S"),
                # 环境信息
                "os_info": f"{platform.system()} {platform.release()}",
                "python_version": platform.python_version(),
                "hostname": socket.gethostname(),
                "ip_address": socket.gethostbyname(socket.gethostname())
            }
            
            # 加载并渲染模板
            try:
                template = self.template_engine.load_template("summary.html")
                # print("Summary template loaded successfully")
            except Exception as template_error:
                print(f"Failed to load summary template: {template_error}")
                # 如果找不到模板，使用默认模板
                template = self._get_default_summary_template()
                print("Using default summary template")
            
            # 准备渲染数据
            stats = {
                "total": total_cases,
                "passed": passed_cases,
                "failed": failed_cases,
                "running": running_cases,
                "stopped": stopped_cases,
                "pass_rate": (passed_cases / total_cases * 100) if total_cases > 0 else 0
            }
            
            # 按照用例执行的顺序排序（使用开始时间）
            sorted_cases = sorted(self.cases, key=lambda x: x.get("start_time", current_time))
            
            html_content = self.template_engine.render(template, {
                "cases": sorted_cases,  # 使用按开始时间正序排序的用例列表
                "summary": summary_data,
                "stats": stats,
                "timestamp": current_time.strftime("%Y-%m-%d %H:%M:%S")
            })
            
            # 写入摘要文件
            with open(self.summary_file, "w", encoding="utf-8") as f:
                f.write(html_content)
                f.flush()
                os.fsync(f.fileno())
            # print(f"Summary file written successfully to {self.summary_file}")
                
        except Exception as e:
            import traceback
            print(f"Failed to update summary: {e}")
            print(traceback.format_exc())
            
    def _update_case_report_link(self, case: Dict[str, Any]) -> None:
        """更新用例报告链接"""
        if not self.report_dir:
            return
            
        case_id = case.get("id", "unknown_test")
        case_dir = self.report_dir / case_id
        if case_dir.exists():
            # 查找最新的报告文件
            report_files = list(case_dir.glob(f"{case_id}--*.html"))
            if report_files:
                latest_file = sorted(report_files)[-1]
                case["report_link"] = f"{case_id}/{latest_file.name}"
            else:
                # 如果没有找到报告文件，使用默认命名
                case["report_link"] = f"{case_id}/{case_id}--0.html"
        else:
            case["report_link"] = f"{case_id}/{case_id}--0.html"

    def _get_default_summary_template(self) -> str:
        """获取默认摘要模板"""
        return self.template_engine.env.from_string("""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试报告摘要</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; }
        h1 { margin-top: 0; color: #333; }
        .timestamp { color: #666; margin-bottom: 20px; }
        
        /* 统计卡片 */
        .summary-card { 
            background: white; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 30px; 
            overflow: hidden;
        }
        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        .summary-header h2 { margin: 0; font-size: 18px; }
        .summary-stats {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
            gap: 20px;
            justify-content: space-between;
        }
        .stat-item {
            flex: 1;
            min-width: 120px;
            text-align: center;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label { font-size: 14px; color: #666; }
        
        /* 状态颜色 */
        .passed, .stat-item.passed .stat-value { color: #28a745; }
        .failed, .stat-item.failed .stat-value { color: #dc3545; }
        .running, .stat-item.running .stat-value { color: #007bff; }
        .stopped, .stat-item.stopped .stat-value { color: #fd7e14; }
        
        /* 用例表格 */
        .cases-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .cases-table th, .cases-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .cases-table th { background: #f8f9fa; font-weight: 500; }
        .case-row:hover { background-color: #f8f9fa; }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-badge.passed { background: #d4edda; color: #155724; }
        .status-badge.failed { background: #f8d7da; color: #721c24; }
        .status-badge.running { background: #cce5ff; color: #004085; }
        .status-badge.stopped { background: #fff3cd; color: #856404; }
        
        /* 按钮样式 */
        .view-btn {
            display: inline-block;
            padding: 4px 10px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #495057;
            text-decoration: none;
            font-size: 12px;
        }
        .view-btn:hover { background: #e9ecef; }
    </style>
    <meta http-equiv="refresh" content="5">
</head>
<body>
    <div class="container">
        <h1>测试报告摘要</h1>
        <p class="timestamp">更新时间: {{ timestamp }}</p>
        
        <!-- 摘要统计 -->
        <div class="summary-card">
            <div class="summary-header">
                <h2>测试概览</h2>
                <span>开始于: {{ summary.start_time }}</span>
            </div>
            
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ stats.total }}</div>
                    <div class="stat-label">总用例数</div>
                </div>
                <div class="stat-item passed">
                    <div class="stat-value">{{ stats.passed }}</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-item failed">
                    <div class="stat-value">{{ stats.failed }}</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-item running">
                    <div class="stat-value">{{ stats.running }}</div>
                    <div class="stat-label">运行中</div>
                </div>
                <div class="stat-item stopped">
                    <div class="stat-value">{{ stats.stopped }}</div>
                    <div class="stat-label">已停止</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ "%.2f"|format(stats.pass_rate) }}%</div>
                    <div class="stat-label">通过率</div>
                </div>
            </div>
        </div>
        
        <!-- 用例列表 -->
        <h2>测试用例列表</h2>
        <table class="cases-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用例名称</th>
                    <th>状态</th>
                    <th>执行时长</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for case in cases %}
                <tr class="case-row {{ case.status }}">
                    <td>{{ case.id }}</td>
                    <td>{{ case.name }}</td>
                    <td><span class="status-badge {{ case.status }}">{{ case.status }}</span></td>
                    <td>{{ case.duration }}秒</td>
                    <td>{{ case.start_time_display }}</td>
                    <td>{{ case.end_time_display }}</td>
                    <td><a href="{{ case.report_link }}" class="view-btn">查看详情</a></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>
""")

    def initialize(self, report_dir: str) -> None:
        """初始化报告生成器"""
        super().initialize(report_dir)
        
        # 立即复制静态资源
        self._copy_assets()
        
        # 初始化摘要报告
        self._update_summary()
        # print("HTML reporter initialized successfully")