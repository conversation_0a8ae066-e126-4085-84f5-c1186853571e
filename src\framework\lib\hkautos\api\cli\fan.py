"""
Description: 4.12 风扇命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Fan(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Fan, self).__init__()
        self.name = "fan"

    @validate_param(fanlevel=str)
    def cli_set_fan_speed(self, fanlevel, fanid=None):
        """4.12.1 设置风扇运行速度（fanlevel）
            命令功能: fanlevel命令用于设置风扇运行速度。
            命令格式: ipmcset -d fanlevel -v <fanlevel> [fanid]
        Args:
            fanlevel: 表示设置当前风扇转速为全速运转时的百分比。
            fanid: 表示风扇的ID
        """
        params = {"fanlevel": fanlevel, "fanid": fanid}
        result = self.dispatcher.dispatch("cli_set_fan_speed", params=params)[0]["parser"]
        return result

    @validate_param(mode=str)
    def cli_set_fan_mode(self, mode, timeout=None):
        """4.12.2 设置风扇运行模式（fanmode）
            命令功能: fanmode命令用来设置风扇的运行模式。
            命令格式: ipmcset -d fanmode -v <mode> [timeout]
        Args:
            mode: 表示风扇工作模式
            timeout: 表示由手动模式转换成自动模式的超时时间。
        """
        params = {"mode": mode, "fan_timeout": timeout}
        result = self.dispatcher.dispatch("cli_set_fan_mode", params=params)[0]["parser"]
        return result

    def cli_query_fan_state(self):
        """4.12.3 查询风扇工作状态（faninfo）
        命令功能: faninfo命令用来查询风扇的工作模式和当前转速。
        命令格式: ipmcget -d faninfo
        """
        result = self.dispatcher.dispatch("cli_query_fan_state")[0]["parser"]
        return result
