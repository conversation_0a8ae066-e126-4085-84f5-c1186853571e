#!/usr/bin/env python3
"""
测试执行入口脚本
"""

import sys
from pathlib import Path
from typing import Optional

import click
from engine import TestEngine
from engine.core.hktest_config import HKTestConfig
from engine.utils.formatter import format_key_value, format_section
from engine.utils.logger import setup_logger

BANNER = r"""
 _   _   _   __   ___    _   _   _____   _____   _____   _____   _____   _____
| | | | | | / /  / _ \  | | | | |_   _| |  _  | |_   _| |  ___| /  ___| |_   _|
| |_| | | |/ /  / /_\ \ | | | |   | |   | | | |   | |   | |__   \ `--.    | |
|  _  | |    \  |  _  | | | | |   | |   | | | |   | |   |  __|   `--. \   | |
| | | | | |\  \ | | | | | |_| |   | |   \ \_/ /   | |   | |___  /\__/ /   | |
\_| |_/ \_| \_/ \_| |_/  \___/    \_/    \___/    \_/   \____/  \____/    \_/

HKAutoTest - 华鲲自动化测试框架 v1.0.0
Copyright © 2025-2030 四川华鲲振宇智能科技有限责任公司
"""


@click.command()
@click.option(
    "--test-bed",
    type=click.Path(exists=True),
    default=Path(__file__).parent / "config" / "test_bed.yaml",
    help="测试环境配置文件路径",
)
@click.option(
    "--test-set",
    type=click.Path(exists=True),
    default=Path(__file__).parent / "config" / "test_set.yaml",
    help="测试集配置文件路径",
)
@click.option(
    "--test-cases-dir",
    type=click.Path(exists=True),
    default=Path(__file__).parents[3] / "tests" / "test_cases",
    help="测试用例目录路径",
)
@click.option("--parallel/--no-parallel", default=False, help="是否并行执行测试")
@click.option("--workers", type=int, default=1, help="并行执行的工作进程数")
@click.option(
    "--log-level",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR"], case_sensitive=False),
    default="DEBUG",
    help="日志级别",
)
@click.option("--log-file", type=click.Path(), help="日志文件路径")
@click.option("--task-id", type=str, help="测试任务ID，不指定则自动生成")
def main(
    test_bed: str,
    test_set: str,
    test_cases_dir: str,
    parallel: bool,
    workers: int,
    log_level: str,
    log_file: Optional[str],
    task_id: Optional[str],
):
    """HKTest 测试执行工具

    执行指定的测试套件和测试用例。
    """
    # 设置logger
    logger = setup_logger(level=log_level, log_file=log_file, logger_name="hktest")

    # 打印Banner
    logger.info(BANNER)

    # 格式化配置信息
    config_info = "\n".join(
        [
            format_key_value("测试环境配置", test_bed),
            format_key_value("测试集配置", test_set),
            format_key_value("测试用例目录", test_cases_dir),
            format_key_value("并行执行", "是" if parallel else "否"),
            format_key_value("日志级别", log_level),
        ]
    )

    if parallel:
        config_info += "\n" + format_key_value("工作进程数", str(workers))
    if log_file:
        config_info += "\n" + format_key_value("日志文件", log_file)
    if task_id:
        config_info += "\n" + format_key_value("任务ID", task_id)

    logger.info(format_section("配置信息", config_info))

    try:
        # 创建配置
        config = HKTestConfig(test_bed=str(test_bed), test_set=str(test_set), test_cases_dir=str(test_cases_dir))

        # 更新运行时配置
        config.parallel = parallel
        config.workers = workers
        config.log_level = log_level
        config.log_file = log_file

        # 只在指定task_id时设置状态上报配置
        if task_id:
            config.status_report = {"task_id": task_id}
        else:
            config.status_report = None

        # 开始执行提示
        logger.info(format_section("开始执行测试"))

        # 创建并运行引擎
        engine = TestEngine.create(config=config)
        results = engine.run_suite()

        # 根据失败用例数判断整体执行结果
        if results["failed"] == 0:
            sys.exit(0)  # 所有用例都通过，返回0
        else:
            sys.exit(1)  # 有失败用例，返回1

    except Exception as e:
        logger.error(f"\n错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
