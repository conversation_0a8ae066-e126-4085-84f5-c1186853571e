"""状态上报模块

提供测试用例执行状态的上报功能，包括：
1. 用例执行状态上报（开始、通过、失败等）
2. 用户认证和Token管理
3. 请求重试和错误处理
4. 批量上报和队列管理
"""
from typing import Dict, Any, Optional, List, Set
import json
import logging
import requests
import asyncio
import aiohttp
import zlib
from datetime import datetime, timedelta
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from engine.utils.singleton import Singleton
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)

# 默认状态上报配置
DEFAULT_STATUS_REPORT_CONFIG = {
    "api": {
        "base_url": "http://192.168.234.151:8080/jeecg-boot",
        "status": "/run_record/hktestRecode/update_task_status"  # 状态上报接口
    },
    "retry": {
        "total": 3,                          # 最大重试次数
        "backoff_factor": 0.5,               # 退避因子
        "status_forcelist": [500, 502, 503, 504]
    },
    "headers": {
        "Content-Type": "application/json"    # 默认请求头
    }
}

class Status:
    """测试用例状态枚举"""
    NOT_STARTED = "NOT_STARTED"  # 未开始
    RUNNING = "RUNNING"          # 运行中
    PASSED = "PASSED"           # 通过
    FAILED = "FAILED"           # 失败
    ERROR = "ERROR"             # 错误
    SKIPPED = "SKIPPED"         # 跳过
    INTERRUPTED = "INTERRUPTED" # 中断

class StatusReporter(metaclass=Singleton):
    """状态上报器，使用单例模式确保全局唯一实例"""

    def __init__(self):
        """初始化状态上报器属性"""
        # 基础属性
        self._enabled = False
        self._api_url = None
        self._task_id = None
        self._headers = None

        # 会话和重试
        self._session = None
        self._retry_strategy = None
        self._async_session = None
        self._thread_pool = ThreadPoolExecutor(max_workers=4)

        # 状态管理
        self._current_case = None  # 当前正在执行的用例状态
        self._executed_cases = {}  # 已执行用例的状态记录
        self._status_lock = threading.Lock()  # 改用线程锁

    def _sync_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """同步发送HTTP请求

        Args:
            method: 请求方法
            url: 请求URL
            **kwargs: 请求参数

        Returns:
            requests.Response: 响应对象
        """
        if not self._session:
            self._session = requests.Session()
            self._session.mount('http://', HTTPAdapter(max_retries=self._retry_strategy))
            self._session.mount('https://', HTTPAdapter(max_retries=self._retry_strategy))

        # 打印请求信息
        logger.info(f"\n发送请求:")
        logger.info(f"URL: {url}")
        logger.info(f"Method: {method}")
        logger.info(f"Headers: {kwargs.get('headers', {})}")
        if 'json' in kwargs:
            logger.info(f"Request Body: {json.dumps(kwargs['json'], ensure_ascii=False, indent=2)}")

        response = self._session.request(method, url, **kwargs)

        # 打印响应信息
        logger.info(f"\n收到响应:")
        logger.info(f"Status Code: {response.status_code}")
        try:
            response_json = response.json()
            logger.info(f"Response Body: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
        except:
            logger.info(f"Response Text: {response.text}")

        response.raise_for_status()
        return response

    def report_status_sync(self, case_id: str, status: str, details: Optional[Dict[str, Any]] = None) -> bool:
        """同步上报用例执行状态

        Args:
            case_id: 测试用例ID
            status: 执行状态
            details: 详细信息

        Returns:
            bool: 上报是否成功
        """
        if not self._enabled:
            logger.debug("状态上报已禁用，跳过上报")
            return True

        try:
            # 构建状态数据
            data = {
                'caseId': case_id,
                'status': status,
                'updateTime': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'errorMsg': details.get('error', ''),
                'duration': details.get('duration', 0),
                'logInfo': details.get('log', ''),
                'reportLinks': details.get('reportLinks', '')
            }

            self._send_single_sync(data)
            return True

        except Exception as e:
            logger.error(f"状态上报失败: {str(e)}")
            return True  # 即使失败也返回True，不影响测试执行

    def _send_single_sync(self, data: Dict[str, Any]) -> None:
        """同步发送单个状态数据

        Args:
            data: 状态数据
        """
        try:
            # 更新当前用例的状态
            case_id = data['caseId']
            case_status = {
                'status': data['status'],
                'updateTime': data['updateTime'],
                'duration': data['duration'],
                'errorMsg': data['errorMsg'],
                'logInfo': data['logInfo'],
                'reportLinks': data.get('reportLinks', '')
            }

            # 更新当前用例状态和已执行用例记录
            with self._status_lock:
                self._current_case = {
                    'id': case_id,
                    'status': case_status
                }
                # 更新已执行用例状态记录
                if case_status['status'] != Status.RUNNING:  # 只记录最终状态
                    self._executed_cases[case_id] = case_status['status']

            # 计算当前的统计信息
            summary = {
                'passed': sum(1 for status in self._executed_cases.values() if status == Status.PASSED),
                'failed': sum(1 for status in self._executed_cases.values() if status == Status.FAILED),
                'error': sum(1 for status in self._executed_cases.values() if status == Status.ERROR),
                'skipped': sum(1 for status in self._executed_cases.values() if status == Status.SKIPPED),
                'running': 1 if case_status['status'] == Status.RUNNING else 0,
                'notStarted': data.get('totalCases', 1) - len(self._executed_cases) - (1 if case_status['status'] == Status.RUNNING else 0)
            }

            # 构建状态数据（只包含当前用例）
            report_data = {
                'taskInfo': {
                    'taskId': self._task_id,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'totalCases': data.get('totalCases', 1),  # 从传入数据获取总用例数
                    'summary_report': data.get('taskInfo', {}).get('reportLinks', ''),
                    'summary': summary
                },
                'caseResults': {
                    case_id: {
                        'status': case_status['status'],
                        'updateTime': case_status['updateTime'],
                        'duration': case_status['duration'],
                        'errorMsg': case_status['errorMsg'],
                        'logInfo': case_status['logInfo'],
                        'html_report': case_status['reportLinks']
                    }
                }
            }

            # 只打印关键状态信息
            logger.debug(f"上报用例状态: {data['caseId']} -> {data['status']}")

            # 发送数据，设置超时时间为5秒
            self._sync_request('POST', self._api_url, json=report_data, headers=self._headers, timeout=5)

        except Exception as e:
            logger.error(f"发送状态数据失败: {str(e)}")
            # 不抛出异常，让测试继续执行

    async def initialize(self, config: Dict[str, Any]) -> None:
        """初始化状态上报器

        Args:
            config: 状态上报配置，必须包含task_id
        """
        try:
            # 检查是否有task_id
            if not config or 'task_id' not in config:
                self._enabled = False
                logger.info("状态上报未启用")
                return

            self._enabled = True

            # 合并默认配置
            merged_config = DEFAULT_STATUS_REPORT_CONFIG.copy()
            for key, value in config.items():
                if key in merged_config and isinstance(merged_config[key], dict):
                    merged_config[key].update(value)
                else:
                    merged_config[key] = value

            # 设置API URL
            api = merged_config['api']
            base_url = api['base_url'].rstrip('/')
            api['status_url'] = f"{base_url}{api['status']}"

            # 设置认证信息
            auth_config = merged_config.get('auth', {})
            self._username = auth_config.get('username', 'admin')
            self._password = auth_config.get('password', '123456')

            # 设置重试策略
            retry_config = merged_config.get('retry', {})
            self._retry_strategy = Retry(
                total=retry_config.get('total', 3),
                backoff_factor=retry_config.get('backoff_factor', 0.5),
                status_forcelist=retry_config.get('status_forcelist', [500, 502, 503, 504])
            )

            # 设置请求头
            self._headers = merged_config.get('headers', {'Content-Type': 'application/json'})

            # 设置API URL
            self._api_url = api['status_url']
            self._task_id = merged_config['task_id']

            # 创建session并配置重试
            self._session = requests.Session()
            self._session.mount('http://', HTTPAdapter(max_retries=self._retry_strategy))
            self._session.mount('https://', HTTPAdapter(max_retries=self._retry_strategy))

            # 创建异步session，设置超时时间为5秒
            timeout = aiohttp.ClientTimeout(total=5)
            self._async_session = aiohttp.ClientSession(timeout=timeout)

            # 只输出关键配置信息
            logger.info("状态上报已启用")
            logger.debug(f"API地址: {api['base_url']}")
            logger.debug(f"任务ID: {self._task_id}")

        except Exception as e:
            logger.error(f"状态上报初始化失败：{str(e)}")
            self._enabled = False


    async def _batch_report_worker(self):
        """批量上报工作线程"""
        while not self._stop_batch:
            try:
                # 等待指定的批量上报间隔
                await asyncio.sleep(self._batch_interval)

                # 获取待上报的用例状态
                with self._status_lock:
                    if not self._pending_updates:
                        continue

                    # 获取所有待上报用例的最新状态
                    batch_data = []
                    for case_id in self._pending_updates:
                        if case_id in self._case_statuses:
                            batch_data.append(self._case_statuses[case_id])

                    # 清空待上报集合
                    self._pending_updates.clear()

                # 发送批量状态数据
                if batch_data:
                    await self._send_batch(batch_data)

            except Exception as e:
                logger.error(f"批量上报异常: {str(e)}")
                await asyncio.sleep(1)

    async def _send_batch(self, batch: List[Dict[str, Any]]):
        """异步发送批量状态数据

        Args:
            batch: 状态数据列表
        """
        if not batch:
            return

        try:
            # 构建状态上报数据结构
            data = {
                'taskInfo': {
                    'taskId': self._task_id,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'totalCases': len(batch),
                    'summary_report': batch[0].get('taskInfo', {}).get('reportLinks', ''),  # 使用 summary_report
                    'summary': {
                        'passed': sum(1 for item in batch if item['status'] == Status.PASSED),
                        'failed': sum(1 for item in batch if item['status'] == Status.FAILED),
                        'error': sum(1 for item in batch if item['status'] == Status.ERROR),
                        'skipped': sum(1 for item in batch if item['status'] == Status.SKIPPED),
                        'running': sum(1 for item in batch if item['status'] == Status.RUNNING),
                        'notStarted': sum(1 for item in batch if item['status'] == Status.NOT_STARTED)
                    }
                },
                'caseResults': {
                    item['caseId']: {
                        'status': item['status'],
                        'updateTime': item['updateTime'],
                        'duration': item['duration'],
                        'errorMsg': item['errorMsg'],
                        'logInfo': item['logInfo'],
                        'html_report': item.get('reportLinks', '')  # 使用 html_report
                    } for item in batch
                }
            }

            # 打印请求信息
            logger.info(f"\n发送批量请求:")
            logger.info(f"URL: {self._api_url}")
            logger.info(f"Method: POST")
            logger.info(f"Headers: {self._headers}")
            logger.info(f"Request Body: {json.dumps(data, ensure_ascii=False, indent=2)}")

            # 转换为JSON字符串
            json_data = json.dumps(data)

            # 如果数据大于阈值则压缩
            if len(json_data.encode()) > self._compression_threshold:
                compressed = zlib.compress(json_data.encode())
                headers = {**self._headers, 'Content-Encoding': 'gzip'}
                logger.info("数据已压缩")
            else:
                compressed = json_data.encode()
                headers = self._headers

            async with self._async_session.post(
                self._api_url,
                data=compressed,
                headers=headers
            ) as response:
                response.raise_for_status()
                # 打印响应信息
                logger.info(f"\n收到响应:")
                logger.info(f"Status Code: {response.status}")
                try:
                    response_json = await response.json()
                    logger.info(f"Response Body: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
                except:
                    response_text = await response.text()
                    logger.info(f"Response Text: {response_text}")

            logger.info(f"批量上报成功")

        except Exception as e:
            logger.error(f"批量上报失败: {str(e)}")
            # 失败时将数据重新加入待上报集合
            with self._status_lock:
                for item in batch:
                    case_id = item['caseId']
                    self._pending_updates.add(case_id)
    async def report_status(self, case_id: str, status: str, details: Optional[Dict[str, Any]] = None) -> bool:
        """异步上报用例执行状态

        Args:
            case_id: 测试用例ID
            status: 执行状态
            details: 详细信息，可包含：
                - error: 错误信息
                - duration: 执行时长（毫秒）
                - log: 日志信息

        Returns:
            bool: 上报是否成功
        """
        if not self._enabled:
            logger.debug("状态上报已禁用，跳过上报")
            return True

        if not self._task_id:
            logger.warning("未设置任务ID，跳过状态上报")
            return False

        try:
            # 构建状态数据
            data = {
                'caseId': case_id,
                'status': status,
                'updateTime': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'errorMsg': details.get('error', ''),
                'duration': details.get('duration', 0),
                'logInfo': details.get('log', ''),
                'reportLinks': details.get('reportLinks', {})  # 添加报告链接
            }

            if self._batch_enabled:
                # 批量模式：更新状态并加入待上报队列
                with self._status_lock:
                    self._case_statuses[case_id] = data
                    self._pending_updates.add(case_id)
                logger.debug(f"状态数据已加入批量队列: {case_id} -> {status}")
            else:
                # 串行模式：立即上报
                await self._send_single(data)
                logger.debug(f"状态数据已即时上报: {case_id} -> {status}")

            return True

        except Exception as e:
            logger.error(f"状态上报失败: {str(e)}")
            return False

    async def _send_single(self, data: Dict[str, Any]) -> None:
        """发送单个状态数据

        Args:
            data: 状态数据
        """
        try:
            # 构建完整的状态数据
            report_data = {
                'taskInfo': {
                    'taskId': self._task_id,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'totalCases': 1,
                    'summary_report': data.get('taskInfo', {}).get('reportLinks', ''),  # 使用 summary_report
                },
                'caseResults': {
                    data['caseId']: {
                        'status': data['status'],
                        'updateTime': data['updateTime'],
                        'duration': data['duration'],
                        'errorMsg': data['errorMsg'],
                        'logInfo': data['logInfo'],
                        'html_report': data.get('reportLinks', '')  # 使用 html_report
                    }
                }
            }

            # 打印请求信息
            logger.info(f"\n发送请求:")
            logger.info(f"URL: {self._api_url}")
            logger.info(f"Method: POST")
            logger.info(f"Headers: {self._headers}")
            logger.info(f"Request Body: {json.dumps(report_data, ensure_ascii=False, indent=2)}")

            # 发送数据
            async with self._async_session.post(
                self._api_url,
                json=report_data,
                headers=self._headers
            ) as response:
                response.raise_for_status()
                # 打印响应信息
                logger.info(f"\n收到响应:")
                logger.info(f"Status Code: {response.status}")
                try:
                    response_json = await response.json()
                    logger.info(f"Response Body: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
                except:
                    response_text = await response.text()
                    logger.info(f"Response Text: {response_text}")

        except Exception as e:
            logger.error(f"发送状态数据失败: {str(e)}")

    async def close(self):
        """关闭状态上报器"""
        self._stop_batch = True

        # 等待最后一次批量上报完成
        if self._batch_task:
            try:
                await asyncio.wait_for(self._batch_task, timeout=5)
            except asyncio.TimeoutError:
                logger.warning("等待批量上报任务完成超时")

        if self._async_session:
            await self._async_session.close()

        if self._session:
            self._session.close()

        self._thread_pool.shutdown(wait=False)
        self._enabled = False

        # 不在这里关闭事件循环
        logger.info("状态上报器已关闭")

    def close_sync(self):
        """同步关闭状态上报器"""
        self._enabled = False
        logger.info("状态上报器已关闭")
