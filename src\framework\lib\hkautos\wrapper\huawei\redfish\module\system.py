#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 系统管理模块

提供系统管理相关功能:
- 系统资源配置
- 处理器管理
- 内存管理
- 存储管理
- 网络管理
- 电源管理
- 散热管理
- 启动管理

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""
from typing import Dict, Any, List


def redfish_get_systems(self) -> List[Dict[str, Any]]:
    """
    获取系统列表
    
    URL: /redfish/v1/Systems
    请求方式: GET
    """
    systems = self.get_resource("/redfish/v1/Systems")
    return systems.data.get("Members", [])


def redfish_get_system(self) -> Dict[str, Any]:
    """
    获取系统信息
    
    URL: /redfish/v1/Systems/{system_id}
    请求方式: GET
    """
    system_id = self.system_id
    return self.get_resource(f"/redfish/v1/Systems/{system_id}").data


def redfish_reset_system(self, params: Dict[str, Any]) -> None:
    """
    重置系统
    
    URL: /redfish/v1/Systems/{system_id}/Actions/ComputerSystem.Reset
    请求方式: POST
    
    参数:
        params: 参数字典，常用字段:
            - ResetType: 重置类型(On/ForceOff/GracefulShutdown/ForceRestart/Nmi等)
    """
    system_id = self.system_id
    rest_result = self.post(
        f"/redfish/v1/Systems/{system_id}/Actions/ComputerSystem.Reset",
        params
    )
    return rest_result.get('error')


def redfish_get_processors(self) -> List[Dict[str, Any]]:
    """
    获取处理器列表
    
    URL: /redfish/v1/Systems/{system_id}/Processors/X
    请求方式: GET
    """
    result = []
    system_id = self.system_id
    processors = self.get_resource(f"/redfish/v1/Systems/{system_id}/Processors")
    for member in processors.data.get("Members"):
        result.append(self.get_resource(member['@odata.id']).data)
    return result


def redfish_get_memory(self) -> List[Dict[str, Any]]:
    """
    获取内存列表
    
    URL: /redfish/v1/Systems/{system_id}/Memory
    请求方式: GET
    """
    result = []
    system_id = self.system_id
    memory = self.get_resource(f"/redfish/v1/Systems/{system_id}/Memory")
    for member in memory.data.get("Members"):
        result.append(self.get_resource(member['@odata.id']).data)
    return result


def redfish_get_storage(self) -> List[Dict[str, Any]]:
    """
    获取存储列表
    
    URL: /redfish/v1/Systems/{system_id}/Storage
    请求方式: GET
    """
    system_id = self.system_id
    storage = self.get_resource(f"/redfish/v1/Systems/{system_id}/Storage")
    return storage.data.get("Members", [])


def redfish_get_ethernet_interfaces(self) -> List[Dict[str, Any]]:
    """
    获取网络接口列表
    
    URL: /redfish/v1/Systems/{system_id}/EthernetInterfaces
    请求方式: GET
    """
    system_id = self.system_id
    interfaces = self.get_resource(
        f"/redfish/v1/Systems/{system_id}/EthernetInterfaces"
    )
    return interfaces.data.get("Members", [])


def redfish_get_power(self) -> Dict[str, Any]:
    """
    获取电源信息
    
    URL: /redfish/v1/Systems/{system_id}/Power
    请求方式: GET
    """
    system_id = self.system_id
    return self.get_resource(f"/redfish/v1/Systems/{system_id}/Power").data


def redfish_get_boot(self) -> Dict[str, Any]:
    """
    获取启动配置
    
    URL: /redfish/v1/Systems/{system_id}/Boot
    请求方式: GET
    """
    system_id = self.system_id
    return self.get_resource(f"/redfish/v1/Systems/{system_id}/Boot").data


def redfish_set_boot(self, params: Dict[str, Any]) -> None:
    """
    设置启动配置
    
    URL: /redfish/v1/Systems/{system_id}
    请求方式: PATCH
    
    参数:
        params: 参数字典，常用字段:
            - BootSourceOverrideEnabled: 启动覆盖使能(Once/Continuous/Disabled)
            - BootSourceOverrideTarget: 启动源(None/Pxe/Floppy/Cd/Usb/Hdd等)
            - BootSourceOverrideMode: 启动模式(UEFI/Legacy)
    """
    system_id = self.system_id
    self.patch(f"/redfish/v1/Systems/{system_id}", params)


def redfish_systems_query_log_service_collection_information(self) -> Dict[str, Any]:
    """查询日志服务集合资源信息"""
    system_id = self.system_id
    return self.get_resource(f"/redfish/v1/Systems/{system_id}/LogServices").data


def redfish_systems_query_log_service(self, params: Dict[str, Any]) -> None:
    """查询指定日志服务资源信息"""
    system_id = self.system_id
    logservices_id = params.get("logservices_id")
    return self.get_resource(f"/redfish/v1/Systems/{system_id}/LogServices/{logservices_id}").data


def redfish_mock_alarm(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """模拟精确告警"""
    return self.post(
        "/redfish/v1/EventService/Actions/Oem/Huawei/EventService.MockPreciseAlarm",
        params
    )


def redfish_get_sel(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """查询sel日志"""
    system_id = self.system_id
    custom_id = self.oem_vendor
    log_services_id = params.get("log_services_id")
    del params["log_services_id"]
    return self.post(
        f"/redfish/v1/Systems/{system_id}/LogServices/{log_services_id}/Actions/Oem/{custom_id}/LogService.QuerySelLogEntries",
        params
    )


def redfish_clear_sel(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """清除sel日志"""
    system_id = self.system_id
    log_services_id = params.get("log_services_id")
    # 请求需要至少要传一个空json
    params = {}
    return self.post(
        f"/redfish/v1/Systems/{system_id}/LogServices/{log_services_id}/Actions/LogService.ClearLog", params
    )


def redfish_systems_query_system_id(self):
    """查询指定系统资源信息

    Args:
        system_id: 系统资源的ID
    """
    system_id = self.system_id
    return self.get_resource(
        f"/redfish/v1/Systems/{system_id}"
    ).data


def redfish_systems_query_system(self):
    """查询系统集合资源信息"""
    return self.get_resource(
        f"/redfish/v1/Systems"
    ).data


