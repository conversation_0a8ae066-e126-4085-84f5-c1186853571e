# HKAutoTest 2.0 项目结构设计

## 整体项目结构

```
hktest-framework/
├── README.md                           # 项目说明文档
├── CHANGELOG.md                        # 版本变更日志
├── LICENSE                             # 开源许可证
├── pyproject.toml                      # Python项目配置
├── poetry.lock                         # 依赖锁定文件
├── Dockerfile                          # Docker构建文件
├── Dockerfile.executor                 # 执行器Docker文件
├── docker-compose.yml                  # Docker Compose配置
├── .github/                            # GitHub Actions配置
│   └── workflows/
│       ├── ci.yml                      # 持续集成
│       ├── cd.yml                      # 持续部署
│       └── security.yml                # 安全扫描
├── .gitignore                          # Git忽略文件
├── .pre-commit-config.yaml             # 预提交钩子配置
└── requirements/                       # 依赖管理
    ├── base.txt                        # 基础依赖
    ├── dev.txt                         # 开发依赖
    └── prod.txt                        # 生产依赖

## 核心框架代码结构

```
hktest/                                 # 框架核心包
├── __init__.py                         # 包初始化
├── version.py                          # 版本信息
├── main.py                             # 主入口点
├── config.py                           # 全局配置
├── exceptions.py                       # 异常定义
└── constants.py                        # 常量定义

### 核心抽象层
hktest/core/                           # 核心抽象
├── __init__.py
├── interfaces/                         # 接口定义
│   ├── __init__.py
│   ├── device_interface.py             # 设备接口
│   ├── test_interface.py               # 测试接口
│   ├── execution_interface.py          # 执行接口
│   ├── repository_interface.py         # 仓储接口
│   └── plugin_interface.py             # 插件接口
├── models/                             # 领域模型
│   ├── __init__.py
│   ├── device.py                       # 设备模型
│   ├── test_case.py                    # 测试用例模型
│   ├── test_result.py                  # 测试结果模型
│   ├── execution.py                    # 执行模型
│   └── user.py                         # 用户模型
├── exceptions/                         # 异常体系
│   ├── __init__.py
│   ├── base.py                         # 基础异常
│   ├── device_exceptions.py            # 设备异常
│   ├── test_exceptions.py              # 测试异常
│   └── security_exceptions.py          # 安全异常
├── events/                             # 事件系统
│   ├── __init__.py
│   ├── event_bus.py                    # 事件总线
│   ├── event_handler.py                # 事件处理器
│   └── domain_events.py                # 领域事件
└── enums/                              # 枚举定义
    ├── __init__.py
    ├── device_types.py                 # 设备类型
    ├── test_status.py                  # 测试状态
    └── execution_status.py             # 执行状态

### 应用服务层
hktest/services/                       # 应用服务
├── __init__.py
├── test_service.py                     # 测试服务
├── device_service.py                   # 设备服务
├── execution_service.py                # 执行服务
├── report_service.py                   # 报告服务
├── ai_service.py                       # AI智能服务
├── notification_service.py             # 通知服务
└── analytics_service.py                # 分析服务

### 领域层
hktest/domain/                         # 领域层
├── __init__.py
├── test/                               # 测试域
│   ├── __init__.py
│   ├── entities/                       # 实体
│   │   ├── test_case.py                # 测试用例实体
│   │   ├── test_suite.py               # 测试套件实体
│   │   └── test_step.py                # 测试步骤实体
│   ├── value_objects/                  # 值对象
│   │   ├── test_criteria.py            # 测试标准
│   │   ├── test_parameters.py          # 测试参数
│   │   └── test_configuration.py       # 测试配置
│   ├── repositories/                   # 仓储接口
│   │   └── test_repository.py          # 测试仓储接口
│   └── services/                       # 领域服务
│       ├── test_validator.py           # 测试验证服务
│       └── test_scheduler.py           # 测试调度服务
├── device/                             # 设备域
│   ├── __init__.py
│   ├── entities/                       # 实体
│   │   ├── device.py                   # 设备实体
│   │   ├── device_pool.py              # 设备池实体
│   │   └── connection.py               # 连接实体
│   ├── value_objects/                  # 值对象
│   │   ├── connection_info.py          # 连接信息
│   │   ├── device_capabilities.py      # 设备能力
│   │   └── device_status.py            # 设备状态
│   ├── repositories/                   # 仓储接口
│   │   └── device_repository.py        # 设备仓储接口
│   └── services/                       # 领域服务
│       ├── device_discovery.py         # 设备发现服务
│       └── connection_manager.py       # 连接管理服务
└── execution/                          # 执行域
    ├── __init__.py
    ├── entities/                       # 实体
    │   ├── test_execution.py           # 测试执行实体
    │   ├── execution_plan.py           # 执行计划实体
    │   └── execution_context.py        # 执行上下文实体
    ├── value_objects/                  # 值对象
    │   ├── execution_result.py         # 执行结果
    │   ├── execution_metrics.py        # 执行指标
    │   └── resource_allocation.py      # 资源分配
    ├── repositories/                   # 仓储接口
    │   └── execution_repository.py     # 执行仓储接口
    └── services/                       # 领域服务
        ├── execution_engine.py         # 执行引擎
        └── resource_scheduler.py       # 资源调度器

### 基础设施层
hktest/infrastructure/                 # 基础设施
├── __init__.py
├── config/                            # 配置管理
│   ├── __init__.py
│   ├── settings.py                    # 设置管理
│   ├── environment.py                 # 环境配置
│   └── validation.py                  # 配置验证
├── database/                          # 数据库
│   ├── __init__.py
│   ├── connection.py                  # 数据库连接
│   ├── session.py                     # 会话管理
│   ├── migrations/                    # 数据库迁移
│   └── repositories/                  # 仓储实现
│       ├── __init__.py
│       ├── test_repository_impl.py    # 测试仓储实现
│       ├── device_repository_impl.py  # 设备仓储实现
│       └── execution_repository_impl.py # 执行仓储实现
├── cache/                             # 缓存系统
│   ├── __init__.py
│   ├── redis_cache.py                 # Redis缓存
│   ├── memory_cache.py                # 内存缓存
│   └── multi_level_cache.py           # 多级缓存
├── messaging/                         # 消息系统
│   ├── __init__.py
│   ├── kafka_producer.py              # Kafka生产者
│   ├── kafka_consumer.py              # Kafka消费者
│   ├── redis_pubsub.py                # Redis发布订阅
│   └── event_dispatcher.py            # 事件分发器
├── storage/                           # 存储系统
│   ├── __init__.py
│   ├── file_storage.py                # 文件存储
│   ├── s3_storage.py                  # S3存储
│   └── binary_storage.py              # 二进制存储
├── security/                          # 安全模块
│   ├── __init__.py
│   ├── authentication.py              # 认证
│   ├── authorization.py               # 授权
│   ├── encryption.py                  # 加密
│   ├── audit.py                       # 审计
│   └── compliance.py                  # 合规性
├── monitoring/                        # 监控系统
│   ├── __init__.py
│   ├── metrics.py                     # 指标收集
│   ├── logging.py                     # 日志系统
│   ├── tracing.py                     # 链路追踪
│   └── health_check.py                # 健康检查
└── plugins/                           # 插件系统
    ├── __init__.py
    ├── plugin_manager.py              # 插件管理器
    ├── plugin_loader.py               # 插件加载器
    ├── plugin_registry.py             # 插件注册表
    └── plugin_sandbox.py              # 插件沙箱

### 适配器层
hktest/adapters/                       # 适配器
├── __init__.py
├── protocols/                         # 协议适配器
│   ├── __init__.py
│   ├── ssh_adapter.py                 # SSH适配器
│   ├── telnet_adapter.py              # Telnet适配器
│   ├── redfish_adapter.py             # Redfish适配器
│   ├── ipmi_adapter.py                # IPMI适配器
│   ├── snmp_adapter.py                # SNMP适配器
│   └── web_adapter.py                 # Web适配器
├── vendors/                           # 厂商适配器
│   ├── __init__.py
│   ├── huawei/                        # 华为适配器
│   │   ├── __init__.py
│   │   ├── bmc_adapter.py             # BMC适配器
│   │   ├── server_adapter.py          # 服务器适配器
│   │   └── switch_adapter.py          # 交换机适配器
│   ├── dell/                          # 戴尔适配器
│   │   ├── __init__.py
│   │   ├── idrac_adapter.py           # iDRAC适配器
│   │   └── server_adapter.py          # 服务器适配器
│   ├── hpe/                           # HPE适配器
│   │   ├── __init__.py
│   │   ├── ilo_adapter.py             # iLO适配器
│   │   └── server_adapter.py          # 服务器适配器
│   └── supermicro/                    # 超微适配器
│       ├── __init__.py
│       └── bmc_adapter.py             # BMC适配器
├── frameworks/                        # 测试框架适配器
│   ├── __init__.py
│   ├── pytest_adapter.py              # pytest适配器
│   ├── unittest_adapter.py            # unittest适配器
│   └── robot_adapter.py               # Robot Framework适配器
└── external/                          # 外部系统适配器
    ├── __init__.py
    ├── jenkins_adapter.py             # Jenkins适配器
    ├── gitlab_adapter.py              # GitLab适配器
    └── jira_adapter.py                # Jira适配器

### AI智能化模块
hktest/ai/                             # AI模块
├── __init__.py
├── test_generation/                   # 测试生成
│   ├── __init__.py
│   ├── llm_generator.py               # LLM生成器
│   ├── template_engine.py             # 模板引擎
│   ├── knowledge_base.py              # 知识库
│   └── requirements_analyzer.py       # 需求分析器
├── diagnostics/                       # 智能诊断
│   ├── __init__.py
│   ├── diagnostic_engine.py           # 诊断引擎
│   ├── anomaly_detector.py            # 异常检测
│   ├── symptom_analyzer.py            # 症状分析器
│   ├── root_cause_analyzer.py         # 根因分析器
│   └── repair_advisor.py              # 修复建议器
├── optimization/                      # 性能优化
│   ├── __init__.py
│   ├── performance_analyzer.py        # 性能分析器
│   ├── resource_optimizer.py          # 资源优化器
│   ├── execution_planner.py           # 执行规划器
│   └── intelligent_scheduler.py       # 智能调度器
├── models/                            # ML模型
│   ├── __init__.py
│   ├── duration_predictor.py          # 时长预测模型
│   ├── failure_classifier.py          # 故障分类模型
│   ├── anomaly_detector.py            # 异常检测模型
│   └── performance_predictor.py       # 性能预测模型
└── training/                          # 模型训练
    ├── __init__.py
    ├── data_preprocessor.py           # 数据预处理
    ├── feature_extractor.py           # 特征提取
    ├── model_trainer.py               # 模型训练器
    └── model_evaluator.py             # 模型评估器

### 工具库
hktest/utils/                          # 工具函数
├── __init__.py
├── common.py                          # 通用工具
├── decorators.py                      # 装饰器
├── validators.py                      # 验证器
├── formatters.py                      # 格式化器
├── crypto.py                          # 加密工具
├── network.py                         # 网络工具
├── file_utils.py                      # 文件工具
├── time_utils.py                      # 时间工具
└── string_utils.py                    # 字符串工具

## API服务器结构

```
hktest/server/                         # API服务器
├── __init__.py
├── main.py                            # FastAPI应用入口
├── api/                               # API路由
│   ├── __init__.py
│   ├── v1/                            # API v1版本
│   │   ├── __init__.py
│   │   ├── test_cases.py              # 测试用例API
│   │   ├── devices.py                 # 设备API
│   │   ├── executions.py              # 执行API
│   │   ├── reports.py                 # 报告API
│   │   └── analytics.py               # 分析API
│   └── middleware/                    # 中间件
│       ├── __init__.py
│       ├── auth.py                    # 认证中间件
│       ├── cors.py                    # CORS中间件
│       ├── logging.py                 # 日志中间件
│       └── rate_limit.py              # 限流中间件
├── schemas/                           # API模式
│   ├── __init__.py
│   ├── test_case.py                   # 测试用例模式
│   ├── device.py                      # 设备模式
│   ├── execution.py                   # 执行模式
│   └── common.py                      # 通用模式
├── dependencies/                      # 依赖注入
│   ├── __init__.py
│   ├── auth.py                        # 认证依赖
│   ├── database.py                    # 数据库依赖
│   └── services.py                    # 服务依赖
└── websocket/                         # WebSocket
    ├── __init__.py
    ├── connection_manager.py          # 连接管理
    ├── execution_updates.py           # 执行更新
    └── real_time_monitoring.py        # 实时监控

## CLI工具结构

```
hktest/cli/                            # 命令行工具
├── __init__.py
├── main.py                            # CLI主入口
├── commands/                          # 命令模块
│   ├── __init__.py
│   ├── test.py                        # 测试命令
│   ├── device.py                      # 设备命令
│   ├── config.py                      # 配置命令
│   ├── plugin.py                      # 插件命令
│   ├── server.py                      # 服务器命令
│   └── init.py                        # 初始化命令
├── utils/                             # CLI工具
│   ├── __init__.py
│   ├── console.py                     # 控制台工具
│   ├── progress.py                    # 进度条
│   └── table.py                       # 表格显示
└── templates/                         # 项目模板
    ├── basic/                         # 基础模板
    ├── advanced/                      # 高级模板
    └── custom/                        # 自定义模板

## 测试代码结构

```
tests/                                 # 测试代码
├── __init__.py
├── conftest.py                        # pytest配置
├── unit/                              # 单元测试
│   ├── __init__.py
│   ├── core/                          # 核心模块测试
│   ├── services/                      # 服务测试
│   ├── domain/                        # 领域测试
│   ├── infrastructure/                # 基础设施测试
│   └── adapters/                      # 适配器测试
├── integration/                       # 集成测试
│   ├── __init__.py
│   ├── api/                           # API集成测试
│   ├── database/                      # 数据库集成测试
│   ├── messaging/                     # 消息系统集成测试
│   └── external/                      # 外部系统集成测试
├── e2e/                               # 端到端测试
│   ├── __init__.py
│   ├── test_scenarios/                # 测试场景
│   ├── test_workflows/                # 测试工作流
│   └── performance/                   # 性能测试
├── fixtures/                          # 测试夹具
│   ├── __init__.py
│   ├── devices.py                     # 设备夹具
│   ├── test_cases.py                  # 测试用例夹具
│   └── data/                          # 测试数据
│       ├── devices.json               # 设备数据
│       ├── test_cases.json            # 测试用例数据
│       └── configurations.yaml        # 配置数据
└── mocks/                             # Mock对象
    ├── __init__.py
    ├── device_mocks.py                # 设备Mock
    ├── service_mocks.py               # 服务Mock
    └── external_mocks.py              # 外部系统Mock

## 示例代码结构

```
examples/                              # 示例代码
├── README.md                          # 示例说明
├── basic/                             # 基础示例
│   ├── simple_test.py                 # 简单测试示例
│   ├── device_connection.py           # 设备连接示例
│   └── test_execution.py              # 测试执行示例
├── advanced/                          # 高级示例
│   ├── custom_plugin.py               # 自定义插件示例
│   ├── ai_test_generation.py          # AI测试生成示例
│   ├── performance_optimization.py    # 性能优化示例
│   └── distributed_testing.py         # 分布式测试示例
├── integrations/                      # 集成示例
│   ├── jenkins_integration.py         # Jenkins集成示例
│   ├── gitlab_ci.yml                  # GitLab CI示例
│   └── docker_deployment/             # Docker部署示例
└── tutorials/                         # 教程代码
    ├── getting_started.py             # 快速开始
    ├── writing_tests.py               # 编写测试
    ├── custom_adapters.py             # 自定义适配器
    └── plugin_development.py          # 插件开发

## 文档结构

```
docs/                                  # 文档
├── README.md                          # 文档说明
├── index.md                           # 文档首页
├── getting-started/                   # 快速开始
│   ├── installation.md                # 安装指南
│   ├── quickstart.md                  # 快速开始
│   └── first-test.md                  # 第一个测试
├── user-guide/                        # 用户指南
│   ├── overview.md                    # 概览
│   ├── test-cases.md                  # 测试用例
│   ├── devices.md                     # 设备管理
│   ├── execution.md                   # 测试执行
│   ├── reports.md                     # 报告系统
│   └── configuration.md               # 配置管理
├── developer-guide/                   # 开发者指南
│   ├── architecture.md                # 架构说明
│   ├── contributing.md                # 贡献指南
│   ├── plugin-development.md          # 插件开发
│   ├── api-reference.md               # API参考
│   └── extending.md                   # 扩展框架
├── deployment/                        # 部署指南
│   ├── docker.md                      # Docker部署
│   ├── kubernetes.md                  # Kubernetes部署
│   ├── cloud.md                       # 云部署
│   └── monitoring.md                  # 监控配置
├── reference/                         # 参考文档
│   ├── api/                           # API文档
│   ├── cli.md                         # CLI参考
│   ├── configuration.md               # 配置参考
│   └── troubleshooting.md             # 故障排除
└── tutorials/                         # 教程
    ├── basic-usage.md                 # 基础使用
    ├── advanced-features.md           # 高级功能
    ├── best-practices.md              # 最佳实践
    └── migration.md                   # 迁移指南

## 配置文件结构

```
configs/                               # 配置文件
├── default.yaml                       # 默认配置
├── development.yaml                   # 开发环境配置
├── testing.yaml                       # 测试环境配置
├── staging.yaml                       # 预发布环境配置
├── production.yaml                    # 生产环境配置
├── logging.yaml                       # 日志配置
├── security.yaml                      # 安全配置
└── plugins/                           # 插件配置
    ├── huawei.yaml                    # 华为插件配置
    ├── dell.yaml                      # 戴尔插件配置
    └── custom.yaml                    # 自定义插件配置

## 部署配置结构

```
deployment/                            # 部署配置
├── docker/                            # Docker配置
│   ├── Dockerfile                     # 主Dockerfile
│   ├── docker-compose.yml             # Compose配置
│   ├── docker-compose.dev.yml         # 开发环境
│   └── docker-compose.prod.yml        # 生产环境
├── kubernetes/                        # Kubernetes配置
│   ├── namespace.yaml                 # 命名空间
│   ├── configmap.yaml                 # 配置映射
│   ├── secret.yaml                    # 密钥
│   ├── deployment.yaml                # 部署
│   ├── service.yaml                   # 服务
│   ├── ingress.yaml                   # Ingress
│   └── hpa.yaml                       # 自动扩展
├── helm/                              # Helm图表
│   ├── Chart.yaml                     # 图表元数据
│   ├── values.yaml                    # 默认值
│   ├── values-dev.yaml                # 开发环境值
│   ├── values-prod.yaml               # 生产环境值
│   └── templates/                     # 模板文件
├── terraform/                         # Terraform配置
│   ├── main.tf                        # 主配置
│   ├── variables.tf                   # 变量定义
│   ├── outputs.tf                     # 输出定义
│   └── modules/                       # 模块
└── ansible/                           # Ansible配置
    ├── playbook.yml                   # 主剧本
    ├── inventory/                     # 清单
    └── roles/                         # 角色

## 监控配置结构

```
monitoring/                            # 监控配置
├── prometheus/                        # Prometheus配置
│   ├── prometheus.yml                 # 主配置
│   ├── rules/                         # 告警规则
│   └── alerts/                        # 告警配置
├── grafana/                           # Grafana配置
│   ├── dashboards/                    # 仪表板
│   ├── datasources/                   # 数据源
│   └── provisioning/                  # 配置供应
├── elasticsearch/                     # Elasticsearch配置
│   ├── elasticsearch.yml              # 主配置
│   ├── mappings/                      # 索引映射
│   └── templates/                     # 索引模板
└── kibana/                            # Kibana配置
    ├── kibana.yml                     # 主配置
    ├── dashboards/                    # 仪表板
    └── visualizations/                # 可视化

## 插件开发结构

```
plugins/                               # 插件目录
├── official/                          # 官方插件
│   ├── huawei-bmc/                    # 华为BMC插件
│   │   ├── plugin.yaml                # 插件清单
│   │   ├── __init__.py                # 初始化
│   │   ├── adapter.py                 # 适配器实现
│   │   ├── commands.py                # 命令定义
│   │   ├── tests/                     # 插件测试
│   │   └── docs/                      # 插件文档
│   ├── dell-idrac/                    # Dell iDRAC插件
│   └── hpe-ilo/                       # HPE iLO插件
├── community/                         # 社区插件
├── custom/                            # 自定义插件
└── templates/                         # 插件模板
    ├── protocol-adapter/              # 协议适配器模板
    ├── vendor-driver/                 # 厂商驱动模板
    ├── test-runner/                   # 测试运行器模板
    └── report-generator/              # 报告生成器模板

## 工具脚本结构

```
scripts/                               # 工具脚本
├── setup/                             # 安装脚本
│   ├── install.sh                     # Linux安装脚本
│   ├── install.bat                    # Windows安装脚本
│   └── requirements.sh                # 依赖安装脚本
├── build/                             #构建脚本
│   ├── build.sh                       # 构建脚本
│   ├── package.sh                     # 打包脚本
│   └── release.sh                     # 发布脚本
├── development/                       # 开发脚本
│   ├── dev-setup.sh                   # 开发环境设置
│   ├── run-tests.sh                   # 运行测试
│   ├── lint.sh                        # 代码检查
│   └── format.sh                      # 代码格式化
├── deployment/                        # 部署脚本
│   ├── deploy.sh                      # 部署脚本
│   ├── rollback.sh                    # 回滚脚本
│   └── health-check.sh                # 健康检查
├── maintenance/                       # 维护脚本
│   ├── backup.sh                      # 备份脚本
│   ├── cleanup.sh                     # 清理脚本
│   └── migrate.sh                     # 数据迁移
└── utilities/                         # 实用工具
    ├── generate-docs.sh               # 生成文档
    ├── update-deps.sh                 # 更新依赖
    └── security-scan.sh               # 安全扫描

## 数据目录结构

```
data/                                  # 数据目录
├── databases/                         # 数据库文件
├── logs/                              # 日志文件
│   ├── application/                   # 应用日志
│   ├── access/                        # 访问日志
│   ├── error/                         # 错误日志
│   └── audit/                         # 审计日志
├── reports/                           # 报告文件
│   ├── html/                          # HTML报告
│   ├── json/                          # JSON报告
│   └── pdf/                           # PDF报告
├── cache/                             # 缓存文件
├── temp/                              # 临时文件
└── artifacts/                         # 构建产物
    ├── screenshots/                   # 截图
    ├── videos/                        # 视频
    └── traces/                        # 跟踪文件
```

## 项目特点总结

### 1. 分层架构清晰
- **表现层**: API服务器、CLI工具、Web界面
- **应用层**: 业务服务、应用逻辑
- **领域层**: 核心业务逻辑、领域模型
- **基础设施层**: 数据访问、外部系统集成

### 2. 模块化设计
- **核心框架**: 提供基础功能和抽象
- **插件系统**: 支持扩展和定制
- **适配器模式**: 支持多种协议和厂商
- **微服务架构**: 支持分布式部署

### 3. 现代化技术栈
- **Python 3.11+**: 现代Python特性
- **FastAPI**: 高性能API框架
- **React + TypeScript**: 现代前端技术
- **Docker + Kubernetes**: 容器化部署
- **AI/ML集成**: 智能化测试

### 4. 完善的工程实践
- **测试覆盖**: 单元测试、集成测试、E2E测试
- **CI/CD**: 自动化构建、测试、部署
- **代码质量**: 静态分析、格式化、安全扫描
- **文档完善**: API文档、用户指南、开发指南

这个项目结构设计体现了现代软件架构的最佳实践，支持高可扩展性、高可维护性，并为未来的功能扩展提供了良好的基础。