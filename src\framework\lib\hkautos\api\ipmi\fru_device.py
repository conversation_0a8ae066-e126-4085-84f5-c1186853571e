"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class FRUDevice(ApiBase):
    def __init__(self):
        super(FRUDevice, self).__init__()

    def get_fru_info(self, params=None):
        """
        获取FRU信息

        Args:
            params: 参数字典
                   例如: {'id': '0'}

        Returns:
            dict: 返回解析后的FRU信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_fru_print", params=params)[0]["parser"]

    def read_fru_data(self, params=None):
        """
        读取FRU数据到文件

        Args:
            params: 参数字典
                   例如: {'id': '0', 'filename': 'fru.bin'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_fru_read", params=params)[0]["parser"]

    def write_fru_data(self, params=None):
        """
        从文件写入FRU数据

        Args:
            params: 参数字典
                   例如: {'id': '0', 'filename': 'fru.bin'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_fru_write", params=params)[0]["parser"]

    def edit_fru_field(self, params=None):
        """
        编辑FRU字段

        Args:
            params: 参数字典
                   例如: {'id': '0', 'field': 'board_serial', 'value': '123456'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_fru_edit", params=params)[0]["parser"]

    def get_fru_size(self, params=None):
        """
        查询对应fru id的区域大小

        Args:
            params: 参数字典
                   例如: {'id': '0'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_fru_size", params=params)[0]["parser"]

    def get_fru_info_raw(self, params=None):
        """
        获取FRU信息

        Args:
            params: 参数字典
                   例如: {'id': '0'}
                   fru_offset : 偏移量
                   bytes_num  : 字节大小

        Returns:
            dict: 返回解析后的FRU信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_fru_info", params=params)[0]["parser"]

    def set_fru(self, params=None):
        """
        写入FRU数据

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_fru", params=params)[0]["parser"]

    def get_fru(self, params=None):
        """
        读取FRU数据

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("get_fru", params=params)[0]["parser"]
