"""Allure报告实现"""
import json
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import allure
from allure_commons.types import AttachmentType

from ..base import BaseReporter


@BaseReporter.register("allure")
class AllureReporter(BaseReporter):
    """Allure报告生成器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.current_test_uuid = None

    def start_test(self, case_info: Dict[str, Any]) -> None:
        """开始测试用例"""
        processed_data = self.processor.process_case(case_info)
        self._handle_case_start(processed_data)

    def end_test(self, case_result: Dict[str, Any]) -> None:
        """结束测试用例"""
        processed_data = self.processor.process_case(case_result)
        self._handle_case_end(processed_data)

    def add_log(self, log_entry: Dict[str, Any]) -> None:
        """添加日志记录"""
        processed_log = self.processor.process_log(log_entry)
        self._handle_log(processed_log)

    def _handle_case_start(self, case_data: Dict[str, Any]) -> None:
        """处理用例开始"""
        self.current_case = case_data
        self.current_test_uuid = str(uuid.uuid4())
        
        # 创建测试结果文件
        result = {
            "uuid": self.current_test_uuid,
            "historyId": str(uuid.uuid4()),
            "name": case_data.get("name", "Unknown Case"),
            "fullName": case_data.get("name", "Unknown Case"),
            "status": "passed",
            "start": int(datetime.now().timestamp() * 1000),
            "labels": [
                {"name": "suite", "value": case_data.get("suite", "Default Suite")},
                {"name": "package", "value": case_data.get("package", "")},
            ],
            "steps": [],
            "attachments": []
        }
        
        self._save_test_result(result)

    def _handle_case_end(self, case_data: Dict[str, Any]) -> None:
        """处理用例结束"""
        if not self.current_test_uuid:
            return

        try:
            result = self._load_test_result()
            if not result:
                return

            # 更新状态和结束时间
            result["status"] = case_data.get("status", "broken")
            result["stop"] = int(datetime.now().timestamp() * 1000)

            # 如果有错误信息,添加为附件
            if error := case_data.get("error"):
                error_uuid = str(uuid.uuid4())
                self._save_attachment(error_uuid, error)
                result["attachments"].append({
                    "name": "Error Details",
                    "source": f"{error_uuid}-attachment.txt",
                    "type": "text/plain"
                })

            # 保存最终结果
            self._save_test_result(result)

        finally:
            self.current_test_uuid = None
            self.current_case = None

    def _handle_log(self, log_data: Dict[str, Any]) -> None:
        """处理日志"""
        if not self.current_test_uuid:
            return

        # 将日志作为步骤添加
        with allure.step(log_data.get("message", "")):
            level = log_data.get("level", "INFO")
            if level in ["ERROR", "CRITICAL"]:
                allure.attach(
                    log_data.get("message", ""),
                    "Error Log",
                    AttachmentType.TEXT
                )

    def generate(self) -> None:
        """生成Allure报告"""
        if not self.report_dir:
            return

        try:
            # 使用allure命令生成报告
            import subprocess
            import platform
            
            allure_cmd = "allure.bat" if platform.system() == "Windows" else "allure"
            output_dir = self.report_dir / "html"
            
            subprocess.run(
                [
                    allure_cmd, "generate",
                    str(self.report_dir),
                    "-o", str(output_dir),
                    "--clean"
                ],
                check=True,
                capture_output=True
            )
            
        except Exception as e:
            print(f"Failed to generate Allure report: {e}")

    def _save_test_result(self, result: Dict[str, Any]) -> None:
        """保存测试结果文件"""
        if not self.report_dir or not self.current_test_uuid:
            return
            
        result_file = self.report_dir / f"{self.current_test_uuid}-result.json"
        with result_file.open("w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

    def _load_test_result(self) -> Optional[Dict[str, Any]]:
        """加载测试结果文件"""
        if not self.report_dir or not self.current_test_uuid:
            return None
            
        result_file = self.report_dir / f"{self.current_test_uuid}-result.json"
        if result_file.exists():
            with result_file.open("r", encoding="utf-8") as f:
                return json.load(f)
        return None

    def _save_attachment(self, attachment_uuid: str, content: str) -> None:
        """保存附件文件"""
        if not self.report_dir:
            return
            
        attachment_file = self.report_dir / f"{attachment_uuid}-attachment.txt"
        attachment_file.write_text(content, encoding="utf-8") 