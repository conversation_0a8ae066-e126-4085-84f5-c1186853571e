import signal
from abc import ABC, abstractmethod
from contextlib import contextmanager
from typing import Any, Dict, List

from ..adapters.base import TestFrameworkAdapter
from ..core.config import EngineConfig
from ..utils.exceptions import RunnerError


class TimeoutError(RunnerError):
    """测试超时异常"""

    pass


class BaseRunner(ABC):
    """测试运行器基类"""

    def __init__(self, adapter: TestFrameworkAdapter, config: EngineConfig):
        self.adapter = adapter
        self.config = config

    @abstractmethod
    def run(self, test_paths: List[str]) -> List[Dict[str, Any]]:
        """运行测试"""
        pass

    @contextmanager
    def _timeout(self, seconds: int):
        """超时控制"""

        def _handler(signum, frame):
            raise TimeoutError(f"Test execution timed out after {seconds} seconds")

        # 设置信号处理
        original_handler = signal.signal(signal.SIGALRM, _handler)
        signal.alarm(seconds)

        try:
            yield
        finally:
            # 恢复原始信号处理
            signal.alarm(0)
            signal.signal(signal.SIGALRM, original_handler)

    def _handle_retry(self, test_path: str, retry_count: int = 0) -> Dict[str, Any]:
        """处理测试重试逻辑"""
        last_error = None

        for _attempt in range(retry_count + 1):
            try:
                with self._timeout(self.config.timeout):
                    result = self.adapter.run_test(test_path)
                    if result.get("status") == "passed":
                        return result
                    last_error = result
            except Exception as e:
                last_error = e

        return {"path": test_path, "status": "failed", "error": str(last_error), "retries": retry_count}
