class LogPagination {
    constructor(pageSize = 100) {
        this.pageSize = pageSize;
        this.currentPage = 1;
        this.logEntries = Array.from(document.querySelectorAll('.log-entry'));
        this.totalPages = Math.ceil(this.logEntries.length / this.pageSize);
        
        this.init();
    }

    init() {
        this.createPagination();
        this.showPage(1);
        this.bindEvents();
    }

    createPagination() {
        const pagination = document.querySelector('.pagination');
        pagination.innerHTML = `
            <li class="page-item">
                <a class="page-link" href="#" data-page="prev">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        for (let i = 1; i <= this.totalPages; i++) {
            pagination.innerHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }

        pagination.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" data-page="next">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;
    }

    showPage(page) {
        const start = (page - 1) * this.pageSize;
        const end = start + this.pageSize;

        this.logEntries.forEach((entry, index) => {
            entry.style.display = (index >= start && index < end) ? 'block' : 'none';
        });

        this.updatePaginationState(page);
        this.currentPage = page;
    }

    updatePaginationState(page) {
        const pageItems = document.querySelectorAll('.page-item');
        pageItems.forEach(item => {
            item.classList.remove('active', 'disabled');
            const pageLink = item.querySelector('.page-link');
            const pageNum = pageLink.dataset.page;

            if (pageNum === 'prev') {
                if (page === 1) item.classList.add('disabled');
            } else if (pageNum === 'next') {
                if (page === this.totalPages) item.classList.add('disabled');
            } else if (parseInt(pageNum) === page) {
                item.classList.add('active');
            }
        });
    }

    bindEvents() {
        document.querySelector('.pagination').addEventListener('click', (e) => {
            e.preventDefault();
            const pageLink = e.target.closest('.page-link');
            if (!pageLink) return;

            const page = pageLink.dataset.page;
            if (page === 'prev' && this.currentPage > 1) {
                this.showPage(this.currentPage - 1);
            } else if (page === 'next' && this.currentPage < this.totalPages) {
                this.showPage(this.currentPage + 1);
            } else if (page !== 'prev' && page !== 'next') {
                this.showPage(parseInt(page));
            }
        });

        // 自动滚动按钮
        const autoScrollBtn = document.getElementById('autoScroll');
        let autoScrollEnabled = false;
        autoScrollBtn.addEventListener('click', () => {
            autoScrollEnabled = !autoScrollEnabled;
            autoScrollBtn.classList.toggle('active');
            if (autoScrollEnabled) {
                this.showPage(this.totalPages);
                const logsContainer = document.querySelector('.logs-container');
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
        });

        // 展开全部按钮
        document.getElementById('expandAll').addEventListener('click', () => {
            const logsContainer = document.querySelector('.logs-container');
            logsContainer.classList.toggle('expanded');
        });
    }
}

// 初始化分页
document.addEventListener('DOMContentLoaded', () => {
    window.logPagination = new LogPagination();
}); 