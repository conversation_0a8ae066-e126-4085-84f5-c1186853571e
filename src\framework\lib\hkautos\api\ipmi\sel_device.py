"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""
from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class SELDevice(ApiBase):
    def __init__(self):
        super(SELDevice, self).__init__()

    def get_sel_info(self, params=None):
        """
        获取SEL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_info", params=params)[0]["parser"]

    def get_sel_list(self, params=None):
        """
        获取SEL日志列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL日志列表

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_list", params=params)[0]["parser"]

    def get_sel_elist(self, params=None):
        """
        获取SEL日志列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL日志列表

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_elist", params=params)[0]["parser"]

    def get_sel_by_id(self, params=None):
        """
        获取指定ID的SEL日志

        Args:
            params: 参数字典
                   例如: {'logid': 1}

        Returns:
            dict: 返回解析后的SEL日志信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_by_id", params=params)[0]["parser"]

    def clear_sel(self, params=None):
        """
        清除SEL日志

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sel_clear", params=params)[0]["parser"]

    def save_sel(self, params=None):
        """
        保存SEL日志到文件

        Args:
            params: 参数字典
                   例如: {'filename': 'sel.log'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sel_save", params=params)[0]["parser"]

    def get_sel_info_raw(self, params=None):
        """
        获取SEL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_info_raw", params=params)[0]["parser"]

    def get_sel_time(self, params=None):
        """
        获取SEL 时间

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_time", params=params)[0]["parser"]

    def get_sel_time_raw(self, params=None):
        """
        获取SEL 时间

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_time_raw", params=params)[0]["parser"]

    def set_sel_time_raw(self, params=None):
        """
        设置SEL 时间

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sel_time_raw", params=params)[0]["parser"]

    def set_sel_time(self, params=None):
        """
        设置SEL 时间

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SEL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sel_time", params=params)[0]["parser"]

    def get_sel_reserve(self, params=None):
        """
        获取sel Reserve id
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_sel_reserve", params=params)[0]["parser"]

    def clear_sel_raw(self, params=None):
        """
        清除SEL日志

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sel_clear_raw", params=params)[0]["parser"]

    def get_sel_by_id_raw(self, params=None):
        """
        获取指定ID的SEL日志

        Args:
            params: 参数字典
                   例如: {'logid': 1}

        Returns:
            dict: 返回解析后的SEL日志信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sel_by_id_raw", params=params)[0]["parser"]

    def add_sel(self, params=None):
        """
        获取指定ID的SEL日志

        Args:
            params: 参数字典
                   例如: {'logid': 1}

        Returns:
            dict: 返回解析后的SEL日志信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_add_sel", params=params)[0]["parser"]

    def platform_event_message(self, params=None):
        """
        触发平台事件消息
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_platform_event_message", params=params)[0]["parser"]
