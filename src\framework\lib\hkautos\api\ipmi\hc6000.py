"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class Hc6000Device(ApiBase):
    def __init__(self):
        super(Hc6000Device, self).__init__()

    def ipmitool_sensor(self, params=None):
        """
        获取sensor信息

        Args:
            params: 参数字典
                   例如: {'id': '0'}

        Returns:
            dict:

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmitool_get_sensor_info", params=params)[0]["parser"]

    def ipmitool_sensor_name(self, params=None):
        """
        获取sensor信息，处理传感器名称

        Args:
            params: 参数字典
                   例如: {'id': '0'}

        Returns:
            dict:

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmitool_get_sensor_info_name", params=params)[0]["parser"]
