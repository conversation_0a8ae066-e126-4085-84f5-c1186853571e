#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: 兼容性 API

"""

from typing import Any, Dict

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import web_ns


@web_ns.dispatchertype(HostType.BMC)
class Compatibility(ApiBase):
    """兼容性管理 API"""

    def web_get_system_lut(self) -> None:
        """获取web system页面的关键字对照表（Look-Up Table）"""
        return self.dispatcher.dispatch("web_get_system_lut")

    def web_get_cpu_info(self) -> None:
        """cpu信息"""
        return self.dispatcher.dispatch("web_get_cpu_info")

    def web_get_home_product_info(self) -> None:
        """cpu信息"""
        return self.dispatcher.dispatch("web_get_home_product_info")

    def web_get_system_board_info(self) -> None:
        """单板信息"""
        return self.dispatcher.dispatch("web_get_board_info")

    def web_get_mem_info(self) -> None:
        """内存信息"""
        return self.dispatcher.dispatch("web_get_mem_info")

    def web_get_sensor_info(self):
        """获取传感器信息"""
        return self.dispatcher.dispatch("web_get_sensor_info")

    def web_get_system_others_info(self, model):
        """WEB获取”系统管理“->”系统信息“->”其它“信息"""
        return self.dispatcher.dispatch("web_get_system_others_info", params=model)
