"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""
from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class BMCDevice(ApiBase):
    def __init__(self):
        super(BMCDevice, self).__init__()

    def get_mc_info(self, params=None):
        """
        获取BMC信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的BMC信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_mc_info", params=params)[0]["parser"]

    def mc_reset(self, params=None):
        """
        重置BMC

        Args:
            params: 参数字典
                   例如: {'type': 'cold'}
        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_mc_reset", params=params)[0]["parser"]

    def get_mc_guid(self, params=None):
        """
        获取BMC GUID

        Args:
            params: 参数字典
        Returns:
            dict: 返回解析后的GUID信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_mc_guid", params=params)[0]["parser"]

    def get_mc_enables(self, params=None):
        """
        获取BMC功能启用状态

        Args:
            params: 参数字典
        Returns:
            dict: 返回解析后的功能状态信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_mc_getenables", params=params)[0]["parser"]

    def set_mc_enables(self, params=None):
        """
        设置BMC功能启用状态

        Args:
            params: 参数字典
                   例如: {'option': 'receive_message_queue', 'state': 'on'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_mc_setenables", params=params)[0]["parser"]

    def firmware_update(self, params=None):
        """
        通过IPMI升级固件

        Args:
            params: 需要升级的固件名称及路径
                    例如: {'ImageURI': '/tmp/image.hpm'}
        Returns:
            dict: 返回命令执行后的返回值

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_firm_update", params=params)[0]["parser"]

    def get_update_progress(self):
        """
        获取IPMI升级进度
        Returns:
            dict: 升级状态：status  升级进度：task_percentage
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_update_progress")[0]["parser"]

    def get_device_id(self, params=None):
        """
        获取deviceid
        Returns:
            dict: 升级状态：status  升级进度：task_percentage
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_device_id", params=params)[0]["parser"]

    def set_bmc_cold_reset(self, params=None):
        """
        设置BMC冷启动
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_bmc_cold_reset", params=params)[0]["parser"]

    def set_bmc_cold_reset_raw(self, params=None):
        """
        设置BMC冷启动
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_bmc_cold_reset_raw", params=params)[0]["parser"]

    def set_mc_sys_info(self, params=None):
        """
        设置BMC冷启动
        Args:
            params: 参数字典
        Returns:
            dict: 返回命令执行结果
        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_mc_sys_info", params=params)[0]["parser"]
