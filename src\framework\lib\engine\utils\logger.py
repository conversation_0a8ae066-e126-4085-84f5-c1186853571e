import logging
from typing import Optional
from pathlib import Path


def setup_logger(
        level: str = "INFO",
        log_file: Optional[str] = None,
        logger_name: str = "engine"
) -> logging.Logger:
    """设置日志配置"""
    # 设置根日志记录器级别
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 获取指定的日志记录器
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 格式化器
    formatter = logging.Formatter(
        '[%(asctime)s][%(process)d][%(levelname)s] > %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S,%f'[:-3]
    )

    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(level)
    logger.addHandler(console_handler)

    # 添加文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)
        logger.addHandler(file_handler)

    # 设置为不传播到父记录器
    logger.propagate = False

    return logger
