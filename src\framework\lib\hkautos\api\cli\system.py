"""
Description: 4.8 系统命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class System(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(System, self).__init__()
        self.name = "system"

    def cli_query_system_name(self):
        """4.8.1 查询系统名称（systemname）
        命令功能: systemname命令用来查询系统名称。
        命令格式: ipmcget -t smbios -d systemname
        """
        result = self.dispatcher.dispatch("cli_query_system_name")[0]["parser"]
        return result

    @validate_param(timezone=str)
    def cli_set_time_zone(self, timezone):
        """4.8.2 设置iBMC时区（timezone）
            命令功能: timezone命令用来设置iBMC时区。
            命令格式: ipmcset -d timezone -v <timezone>
        Args:
            timezone: 时区。
        """
        params = {"timezone": timezone}
        result = self.dispatcher.dispatch("cli_set_time_zone", params=params)[0]["parser"]
        return result

    def cli_query_ibmc_time(self):
        """4.8.3 查询iBMC时间（time）
        命令功能: time命令用来查询iBMC时间。
        命令格式: ipmcget -d time
        """
        result = self.dispatcher.dispatch("cli_query_ibmc_time")[0]["parser"]
        return result

    def cli_query_device_version(self):
        """4.8.4 查询设备的版本信息（version）
            命令功能: version命令用来查询设备的版本信息。
            命令格式: ipmcget -d version
        Modify:
            获取版本信息后更新dispatcher对应信息
        """
        return self.dispatcher.dispatch("cli_query_device_version")[0]["parser"]

    def cli_query_fru(self, target=""):
        """4.8.5 查询FRU信息（fruinfo）
            命令功能: fruinfo命令用于查询除电源模块之外的其它FRU的信息，包括主板、RAID卡、Mezz卡、
                    硬盘背板、PCIe Rsier卡、GPU载板等。
            命令格式: ipmcget [-t fru0] -d fruinfo
        Args:
            target: 使用-t命令的参数，默认不使用
        """
        params = {"target": target}
        result = self.dispatcher.dispatch("cli_query_fru", params=params)[0]["parser"]
        return result

    def cli_query_system_health_status(self):
        """4.8.6 查询系统的健康状态（health）
        命令功能: health命令用来查询系统的健康状态。
        命令格式: ipmcget [-t fru0] -d health
        """
        result = self.dispatcher.dispatch("cli_query_system_health_status")[0]["parser"]
        return result

    def cli_query_system_health_event(self, page=1):
        """4.8.7 查询系统的健康事件信息（healthevents）
            命令功能: healthevents命令用来查询系统的健康事件信息。
            命令格式: ipmcget [-t fru0] -d healthevents
        Args:
            page(int): 获取日志信息翻页数目。
        """
        params = {"page": page}
        result = self.dispatcher.dispatch("cli_query_system_health_event", params=params)[0]["parser"]
        return result

    def cli_query_of_port_80(self):
        """4.8.8 查询80口信息（port80）
        命令功能: port80命令用于查询80口当前和历史信息。
        命令格式: ipmcget -d port80
        """
        result = self.dispatcher.dispatch("cli_query_of_port_80")[0]["parser"]
        return result

    def cli_query_serial_number_server(self):
        """4.8.9 查询服务器的设备序列号（serialnumber）
        命令功能: serialnumber命令用来查询服务器的设备序列号。
        命令格式: ipmcget [-t smbios] -d serialnumber
        """
        result = self.dispatcher.dispatch("cli_query_serial_number_server")[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_clear_sel(self, method, option=None, sel_id=None, page=1):
        """4.8.10 查询和清除系统SEL信息（sel）
            命令功能: sel命令用来查询和清除系统SEL信息。
            命令格式: ipmcget -d sel -v <option> [sel_id]
                    ipmcset [-t fru0] -d sel -v clear
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 要进行的操作
            sel_id: 要获取处理建议的SEL的ID。
            clear: 清除所有SEL信息。
            page: 获取日志信息翻页数目。
        """
        params = {
            "method": "method",
            "option": "option",
            "sel_id": "sel_id",
            "page": "page",
        }
        result = self.dispatcher.dispatch("cli_query_clear_sel", params=params)[0]["parser"]
        return result

    def cli_query_operation_logs(self, page=1):
        """4.8.11 查询系统操作日志（operatelog）
            命令功能: operatelog命令用来查询系统操作日志。
            命令格式: ipmcget -d operatelog
        Args:
            page: 获取日志信息翻页数目。
        """
        params = {"page": page}
        result = self.dispatcher.dispatch("cli_query_operation_logs", params=params)[0]["parser"]
        return result

    def cli_download_systemcom_data(self):
        """4.8.12 下载系统串口数据（systemcom）
        命令功能: systemcom命令用来下载系统串口数据。
        命令格式: ipmcget -d systemcom
        """
        result = self.dispatcher.dispatch("cli_download_systemcom_data")[0]["parser"]
        return result

    def cli_download_black_box_file(self):
        """4.8.13 下载黑匣子数据（blackbox）
        命令功能: blackbox命令用来下载黑匣子数据。
        命令格式: ipmcget -d blackbox
        """
        result = self.dispatcher.dispatch("cli_download_black_box_file")[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_download_bios(self, option):
        """4.8.14 下载BIOS（download）
            命令功能: maintenance -d download命令用于下载BIOS文件“bios.bin”到“/tmp”目录下。
                    “bios.bin”文件可用于定位OS启动异常和BIOS异常等问题。
            命令格式: ipmcset -t maintenance -d download -v <option>
        Args:
            option: 表示是否下载BIOS到“/tmp”目录下。
        """
        params = {"option": option}
        result = self.dispatcher.dispatch("cli_download_bios", params=params)[0]["parser"]
        return result

    @validate_param(filepath=str)
    def cli_upgrade_bios(self, filepath):
        """4.8.15 升级BIOS（upgradebios）
            命令功能: maintenance -d upgradebios命令用来升级BIOS。
            命令格式: ipmcset -t maintenance -d upgradebios -v filepath
        Args:
            filepath: BIOS升级文件的路径。
        """
        params = {"filepath": filepath}
        result = self.dispatcher.dispatch("cli_upgrade_bios", params=params)[0]["parser"]
        return result

    @validate_param(action=str, ethname=str)
    def cli_set_ibmc_network_port_state(self, action, ethname):
        """4.8.16 设置iBMC网口状态（ethlink）
            命令功能: maintenance -d ethlink命令用来设置iBMC网口的使能状态。
            命令格式: ipmcset -t maintenance -d ethlink -v <ethname> <action>
        Args:
            ethname: 待设置的网口名称
            action: 网口使能状态
        """
        params = {"action": action, "ethname": ethname}
        result = self.dispatcher.dispatch("cli_set_ibmc_network_port_state", params=params)[0]["parser"]
        return result

    def cli_perform_one_click_collection(self):
        """4.8.17 一键收集信息（diaginfo）
        命令功能: diaginfo命令用来一键收集信息，包括iBMC相关的配置信息、版本信息和日志等。
                一键收集信息的更多内容请参见本文档一键收集信息说明。
        命令格式: ipmcget -d diaginfo
        """
        result = self.dispatcher.dispatch("cli_perform_one_click_collection")[0]["parser"]
        return result

    def cli_restore_ibmc_factory_setts(self):
        """4.8.18 恢复iBMC出厂设置（restore）
        命令功能: restore命令用来恢复iBMC出厂设置。执行此命令后iBMC会重启。
        命令格式: ipmcset -d restore
        """
        result = self.dispatcher.dispatch("cli_restore_ibmc_factory_setts")[0]["parser"]
        return result

    @validate_param(option=str)
    def cli_set_clp_notimeout_mode(self, option, value=None):
        """4.8.19 设置CLP notimeout功能（notimeout）
            命令功能: notimeout命令用于设置CLP notimeout功能的使能和禁止状态，以及会话的超时时间。
                    禁用或启用CLP notimeout功能后，需要退出iBMC后重新登录，才能实现CLP
                    notimeout功能的禁用或启用。
                    默认为禁用状态。
            命令格式: ipmcset -d notimeout -v <enabled | disabled> [value]
        Args:
            option:
                enabled: 启用CLP notimeout功能
                disabled: 禁用CLP notimeout功能
            value: 会话超时时间
        """
        params = {"option": option, "value": value}
        result = self.dispatcher.dispatch("cli_set_clp_notimeout_mode", params=params)[0]["parser"]
        return result

    def cli_query_clp_notimeout_set(self):
        """4.8.20 查询CLP notimeout功能的配置信息（notimeoutstate）
        命令功能: notimeoutstate命令用于查询CLP notimeout功能的配置信息，如查询CLP notimeout功能的会话超时时间。
        命令格式: ipmcget -d notimeoutstate
        """
        result = self.dispatcher.dispatch("cli_query_clp_notimeout_set")[0]["parser"]
        return result

    def cli_chang_system_master_keys(self):
        """4.8.21 更新系统主密钥（securityenhance -d updatemasterkey）
        命令功能: securityenhance -d updatemasterkey命令用来更新系统主密钥。
        命令格式: ipmcset -t securityenhance -d updatemasterkey
        """
        result = self.dispatcher.dispatch("cli_chang_system_master_keys")[0]["parser"]
        return result

    @validate_param(method=str, interval=str)
    def cli_query_set_update_interval_master_keys(self, method, interval):
        """4.8.22 查询和设置主密钥自动更新间隔（securityenhance -d masterkeyupdateinterval）
            命令功能: securityenhance -d masterkeyupdateinterval命令用来查询和设置主密钥自动更新间隔。
            命令格式: ipmcget -t securityenhance -d masterkeyupdateinterval
                    ipmcset -t securityenhance -d masterkeyupdateinterval -v <interval>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            interval: 表示自动更新间隔
        """
        params = {"method": method, "interval": interval}
        result = self.dispatcher.dispatch("cli_query_set_update_interval_master_keys", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_automatic_discovery_configuration(self, method, option=None, value=None, netport=None):
        """4.8.23 查询和设置自动发现配置（autodiscovery）
            命令功能: autodiscovery命令用来查询和设置自动发现配置。
            命令格式: ipmcget -d autodiscovery
                    ipmcset -d autodiscovery -v <enable/disable> [option(0/1)] [netport]
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option:
                enable/disable: 使能或禁用自动发现配置功能
            value: 网段选择
            netport: 端口
        """
        params = {
            "method": "method",
            "option": "option",
            "value": "value",
            "netport": "netport",
        }
        result = self.dispatcher.dispatch("cli_query_set_automatic_discovery_configuration", params=params)[0]["parser"]
        return result

    @validate_param(method=str, value=str)
    def cli_query_set_controlled_poweron_configuration(self, method, value=None, ip=None, netport=None):
        """4.8.24 查询和设置受控上电配置（poweronpermit）
            命令功能: poweronpermit命令用来查询和设置受控上电配置。
            命令格式: ipmcget -d poweronpermit
                    ipmcset -d poweronpermit -v <enable | disable> [ip] [netport]
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            value:
                enable: 使能受控上电配置
                disable: 禁止受控上电配置
            ip: 服务器IP地址
            netport: 端口号
        """
        params = {
            "method": "method",
            "value": "value",
            "ip": "ip",
            "netport": "netport",
        }
        result = self.dispatcher.dispatch("cli_query_set_controlled_poweron_configuration", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_clear_poweron_lock(self, method):
        """4.8.25 查询和清除上电锁的锁定状态（poweronlock）
            命令功能: 默认状态下，若服务器在指定时间内未完成上电，则通过iBMC为服务器上电的功能被锁定，
                    服务器将无法通过iBMC上电。
                    poweronlock命令用来查询此上电锁的锁定状态，并可清除此上电锁，取消上述限制。
            命令格式: ipmcget -t maintenance -d poweronlock
                    ipmcset -t maintenance -d poweronlock -v clear
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
        """
        params = {"method": method}
        result = self.dispatcher.dispatch("cli_query_clear_poweron_lock", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_bios_print_enablement_status(self, method, option=None):
        """4.8.26 查询和设置BIOS全打印开关状态（biosprint）
            命令功能: biosprint命令用于查询和设置BIOS全打印开关状态。
            命令格式: ipmcget -t maintenance -d biosprint
                    ipmcset -t maintenance -d biosprint -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            <option>: BIOS全打印开关状态
        """
        params = {"method": method, "option": option}
        result = self.dispatcher.dispatch("cli_query_set_bios_print_enablement_status", params=params)[0]["parser"]
        return result

    @validate_param(value=str)
    def cli_clear_log(self, value):
        """4.8.27 清除日志（clearlog）
            命令功能: clearlog命令用于清除iBMC的操作日志、运行日志或安全日志。
            命令格式: ipmcset -d clearlog -v <value>
        Args:
            <value>: 要清除的日志类型
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_clear_log", params=params)[0]["parser"]
        return result

    def cli_get_sensor(self):
        """4.8.28 查询传感器（sensor）
            命令功能: sensor命令用于查询传感器信息。
            命令格式: ipmcget -t sensor -d list
        Args:

        """
        result = self.dispatcher.dispatch("cli_get_sensor")[0]["parser"]
        return result
