"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""
from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class Bios(ApiBase):
    def __init__(self):
        super(Bios, self).__init__()

    def ipmi_get_bios_boot_mode(self, params=None):
        """
        获取bios启动模式
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_get_bios_boot_mode", params=params)[0][
            "parser"
        ]

    def ipmi_set_bios_boot_options(self, params=None):
        """
        获取bios boot operation:
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_bios_boot_options", params=params)[0][
            "parser"
        ]

    def ipmi_set_bios_boot_params(self, params=None):
        """
        设置bios boot params:
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_bios_boot_params", params=params)[0][
            "parser"
        ]

    def ipmi_set_bios_boot_dev(self, params=None):
        """
        设置bios boot dev:
        Args:
            params:(dict)
        """
        return self.dispatcher.dispatch("ipmi_set_bios_boot_dev", params=params)[0][
            "parser"
        ]
