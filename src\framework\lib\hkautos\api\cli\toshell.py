"""
Description: 4.3 iBMC命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Toshell(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Toshell, self).__init__()
        self.name = "toshell"

    def cli_list_file(self, path=None, time_sort=False, reverse=False, grep=None, awk=False):
        """
        获取文件夹下的文件列表
        Args:
            path:(str) 文件夹路径
            time_sort:(bool) (optional)文件是否按时间升序排序
            reverse:(bool) (optional)逆序排序
            grep:(str) 查找字段
            awk:(bool) 计算文件大小
        Returns:
            result: (str) 读取到的文件列表
        """
        params = {"path": path}
        params.update({"time_sort": time_sort}) if time_sort else None
        params.update({"reverse": reverse}) if reverse else None
        params.update({"grep": grep}) if grep else None
        params.update({"awk": awk}) if awk else None
        return self.dispatcher.dispatch("ls", params=params)[0]["parser"]

    def cli_read_file(self, file_path):
        """
        使用cat命令读取文件
        Args:
            file_path: (str) 要查看的文件路径
        Returns:
            result: (str) 读取到的文件内容，以字符串形式返回
        """
        params = {"file_path": file_path, "timeout": 10}
        result = self.dispatcher.dispatch("cat", params=params)[0]["parser"]
        return result

    def cli_change_directory(self, dir_path):
        """
        telnet连接bmc模式下，切换目录
        Args:
            dir_path: (str) 目录名称
        Returns:
            result: (str) 返回的信息回显
        """
        params = {"dir_path": dir_path, "timeout": 10}
        result = self.dispatcher.dispatch("cd", params=params)[0]["parser"]
        return result

    def cli_unzip_file(self, file_path, exdir=None, force=True, strip=False):
        """
        zip格式文件解压缩
        Args:
            file_path (str): 需要查找指定的路径或者文件
            exdir (str): 解压的目标地址
            force (bool): 不必先询问用户，unzip执行后覆盖原有文件
            strip (bool): 是否去掉压缩文件中目录
        Returns:
            result: (str) 返回的信息回显
        """
        params = {
            "file_path": "file_path",
            "exdir": "exdir",
            "force": "force",
            "strip": "strip",
            "timeout": 10,
        }
        return self.dispatcher.dispatch("unzip_file", params=params)[0]

    def cli_copy_file(self, file_path, destination, sudo=False):
        """
        强制拷贝文件/目标到目标地址
        Args:
            file_path (str): 源路径
            destination (str): 目的路径
            sudo (bool): 超级管理员权限
        Returns:
            result: (str) 返回的信息回显
        """
        params = {
            "source": "file_path",
            "target": "destination",
            "sudo": "sudo",
            "timeout": 10,
        }
        return self.dispatcher.dispatch("copy_file", params=params)[0]

    def cli_execute_program(self, program_name, dir_path, parameter=None, timeout=1800):
        """
        执行可执行程序
        Args:
            program_name: (str) 可执行程序名称
            dir_path: (str) 可执行程序所在的文件夹路径
            parameter: (str) 可执行程序执行时需要的参数
            timeout: (str) 可执行程序执行超时时间
        Returns:
            result: (str) 返回的信息回显
        """
        params = {
            "program_name": program_name,
            "dir_path": dir_path,
            "timeout": timeout,
        }
        params.update({"parameter": parameter}) if parameter else None
        return self.dispatcher.dispatch("execute_program", params=params)[0]

    def cli_chmod(self, file_path, mode, sudo=False):
        """
        改变文件或目录的文件权限
        Args:
            file_path (str): 目的路径
            mode (int) : 文件权限
            sudo (bool): 超级管理员权限
        Returns:
            result: (str) 返回的信息回显
        """
        params = {
            "file_path": "file_path",
            "mode": "mode",
            "sudo": "sudo",
            "timeout": 10,
        }
        return self.dispatcher.dispatch("chmod", params=params)[0]

    def cli_exec_service(self, method, service):
        """
        systemctl命令操作ibmc服务
        Args:
            service  (str):  服务
            method  (str):  操作方式，如start,restart,等
        Returns:
            返回信息
        """
        params = {"service": service, "method": method}
        result = self.dispatcher.dispatch("exec_service", params=params)[0]["parser"]
        return result

    def cli_get_netstat(self, parameter=None):
        """
        netstat命令查看ibmc服务状态
        Args:
           parameter (str): 可执行程序执行时需要的参数
        Returns:
           result (str): 返回的信息回显
        """
        params = {"parameter": parameter} if parameter is not None else None
        result = self.dispatcher.dispatch("get_netstat", params=params)[0]
        return result

    def cli_get_iptables(self, **kwargs):
        """
        iptables命令配置
        Args:
            is_ipv6 (bool): 判断是否为ip6tables表，默认为iptables
            table_type (str): 要查询的tables类型，默认为nat表
            verbose (bool): 判断是否要查询tables的详细模式，-v，默认为开启
            numeric (bool): 判断是否需要将地址和端口的数字输出，-n，默认为开启
            rules_list (bool): 判断是否需要列出一个链或所有链中的规则，-L，默认为开启
        Returns:
            result (str): 返回的信息回显
        """
        result = self.dispatcher.dispatch("get_iptables", kwargs)[0]
        return result

    def cli_del_iptables(self, **kwargs):
        """
        iptables删除操作
        Args:
            is_ipv6 (bool): 判断是否为ip6tables表，默认为iptables
            delete_rule (bool): 判断是否进行删除操作，默认为开启
            chains (bool): 要查询的tables类型，默认为nat表
            numbers (int): 要删除的规则个数，默认为1
        Returns:
            None
        """
        result = self.dispatcher.dispatch("del_iptables", kwargs)[0]
        return result

    def cli_get_route_ipv6(self):
        """
        查询ipv6的路由信息
        Args:
            None
        Returns:
            result (list): ipv6路由信息和前缀
        """
        result = self.dispatcher.dispatch("get_route_ipv6")[0]["parser"]
        return result

    def cli_mount(self):
        """
        查询镜像文件是否存在
        Args:
        Returns:
           返回信息
        """
        result = self.dispatcher.dispatch("mount")[0]["parser"]
        return result

    def cli_get_busybox(self):
        """
        查看进行文件绑定循环设备情况
        Args:
        Returns:
           返回信息
        """
        result = self.dispatcher.dispatch("get_busybox")[0]["parser"]
        return result

    def create_link_file(self, source_file, target_file, link_type="hard_link"):
        """创建硬链接或软链接
        Args:
            link_type        (str): 链接类型（hard_link、soft_link）
            source_file      (str): 源文件
            source_file      (str): 目的文件
        Raises:
            返回信息
        """
        params = {
            "source_file": source_file,
            "target_file": target_file,
            "link_type": link_type,
        }
        result = self.dispatcher.dispatch("create_link_file", params=params)[0]["parser"]
        return result
