#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""华鲲 BMC Web 登录页面模块"""

import os
from typing import Any, Dict

# from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.base_page import BasePage
from hkautos.wrapper.uniwebs.pagebase import PageBase

# from hkautos.wrapper.gui.web.ibmc.ibmcv2r2c90.pages.homepage import HomePage
# from hkautos.utils.concurrent_utils import sleep
from hkautos import log
# from hkautos.exception.webdisplayexception import WebDisplayException
# from hkautos.exception.webloginexception import WebLoginException


class LoginPage(PageBase):
    """
    表示 Bmc登录页面。

    该类包含了初始化页面、从 JSON 文件加载元素定位器，并执行登录操作的方法。

    参数:
        params (dict): 包含初始化参数的字典。

    属性:
        LocatorFile (str): 存储 Web 页面元素定位器的 JSON 文件路径。

    方法:
        __init__(params):
            初始化登录页面，并从 LocatorFile 加载定位器。

        web_login(params):
            使用提供的用户名和密码执行登录操作。

    异常:
        无
    """

    LocatorFile = os.path.join("login.json")

    def __init__(self, WebConnection):
        super(LoginPage, self).__init__(WebConnection)
        self.web_init_elements_dict(self.LocatorFile)
        self.logger = log.get_logger(__name__)

    def web_login(self, params: Dict[str, Any]) -> None:
        """
        执行登录操作，使用提供的用户名和密码填写登录表单并提交。

        参数:
            params:
                - username (str): 用户的登录用户名。
                - password (str): 用户的登录密码。

        返回:
            None

        异常:
            无
        """

        self.WebConnection.connect()
        self.switch_page("login")
        self.WebConnection.fill(self.locator_dict["web_login_name"], params["username"])
        self.WebConnection.fill(self.locator_dict["web_login_pwd"], params["password"])
        self.WebConnection.click(self.locator_dict["web_login_button"])
        self.WebConnection.wait_for_timeout(3000)
        if self.WebConnection.get_elements(f'//*[@id="loginError"]'):
            error_info = self.WebConnection.get_inner_text(f'//*[@id="loginError"]')
            self.logger.error(f"登录失败： {error_info}")
            raise ValueError(f"登录失败： {error_info}")
        self.WebConnection.wait_for_navigation()
        self.WebConnection.wait_for_timeout(5000)
