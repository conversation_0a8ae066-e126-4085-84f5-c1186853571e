
"""
功   能：SMBIOS_type4查询CPU信息测试

修改信息：
    日期：   2025/07/30
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt

class FRAME_CPU_INFO_FUNC_1060(Case):
    """
    CaseId:
        FRAME_CPU_INFO_FUNC_1060
    RunLevel:
        1(#1)
    CaseName:
        SMBIOS_type4查询CPU信息测试
    PreCondition:
        1、环境正常
    TestStep:
        1._进入OS，执行dmidecode_-t_4，查询CPU相关信息，有预期1
    ExpectedResult:
        1._执行成功，CPU数量与web一致，其他检查信息符合下面要求：
        Socket_Designation：需求精准数据且统一，显示成CPU01/CPU02等格式
        Type:_Central_Processor
        Family:_ARM
        Manufacturer：与web一致
        Version：与web一致
        Voltage：精准数据
        Max_Speed：与web一致
        Current_Speed：与web一致
        Serial_Number：与web一致
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.cpu_mgt = self.device_dut.find(HardwareMgt.Cpu)
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.logger.step("1. 进入OS，执行dmidecode -t 4，查询CPU相关信息，有预期1")
        res = self.cpu_mgt.check_cpu_info(self.hardware_json, "dmidecode")
        self.assertTrue(res, "执行成功，CPU数量与web一致，其他检查信息符合要求")

    def post_test_case(self):
        self.logger.info("post test case.... ")
        