#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 机箱管理模块

提供机箱管理相关功能:
- 机箱资源配置
- 传感器管理
- 电源管理
- 散热管理
- LED指示灯管理

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List


def redfish_get_chassis(self) -> List[Dict[str, Any]]:
    """
    获取机箱列表

    URL: /redfish/v1/Chassis
    请求方式: GET
    """
    chassis = self.get_resource("/redfish/v1/Chassis")
    return chassis.data.get("Members", [])


def redfish_get_chassis_info(self) -> Dict[str, Any]:
    """
    获取机箱信息

    URL: /redfish/v1/Chassis/{chassis_id}
    请求方式: GET
    """
    chassis_id = self.chassis_id
    return self.get_resource(f"/redfish/v1/Chassis/{chassis_id}").data


def redfish_get_sensors(self) -> List[Dict[str, Any]]:
    """
    获取传感器列表

    URL: /redfish/v1/Chassis/{chassis_id}/Sensors
    请求方式: GET
    """
    chassis_id = self.chassis_id
    sensors = self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/ThresholdSensors")
    return sensors.data.get("Sensors")


def redfish_get_power(self) -> Dict[str, Any]:
    """
    获取电源信息

    URL: /redfish/v1/Chassis/{chassis_id}/Power
    请求方式: GET
    """
    chassis_id = self.chassis_id
    return self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/Power").data


def redfish_get_thermal(self) -> Dict[str, Any]:
    """
    获取散热信息

    URL: /redfish/v1/Chassis/{chassis_id}/Thermal
    请求方式: GET
    """
    chassis_id = self.chassis_id
    return self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/Thermal").data


def redfish_get_fan_info(self) -> Dict[str, Any]:
    """
    获取风扇信息

    URL: /redfish/v1/Chassis/{chassis_id}/Thermal
    请求方式: GET
    """
    chassis_id = self.chassis_id
    return self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/Thermal").data['Fans']


def redfish_get_indicator_led(self) -> Dict[str, Any]:
    """
    获取指示灯状态

    URL: /redfish/v1/Chassis/{chassis_id}/IndicatorLED
    请求方式: GET
    """
    chassis_id = self.chassis_id
    return self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/IndicatorLED").data


def redfish_set_indicator_led(self, params: Dict[str, Any]) -> None:
    """
    设置指示灯状态

    URL: /redfish/v1/Chassis/{chassis_id}/IndicatorLED
    请求方式: PATCH

    参数:
        params: 参数字典，常用字段:
            - State: 指示灯状态(Lit/Blinking/Off)
            - Color: 指示灯颜色(Red/Green/Blue等)
    """
    chassis_id = self.chassis_id
    self.patch(f"/redfish/v1/Chassis/{chassis_id}/IndicatorLED", params)

def redfish_get_chassis_boards(self, board_type=None) -> List[Dict[str, Any]]:
    """
    查询chassis 板卡的资源信息列表

    URL: /redfish/v1/Chassis/{Chassis_id}/Boards/x
    请求方式: GET
    """
    result = []
    chassis_id = self.chassis_id
    boards_info = self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/Boards").data
    if board_type:
        links = next(iter(boards_info['Oem'].values()))['Boards'][board_type]['Links']
    else:
        links = boards_info["Members"]
    for link in links:
        result.append(self.get_resource(link['@odata.id']).data)
    return result

def redfish_get_psu_info(self) -> Dict[str, Any]:
    """
    获取PSU信息

    URL: /redfish/v1/Chassis/{chassis_id}/Power
    请求方式: GET

    返回:
        PSU信息字典
    """
    chassis_id = self.chassis_id
    return self.get_resource(f"/redfish/v1/Chassis/{chassis_id}/Power").data['PowerSupplies']
