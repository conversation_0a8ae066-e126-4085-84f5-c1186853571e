"""报告基类模块"""
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Type

from .processor import DataProcessor, DefaultProcessor
from .template.engine import TemplateEngine, Jinja2Engine

class BaseReporter(ABC):
    """报告生成器基类"""
    
    # 报告类型注册表
    _reporters: Dict[str, Type["BaseReporter"]] = {}

    def __init__(self, config: Dict[str, Any]):
        """初始化报告生成器
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.processor = self._create_processor()
        self.template_engine = self._create_template_engine()
        self.start_time: Optional[datetime] = None
        self.report_dir: Optional[Path] = None
        self.current_case: Optional[Dict[str, Any]] = None
        self.cases: List[Dict[str, Any]] = []

    @classmethod
    def register(cls, report_type: str):
        """注册报告类型装饰器
        
        Args:
            report_type: 报告类型标识
        """
        def wrapper(reporter_cls: Type["BaseReporter"]):
            cls._reporters[report_type] = reporter_cls
            return reporter_cls
        return wrapper

    @classmethod
    def create(cls, report_type: str, config: Dict[str, Any]) -> "BaseReporter":
        """创建报告实例
        
        Args:
            report_type: 报告类型
            config: 配置信息
            
        Returns:
            BaseReporter: 报告生成器实例
            
        Raises:
            ValueError: 报告类型不存在
        """
        if report_type not in cls._reporters:
            raise ValueError(f"Unknown report type: {report_type}")
            
        return cls._reporters[report_type](config)

    def initialize(self, report_dir: str) -> None:
        """初始化报告目录
        
        Args:
            report_dir: 报告目录
        """
        self.start_time = datetime.now()
        self.report_dir = Path(report_dir)
        self.report_dir.mkdir(parents=True, exist_ok=True)

    def _create_processor(self) -> DataProcessor:
        """创建数据处理器"""
        processor_class = self.config.get("processor", {}).get("class", DefaultProcessor)
        processor_config = self.config.get("processor", {}).get("config", {})
        return processor_class(**processor_config)
        
    def _create_template_engine(self) -> Optional[TemplateEngine]:
        """创建模板引擎"""
        template_config = self.config.get("template", {})
        if not template_config.get("dir"):
            return None
        
        if not template_config.get("enabled", True):
            return None
        
        engine_class = template_config.get("class", Jinja2Engine)
        return engine_class(template_config.get("dir"))

    @abstractmethod
    def start_test(self, case_info: Dict[str, Any]) -> None:
        """开始测试用例
        
        Args:
            case_info: 测试用例信息
        """
        processed_data = self.processor.process_case(case_info)
        self._handle_case_start(processed_data)

    @abstractmethod
    def end_test(self, case_result: Dict[str, Any]) -> None:
        """结束测试用例
        
        Args:
            case_result: 测试结果
        """
        processed_data = self.processor.process_case(case_result)
        self._handle_case_end(processed_data)

    @abstractmethod
    def add_log(self, log_entry: Dict[str, Any]) -> None:
        """添加日志记录
        
        Args:
            log_entry: 日志记录
        """
        processed_log = self.processor.process_log(log_entry)
        self._handle_log(processed_log)

    @abstractmethod
    def generate(self) -> None:
        """生成报告"""
        pass 

    @abstractmethod
    def _handle_case_start(self, case_data: Dict[str, Any]) -> None:
        """处理用例开始"""
        pass
        
    @abstractmethod
    def _handle_case_end(self, case_data: Dict[str, Any]) -> None:
        """处理用例结束"""
        pass
        
    @abstractmethod
    def _handle_log(self, log_data: Dict[str, Any]) -> None:
        """处理日志"""
        pass 