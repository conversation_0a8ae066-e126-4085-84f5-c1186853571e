"""
Description: Upgrade Managerment

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 16:33 created

"""
import random
import re

from hkautos import log
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException
from utilitylibraries.vendor_data.data import sensor_dic

from tests.test_logic.alias import BMCMgt, SystemMgt
from tests.test_logic.bmc_mgt.components.sensor_mgt import SensorMgt
from tests.test_logic.logic import Logic
from tests.utils.compare_utils import check_is_valid_date
from tests.test_data.base import DictListQuery


class SensorLogic(Logic):
    alias = BMCMgt.Sensor

    def __init__(self, owning_device):
        super(SensorLogic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.SensorComponent = SensorMgt(owning_device)
        self.bmc_host = self.owning_device.get_host(HostType.BMC, host_id="1")
        self.vendor = self.bmc_host.vendor
        # Get vendor specific sensor mappings
        self.sensor_mappings = self._get_vendor_sensor_mappings()
        self.log_obj = self.owning_device.find(SystemMgt.Logs)

    def _get_vendor_sensor_mappings(self):
        """
        从data.py获取当前厂商的传感器名称映射

        Returns:
            dict: 当前厂商的传感器名称映射字典，如果厂商不存在则返回华为的映射作为默认值
        """
        return sensor_dic.get(self.vendor, sensor_dic["huawei"])

    def get_sensor_name(self, sensor_key):
        """
        获取厂商特定的传感器名称

        Args:
            sensor_key: 通用的传感器键名（例如：'InletTemp'）

        Returns:
            str: 厂商特定的传感器名称，如果映射中不存在则返回原始键名
        """
        return self.sensor_mappings.get(sensor_key, sensor_key)

    def get_sensor_id(self, sensor_name, **kwargs):
        """
        根据传感器名称获取传感器定制化id；
        Args:
            sensor_name:  传感器名称
        Returns:
            传感器定制化id
        """
        # return sensor_name
        mode = kwargs.get("mode")
        self.SensorComponent.get_sensor_id(sensor_name, mode)

    def get_and_check_sdr(self, sensor_name, **kwargs):
        """
        获取传感器信息
        Args:
            sensor_name: 传感器名称
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回一个字典，包含传感器定制化id和其他信息
        """

        sensor_id = self.get_sensor_name(sensor_name)
        sdr_info = self.SensorComponent.get_sdr(sensor_id, **kwargs)

        # 返回传感器信息和状态
        return {'res_info': sdr_info, 'check_status': self.check_get_sensor_sdr(sdr_info, type='sdr')}

    def check_get_sensor_sdr(self, sdr_info, type='sdr'):
        """
        获取校验sdr信息是否符合
        Sensor ID              : Inlet Temp (0x1)
         Entity ID             : 7.96
         Sensor Type (Threshold)  : Temperature
         Sensor Reading        : 21 (+/- 0) degrees C
         Status                : ok
         Lower Non-Recoverable : na
         Lower Critical        : na
         Lower Non-Critical    : na
         Upper Non-Critical    : 46.000
         Upper Critical        : 48.000
         Upper Non-Recoverable : na
         Positive Hysteresis   : 2.000
         Negative Hysteresis   : 2.000
         Assertion Events      :
         Assertions Enabled    : unc+ ucr+
         Deassertions Enabled  : unc+ ucr+
        Returns:
            True/False
        """

        # 定义期望的键列表
        if type == 'sensor':
            key_list = ['sensor_id', 'entity_id', 'sensor_type_(threshold)', 'sensor_reading', 'status',
                        'lower_non-recoverable', 'lower_critical', 'lower_non-critical', 'upper_non-critical',
                        'upper_critical', 'upper_non-recoverable', 'positive_hysteresis', 'negative_hysteresis',
                        'assertion_events', 'assertions_enabled']
        else:
            key_list = ['sensor_id', 'entity_id', 'sensor_type_(threshold)', 'sensor_reading', 'status',
                        'nominal_reading', 'upper_critical', 'upper_non-critical', 'positive_hysteresis',
                        'negative_hysteresis', 'minimum_sensor_range', 'maximum_sensor_range', 'settable_thresholds',
                        'threshold_read_mask', 'assertion_events', 'assertions_enabled', 'deassertions_enabled']

        # 比较SDR信息中的键与期望的键列表
        for i in key_list:
            if i in sdr_info.keys():
                continue
            else:
                self.logger.error(f'{i} is not in {sdr_info}')
                return False
        return True

    def get_and_check_reservation_id(self, **kwargs):
        """
        获取Reservation Id
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回一个字典，包含传感器定制化id和其他信息
        """
        # 获取Reservation Id
        reservation_id = self.SensorComponent.get_reservation_id(**kwargs)
        return {'res_info': reservation_id, 'check_status': check_is_valid_date(date_str=reservation_id[0])}

    def get_and_check_sensor_sdr_info(self, sensor_info, reserver_id=None, **kwargs):
        """
        获取sdr_info
        Args:
            reserver_id: Reservation Id
            sensor_info: 传感器信息用于与返回值校验
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回一个字典，包含传感器定制化id和其他信息
        """
        # 获取sdr_info
        sensor_id = re.findall(r'\((.*)\)', sensor_info['sensor_id'].strip())[0]
        sdr_info = self.get_sensor_sdr_info(sensor_id=sensor_id, reserver_id=reserver_id, **kwargs)
        check_status = self.check_sensor_sdr_info(sdr_info, sensor_id, sensor_info)
        return {'res_info': sdr_info, 'check_status': check_status}

    def get_sensor_sdr_info(self, sensor_id, reserver_id=None, **kwargs):
        """
        获取sdr_info
        Args:
            reserver_id: Reservation Id
            sensor_info: 传感器信息用于与返回值校验
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回一个字典，包含传感器定制化id和其他信息
        """
        # 获取sdr_info
        if reserver_id is None:
            reserver_id = self.SensorComponent.get_reservation_id()[0]
        sdr_info = self.SensorComponent.get_sensor_sdr_info(sensor_id, reserver_id, **kwargs)
        return sdr_info

    def check_sensor_sdr_info(self, sdr_info, sensor_id, sensor_info):
        """
        校验查询Sensor Sdr info 返回值是否满足
        第3-4字节 为Sensor ID，
        第11-12字 为Entity ID和Entity Instance，对于get_sdr获取的Entity ID : 7.50
        """
        res_list = sdr_info[0].strip().split()
        self.logger.info('校验Sensor ID')
        if int(res_list[2], 16) != int(sensor_id, 16):
            self.logger.error(f'Sensor ID与入参不符，ipmi返回为：{int(sensor_id, 16)}，入参为：{int(res_list[2], 16)}')
            return False
        self.logger.info('校验Entity ID')
        entity_id = f'{int(res_list[10], 16)}.{int(res_list[11], 16)}'
        sensor_entity_id = re.findall(r'(\d+.\d+)', sensor_info['entity_id'].strip())[0]
        if entity_id != sensor_entity_id:
            self.logger.error(f'Entity ID与入参不符,ipmi返回为：{int(sensor_id, 16)}，入参为：{int(res_list[2], 16)}')
            return False
        return True

    def get_sdr_device_info(self, id, **kwargs):
        """
        获取Reservation Id
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        # 获取sdr_device_info
        sdr_device_info = self.SensorComponent.get_sdr_device_info(id, **kwargs)
        return sdr_device_info

    def get_and_check_sdr_device_info(self, id, **kwargs):
        """
        校验查询sdr_device_info 返回值是否为 " xx xx"格式
        """
        sdr_device_info = self.get_sdr_device_info(id, **kwargs)
        return {'res_info': sdr_device_info, 'check_status': check_is_valid_date(date_str=sdr_device_info[0])}

    def get_and_check_sensor(self, sensor_name, **kwargs):
        """
        获取传感器信息
        Args:
            sensor_name:  传感器名称
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回一个字典，包含传感器定制化id和其他信息
        """
        # 获取传感器的SDR信息.
        sensor_id = self.get_sensor_name(sensor_name)
        sdr_info = self.SensorComponent.get_sensor(sensor_id, **kwargs)

        # 返回传感器信息和状态
        return {'res_info': sdr_info, 'check_status': self.check_get_sensor_sdr(sdr_info, type='sensor')}

    def get_sensor_device_info(self, sensor_id, **kwargs):
        """
        获取Reservation Id
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        # 获取sdr_device_info
        sdr_device_info = self.SensorComponent.get_sensor_device_info(sensor_id, **kwargs)
        return sdr_device_info

    def get_and_check_sensor_device_info(self, sensor_info, **kwargs):
        """
        校验查询sdr_device_info 返回值是否为 " xx xx"格式
        """
        sensor_id = re.findall(r'\((.*)\)', sensor_info['sensor_id'].strip())[0]
        sensor_device_info = self.get_sensor_device_info(sensor_id, **kwargs)
        rule = '( [a-f0-9]{2}){7}'
        return {'res_info': sensor_device_info,
                'check_status': check_is_valid_date(date_str=sensor_device_info[0], rule=rule)}

    def sensor_read_get(self, sensor_name, **kwargs):
        """
        获取传感器信息
        Args:
            sensor_name:  传感器名称
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回一个字典，包含传感器定制化id和其他信息
        """
        # 获取传感器的SDR信息.
        sensor_id = self.get_sensor_name(sensor_name)
        read_info = self.SensorComponent.sensor_read_get(sensor_id, **kwargs)
        return read_info

    def check_get_sensor_read(self, sensor_id, sensor_read, **kwargs):

        sensor_info = self.SensorComponent.get_sensor(sensor_id, **kwargs)
        get_read = re.findall(r'(\d+) \(', sensor_info['sensor_reading'].strip())[0]

        self.logger.info('校验返回数据格式')
        rule = rf'{sensor_id}\s+\|\s+\d+'
        if not check_is_valid_date(date_str=sensor_read, rule=rule):
            return False

        self.logger.info('校验传感器读数是否正确')
        sensor_read = re.findall(rf'{sensor_id}\s+\|\s+(\d+)', sensor_read)[0]
        if abs(int(sensor_read) - int(get_read)) > 2:
            self.logger.error(f'Sensor Reading与入参不符，ipmi返回为：'
                              f'{sensor_read}={int(sensor_read)}，入参为：{int(get_read)}')
            return False
        return True

    def sensor_read_get_check(self, sensor_name, **kwargs):
        """
        校验查询sensor read
        返回值是否为 "CPU Temp    | xx"格式
        """
        sensor_id = self.get_sensor_name(sensor_name)
        read_info = self.sensor_read_get(sensor_id, **kwargs)
        # 返回传感器信息和状态
        return {'res_info': read_info, 'check_status': self.check_get_sensor_read(sensor_id, read_info[0], **kwargs)}

    def get_sensor_read(self, id, **kwargs):
        """
        获取Reservation Id
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        # 获取sdr_device_info
        sdr_device_info = self.SensorComponent.get_sensor_read(id, **kwargs)
        return sdr_device_info

    def check_sensor_read(self, sensor_info, sensor_read):

        get_read = re.findall(r'(\d+) \(', sensor_info['sensor_reading'].strip())[0]
        self.logger.info('校验返回数据格式')
        rule = '( [a-f0-9]{2}){4}'
        if not check_is_valid_date(date_str=sensor_read, rule=rule):
            return False

        self.logger.info('校验传感器读数是否正确')
        res_list = sensor_read.strip().split()
        if int(res_list[0], 16) != int(get_read):
            self.logger.error(f'Sensor Reading与入参不符，ipmi返回为：'
                              f'0x{res_list[0]}={int(res_list[0], 16)}，入参为：{int(get_read)}')
            return False
        return True

    def get_and_check_sensor_read(self, sensor_info, **kwargs):
        """
        校验查询sensor read
        返回值是否为 " xx xx....."格式
        校验 与 sensor读值是否一致
        """
        id = re.findall(r'\((.*)\)', sensor_info['sensor_id'].strip())[0]
        sensor_read = self.get_sensor_read(id, **kwargs)
        return {'res_info': sensor_read,
                'check_status': self.check_sensor_read(sensor_info=sensor_info, sensor_read=sensor_read[0])}

    def get_sensor_type(self, sensor_id, **kwargs):
        """
        获取sensor_type
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        # 获取sensor_type
        sensor_type = self.SensorComponent.get_sensor_type(sensor_id=sensor_id, **kwargs)
        return sensor_type

    def get_and_check_sensor_type(self, expect_type, sensor_info, **kwargs):
        """
        校验查询sensor_type 返回值是否为 " xx xx"格式
        """
        sensor_id = re.findall(r'\((.*)\)', sensor_info['sensor_id'].strip())[0]
        sensor_info = self.get_sensor_type(sensor_id, **kwargs)
        return {'res_info': sensor_info, 'check_status': self.check_sensor_type(sensor_info=sensor_info[0],
                                                                                expect_type=expect_type)}

    def check_sensor_type(self, sensor_info, expect_type):
        """
        校验查询sensor_type 返回值是否为 " xx xx"格式
        """
        if not check_is_valid_date(date_str=sensor_info):
            self.logger.error('ipmi返回信息格式不为" xx xx"')
            return False
        if expect_type != sensor_info.strip().split()[0]:
            self.logger.error(f'Sensor Type与入参不符，ipmi返回为：'
                              f'{sensor_info}={expect_type}')
            return False
        return True

    def get_sdr_stores(self, **kwargs):
        """
        查询sdr仓库信息
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        return self.SensorComponent.get_sdr_stores(**kwargs)

    def check_sdr_stores(self, sdr_info):
        """
        校验返回值第二字节是否为sdr仓库值
        """
        sdr_list = self.SensorComponent.getHostObj().get_sdr_list()
        len_sdr = hex(len(sdr_list))[2:]
        res_sdr_len = sdr_info.strip().split()[1]
        if len_sdr != res_sdr_len:
            self.logger.error(f'Sensor Reading与入参不符，ipmi返回为：'
                              f'{res_sdr_len}={len_sdr}')
            return False
        return True

    def get_and_check_sdr_stores(self, **kwargs):
        """
        查询sdr仓库信息
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        sdr_info = self.get_sdr_stores(**kwargs)
        return {'res_info': sdr_info, 'check_status': self.check_sdr_stores(sdr_info[0])}

    def check_mc_guid(self, raw_guid, guid):
        """
        查询并校验查询raw命令与mc guid查询到的guid值是否相同 返回值是否满足
        """
        raw_guid = self.fomat_raw_guid(raw_guid)
        if raw_guid == guid:
            return True
        else:
            return False

    def fomat_raw_guid(self, raw_guid):
        """
        格式化raw命令返回值
        """
        hex_str = raw_guid.replace(" ", "")
        part1 = hex_str[6:8] + hex_str[4:6] + hex_str[2:4] + hex_str[0:2]
        part2 = hex_str[10:12] + hex_str[8:10]
        part3 = hex_str[14:16] + hex_str[12:14]
        part4 = hex_str[16:20]
        part5 = hex_str[20:]
        raw_guid = f"{part1}-{part2}-{part3}-{part4}-{part5}"
        return raw_guid

    def check_sdr_list_format(self, host="local", params=None):
        """
        校验返回值是否符合以下格式
        SDR Version                         : 0x51
        Record Count                        : 169
        Free Space                          : unspecified
        Most recent Addition                :
        Most recent Erase                   :
        SDR overflow                        : no
        SDR Repository Update Support       : unspecified
        Delete SDR supported                : no
        Partial Add SDR supported           : no
        Reserve SDR repository supported    : no
        SDR Repository Alloc info supported : no
        """
        key_list = ['sdr_version', 'record_count', 'free_space', 'most_recent_addition', 'most_recent_erase',
                    'sdr_overflow', 'sdr_repository_update_support', 'delete_sdr_supported',
                    'partial_add_sdr_supported',
                    'reserve_sdr_repository_supported', 'sdr_repository_alloc_info_supported']
        sdr_list = self.SensorComponent.get_sdr_info_interface(host=host, params=params)
        for i in key_list:
            if i in sdr_list.keys():
                continue
            else:
                self.logger.error(f'{i} is not in sdr info')
                return False
        return {"result": True, "sdr_info": sdr_list}

    def set_and_check_sensor_upper(self,sensor_id,select_key='set_sensor_upper',result='Success',
                                   unc="0x00", ucr="0x00", unr="0x00", **kwargs):
        """
        设置传感器门限值
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        log_type = 'audit_log' if self.vendor == 'Openbmc' else 'operation_log'
        expect_log = self.log_obj.get_expected_logs(
            select_key=select_key, log_type=log_type, result=result
        )
        method_name = 'set_sensor_upper'
        return self.log_obj.query_and_check_log(
            log_job=self.SensorComponent,
            method_name=method_name,
            expect_info=expect_log,
            unc=unc,
            ucr=ucr,
            unr=unr,
            sensor_id=sensor_id,
            **kwargs
        )

    def set_and_check_sensor(self,sensor_id,cmd,data,select_key='set_sensor_upper',result='Success',**kwargs):
        """
        设置传感器门限值
        Args:
            username: 用户名，默认为None
            password: 密码，默认为None
            role: 用户角色，默认为None
            mode: 模式，默认为'local'
            check_num: 检查状态码0/1，默认为0
        Returns:
            返回命令返回结果
        """
        log_type = 'audit_log' if self.vendor == 'Openbmc' else 'operation_log'
        expect_log = self.log_obj.get_expected_logs(
            select_key=select_key, log_type=log_type, result=result
        )
        method_name = 'set_sensor'
        return self.log_obj.query_and_check_log(
            log_job=self.SensorComponent,
            method_name=method_name,
            expect_info=expect_log,
            cmd=cmd,
            data=data,
            sensor_id=sensor_id,
            **kwargs
        )

    def set_sensor_threshold_random(self, sensor_list):
        """
        随机挑选一个传感器，并设置其告警门限阈值
        """
        try:
            for k, v in sensor_list.items():
                original_uc = v["'uc'"]
                if original_uc != 'na' and original_uc != '':
                    sensor_id = k
                    self.logger.info(f'测试使用的sensor为：{sensor_id}')
                    break
            new_threshold = str(int(original_uc) + random.randint(1, 3))
            set_ret = self.SensorComponent.set_sensor_threshold_interface(
                params={'sensor_id': sensor_id, 'ucr': new_threshold})
            return {"set_ret": set_ret, "sensor_id": sensor_id, "test_threshold": new_threshold,
                    "original_threshold": original_uc}
        except Exception as e:
            raise HKTestException(f"设置传感器阈值失败: {e}") from e

    def check_sensor_temp(self, sensor_name, mode="web") -> bool:
        """
        通过WEB/IPMI/REDFISH读取某个/些温度信息，检查该温度值范围是否正常，状态是否正常
        Args:
            sensor_name:    需要检查的sensor名称，支持模糊匹配和多顶匹配，如:“CPU TEMP”、"1711 Core Temp"、["CPU TEMP", "CPU REM"]
            mode:   接口模式，可选”web“、”ipmi“、”redfish“，缺省值为“web”
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        # 设定期望的温度范围，当前比较粗，后续可以根据温度点的区域和温度的特性进行细化
        range_expect = [15, 100]
        sensor_name_keyword = "sensor name"
        sensor_status_keyword = "status"
        sensor_value_keyword = 'value'
        if mode == "web":
            sensor_info = self.SensorComponent.web.web_get_sensor_info()
            sensor_obj = DictListQuery(sensor_info["info"])
        elif mode == "ipmi":
            sensor_info = self.SensorComponent.get_sensor_list_interface()
            sensor_obj = DictListQuery(list(sensor_info.values()))
        elif mode == "redfish":
            sensor_info = self.SensorComponent.redfish.get_sensors()
            sensor_obj = DictListQuery(sensor_info)
            sensor_name_keyword = "Name"
            sensor_status_keyword = "Status"
            sensor_value_keyword = 'ReadingValue'
        else:
            self.logger.error(f"参数mode当前只支持”web“、”ipmi“、”redfish“，暂不支持{mode}")
            return False

        sensor_temps = []
        if type(sensor_name) is list:
            for name in sensor_name:
                sensor_temps = sensor_temps + sensor_obj.fuzzy_query(sensor_name_keyword, name)
        else:
            sensor_temps = sensor_obj.fuzzy_query(sensor_name_keyword, sensor_name)
        self.logger.info(f"{sensor_name}相关的温度信息: {sensor_temps}")
        for temp in sensor_temps:
            if temp[sensor_status_keyword].upper() != "OK" or \
                    abs(int(float(temp[sensor_value_keyword])) - (range_expect[0]+range_expect[1])/2) > \
                    (range_expect[1]-range_expect[0])/2:
                self.logger.error(f"{sensor_name}温度检查失败，状态异常或者温度范围异常，温度信息: {temp}， 期望温度范围: {range_expect}")
                return False
        return True
