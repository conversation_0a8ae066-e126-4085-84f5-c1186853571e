#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Web 登录 API

提供登录相关功能:
- 用户登录
- 登录状态检查
- 登出操作
"""

from typing import Any, Dict

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import web_ns


@web_ns.dispatchertype(HostType.BMC)
class Login(ApiBase):
    """登录管理 API"""

    def login(self, params: Dict[str, Any]) -> object:
        """用户登录"""
        return self.dispatcher.dispatch("web_login", params=params)

    def logout(self, web_handle: object):
        """用户登出
            使用方法：
            self.web_handle = self.web_api.login(self.web_login_params)
            self.web_api.logout(self.web_handle)
        """
        return self.dispatcher.dispatch("web_logout", params=web_handle)
