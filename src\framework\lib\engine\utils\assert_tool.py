"""
功    能: 断言基类

版权信息: ©2024-2028 四川华鲲振宇智能科技有限责任公司

"""

import collections
import difflib
import pprint
import re
import warnings

from engine.utils.util import _count_diff_all_purpose, _count_diff_hashable, safe_repr
from hkautos.exception.hktest_exception import SelfAssertionError

_MAX_LENGTH = 80
DIFF_OMITTED = """
Diff is %s characters long. Set self.maxDiff to None to see it."""


class _AssertRaisesContext:
    """A context manager used to implement TestCase.assertRaises* methods."""

    def __init__(self, expected, test_case, expected_regexp=None):
        self.expected = expected
        self.failure_exception = test_case.failure_exception
        self.expected_regexp = expected_regexp

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, tb):
        if exc_type is None:
            try:
                exc_name = self.expected.__name__
            except AttributeError:
                exc_name = str(self.expected)
            raise self.failure_exception("{0} not raised".format(exc_name))
        else:
            if not issubclass(exc_type, self.expected):
                return False
            self.exception = exc_value
            if self.expected_regexp is None:
                return True
            expected_regexp = self.expected_regexp
            raise (expected_regexp.search(str(exc_value)) or self.failure_exception)(
                '"%s" does not match "%s"' % (expected_regexp.pattern, str(exc_value))
            )
        return True


class AssertTool:
    failure_exception = SelfAssertionError
    maxDiff = 640
    _diffThreshold = 65536

    def __init__(self):
        self._type_equality_funcs = {}
        self.addTypeEqualityFunc(dict, "assertDictEqual")
        self.addTypeEqualityFunc(list, "assertListEqual")
        self.addTypeEqualityFunc(tuple, "assertTupleEqual")
        self.addTypeEqualityFunc(set, "assertSetEqual")
        self.addTypeEqualityFunc(frozenset, "assertSetEqual")
        try:
            self.addTypeEqualityFunc(str, "assertMultiLineEqual")
        except NameError:
            pass

    def addTypeEqualityFunc(self, typeobj, function):
        """Add a type specific assertEqual style function to compare a type.

        This method is for use by TestCase subclasses that need to register
        their own type equality functions to provide nicer error messages.

        Args:
            typeobj: The data type to call this function on when both values
                    are of the same type in assertEqual().
            function: The callable taking two arguments and an optional
                    msg= argument that raises self.failure_exception with a
                    useful error message when the two arguments are not equal.
        """
        self._type_equality_funcs[typeobj] = function

    def fail(self, msg=None):
        """Fail immediately, with the given message."""
        raise self.failure_exception(msg)

    def assertFalse(self, expr, msg=None):
        """Check that the expression is false."""
        if expr:
            msg = self._formatMessage(msg, "%s is not false" % safe_repr(expr))
            raise self.failure_exception(msg)

    def assertTrue(self, expr, msg=None):
        """Check that the expression is true."""
        if not expr:
            msg = self._formatMessage(msg, "%s is not true" % safe_repr(expr))
            raise self.failure_exception(msg)

    def _formatMessage(self, msg, standardMsg):
        """Honour the longMessage attribute when generating failure messages.
        If longMessage is False this means:
        * Use only an explicit message if it is provided
        * Otherwise use the standard message for the assert

        If longMessage is True:
        * Use the standard message
        * If an explicit message is provided, plus ' : ' and the explicit message
        """
        if msg is None:
            return standardMsg
        try:
            return "%s : %s" % (standardMsg, msg)
        except UnicodeDecodeError:
            return "%s : %s" % (safe_repr(standardMsg), safe_repr(msg))

    def assertRaises(self, excClass, callableObj=None, *args, **kwargs):
        """Fail unless an exception of class excClass is raised
        by callableObj when invoked with arguments args and keyword
        arguments kwargs. If a different type of exception is
        raised, it will not be caught, and the test case will be
        deemed to have suffered an error, exactly as for an
        unexpected exception.

        If called with callableObj omitted or None, will return a
        context object used like this::

             with self.assertRaises(SomeException):
                 do_something()

        The context manager keeps a reference to the exception as
        the 'exception' attribute. This allows you to inspect the
        exception after the assertion::

            with self.assertRaises(SomeException) as cm:
                do_something()
            the_exception = cm.exception
            self.assertEqual(the_exception.error_code, 3)
        """
        context = _AssertRaisesContext(excClass, self)
        if callableObj is None:
            return context
        with context:
            callableObj(*args, **kwargs)

    def _getAssertEqualityFunc(self, first, second):
        """Get a detailed comparison function for the types of the two args.

        Returns: A callable accepting (first, second, msg=None) that will
        raise a failure exception if first != second with a useful human
        readable error message for those types.
        """
        if type(first) is type(second):
            asserter = self._type_equality_funcs.get(type(first))
            if asserter is not None:
                if isinstance(asserter, str):
                    asserter = getattr(self, asserter)
                return asserter
        return self._baseAssertEqual

    def _baseAssertEqual(self, first, second, msg=None):
        """The default assertEqual implementation, not type specific."""
        if not first == second:
            standardMsg = "%s != %s" % (safe_repr(first), safe_repr(second))
            msg = self._formatMessage(msg, standardMsg)
            raise self.failure_exception(msg)

    def assertEqual(self, first, second, msg=None):
        """Fail if the two objects are unequal as determined by the '=='
        operator.
        """
        assertion_func = self._getAssertEqualityFunc(first, second)
        assertion_func(first, second, msg=msg)

    def assertNotEqual(self, first, second, msg=None):
        """Fail if the two objects are equal as determined by the '!='
        operator.
        """
        if not first != second:
            msg = self._formatMessage(msg, "%s == %s" % (safe_repr(first), safe_repr(second)))
            raise self.failure_exception(msg)

    def assertAlmostEqual(self, first, second, places=None, msg=None, delta=None):
        """Fail if the two objects are unequal as determined by their
        difference rounded to the given number of decimal places
        (default 7) and comparing to zero, or by comparing that the
        between the two objects is more than the given delta.

        Note that decimal places (from zero) are usually not the same
        as significant digits (measured from the most signficant digit).

        If the two objects compare equal then they will automatically
        compare almost equal.
        """
        if first == second:
            return
        if not delta:
            if places is not None:
                raise TypeError("specify delta or places not both")
        elif delta:
            if abs(first - second) <= delta:
                return
            standardMsg = "%s != %s within %s delta" % (
                safe_repr(first),
                safe_repr(second),
                safe_repr(delta),
            )
        else:
            if places is None:
                places = 7
            if round(abs(second - first), places) == 0:
                return
            standardMsg = "%s != %s within %r places" % (
                safe_repr(first),
                safe_repr(second),
                places,
            )
        msg = self._formatMessage(msg, standardMsg)
        raise self.failure_exception(msg)

    def assertNotAlmostEqual(self, first, second, places=None, msg=None, delta=None):
        """Fail if the two objects are equal as determined by their
        difference rounded to the given number of decimal places
        (default 7) and comparing to zero, or by comparing that the
        between the two objects is less than the given delta.

        Note that decimal places (from zero) are usually not the same
        as significant digits (measured from the most signficant digit).

        Objects that are equal automatically fail.
        """
        if delta is not None:
            if places is not None:
                raise TypeError("specify delta or places not both")
        if delta is not None:
            if not first == second:
                if abs(first - second) > delta:
                    return
            standardMsg = "%s == %s within %s delta" % (
                safe_repr(first),
                safe_repr(second),
                safe_repr(delta),
            )
        else:
            if places is None:
                places = 7
            if not first == second:
                if round(abs(second - first), places) != 0:
                    return
            standardMsg = "%s == %s within %r places" % (
                safe_repr(first),
                safe_repr(second),
                places,
            )
        msg = self._formatMessage(msg, standardMsg)
        raise self.failure_exception(msg)

    assertEquals = assertEqual
    assertNotEquals = assertNotEqual
    assertAlmostEquals = assertAlmostEqual
    assertNotAlmostEquals = assertNotAlmostEqual
    assert_ = assertTrue

    def assertSequenceEqual(self, seq1, seq2, msg=None, seq_type=None):
        """An equality assertion for ordered sequences (like lists and tuples).

        For the purposes of this function, a valid ordered sequence type is one
        which can be indexed, has a length, and has an equality operator.

        Args:
            seq1: The first sequence to compare.
            seq2: The second sequence to compare.
            seq_type: The expected datatype of the sequences, or None if no
                    datatype should be enforced.
            msg: Optional message to use on failure instead of a list of
                    differences.
        """
        if seq_type is not None:
            seq_type_name = seq_type.__name__
            if not isinstance(seq1, seq_type):
                raise self.failure_exception("First sequence is not a %s: %s" % (seq_type_name, safe_repr(seq1)))
            raise (isinstance(seq2, seq_type) or self.failure_exception)(
                "Second sequence is not a %s: %s" % (seq_type_name, safe_repr(seq2))
            )
        else:
            seq_type_name = "sequence"
        differing = None
        try:
            len1 = len(seq1)
        except (TypeError, NotImplementedError):
            differing = "First %s has no length.    Non-sequence?" % seq_type_name
        if differing is None:
            try:
                len2 = len(seq2)
            except (TypeError, NotImplementedError):
                differing = "Second %s has no length.    Non-sequence?" % seq_type_name
        if differing is None:
            if seq1 == seq2:
                return
            seq1_repr = safe_repr(seq1)
            seq2_repr = safe_repr(seq2)
            if len(seq1_repr) > 30:
                seq1_repr = seq1_repr[:30] + "..."
            if len(seq2_repr) > 30:
                seq2_repr = seq2_repr[:30] + "..."
            elements = seq_type_name.capitalize(), seq1_repr, seq2_repr
            differing = "%ss differ: %s != %s\n" % elements
            for i in range(min(len1, len2)):
                try:
                    item1 = seq1[i]
                except (TypeError, IndexError, NotImplementedError):
                    differing += "\nUnable to index element %d of first %s\n" % (
                        i,
                        seq_type_name,
                    )
                    break
                try:
                    item2 = seq2[i]
                except (TypeError, IndexError, NotImplementedError):
                    differing += "\nUnable to index element %d of second %s\n" % (
                        i,
                        seq_type_name,
                    )
                    break
                if item1 != item2:
                    differing += "\nFirst differing element %d:\n%s\n%s\n" % (
                        i,
                        item1,
                        item2,
                    )
                    break
            else:
                if len1 == len2:
                    if seq_type is None and not isinstance(seq1, type(seq2)):
                        return
            if len1 > len2:
                differing += "\nFirst %s contains %d additional elements.\n" % (
                    seq_type_name,
                    len1 - len2,
                )
                try:
                    differing += "First extra element %d:\n%s\n" % (len2, seq1[len2])
                except (TypeError, IndexError, NotImplementedError):
                    differing += "Unable to index element %d of first %s\n" % (
                        len2,
                        seq_type_name,
                    )
            elif len1 < len2:
                differing += "\nSecond %s contains %d additional elements.\n" % (
                    seq_type_name,
                    len2 - len1,
                )
                try:
                    differing += "First extra element %d:\n%s\n" % (len1, seq2[len1])
                except (TypeError, IndexError, NotImplementedError):
                    differing += "Unable to index element %d of second %s\n" % (
                        len1,
                        seq_type_name,
                    )
        standardMsg = differing
        diffMsg = "\n" + "\n".join(difflib.ndiff(pprint.pformat(seq1).splitlines(), pprint.pformat(seq2).splitlines()))
        standardMsg = self._truncateMessage(standardMsg, diffMsg)
        msg = self._formatMessage(msg, standardMsg)
        self.fail(msg)

    def _truncateMessage(self, message, diff):
        max_diff = self.maxDiff
        if max_diff is None or len(diff) <= max_diff:
            return message + diff
        return message + DIFF_OMITTED % len(diff)

    def assertListEqual(self, list1, list2, msg=None):
        """A list-specific equality assertion.

        Args:
            list1: The first list to compare.
            list2: The second list to compare.
            msg: Optional message to use on failure instead of a list of
                    differences.

        """
        self.assertSequenceEqual(list1, list2, msg, seq_type=list)

    def assertTupleEqual(self, tuple1, tuple2, msg=None):
        """A tuple-specific equality assertion.

        Args:
            tuple1: The first tuple to compare.
            tuple2: The second tuple to compare.
            msg: Optional message to use on failure instead of a list of
                    differences.
        """
        self.assertSequenceEqual(tuple1, tuple2, msg, seq_type=tuple)

    def assertSetEqual(self, set1, set2, msg=None):
        """A set-specific equality assertion.

        Args:
            set1: The first set to compare.
            set2: The second set to compare.
            msg: Optional message to use on failure instead of a list of
                    differences.

        assertSetEqual uses ducktyping to support different types of sets, and
        is optimized for sets specifically (parameters must support a
        difference method).
        """
        try:
            difference1 = set1.difference(set2)
        except TypeError as e:
            try:
                self.fail("invalid type when attempting set difference: %s" % e)
            finally:
                e = None
                del e
        except AttributeError as e:
            try:
                self.fail("first argument does not support set difference: %s" % e)
            finally:
                e = None
                del e
        try:
            difference2 = set2.difference(set1)
        except TypeError as e:
            try:
                self.fail("invalid type when attempting set difference: %s" % e)
            finally:
                e = None
                del e
        except AttributeError as e:
            try:
                self.fail("second argument does not support set difference: %s" % e)
            finally:
                e = None
                del e
        if not difference1:
            if not difference2:
                return
        lines = []
        if difference1:
            lines.append("Items in the first set but not the second:")
            for item in difference1:
                lines.append(repr(item))
        if difference2:
            lines.append("Items in the second set but not the first:")
            for item in difference2:
                lines.append(repr(item))
        standardMsg = "\n".join(lines)
        self.fail(self._formatMessage(msg, standardMsg))

    def assertIn(self, member, container, msg=None):
        """Just like self.assertTrue(a in b), but with a nicer default message."""
        if member not in container:
            standardMsg = "%s not found in %s" % (
                safe_repr(member),
                safe_repr(container),
            )
            self.fail(self._formatMessage(msg, standardMsg))

    def assertNotIn(self, member, container, msg=None):
        """Just like self.assertTrue(a not in b), but with a nicer default message."""
        if member in container:
            standardMsg = "%s unexpectedly found in %s" % (
                safe_repr(member),
                safe_repr(container),
            )
            self.fail(self._formatMessage(msg, standardMsg))

    def assertIs(self, expr1, expr2, msg=None):
        """Just like self.assertTrue(a is b), but with a nicer default message."""
        if expr1 is not expr2:
            standardMsg = "%s is not %s" % (safe_repr(expr1), safe_repr(expr2))
            self.fail(self._formatMessage(msg, standardMsg))

    def assertIsNot(self, expr1, expr2, msg=None):
        """Just like self.assertTrue(a is not b), but with a nicer default message."""
        if expr1 is expr2:
            standardMsg = "unexpectedly identical: %s" % (safe_repr(expr1),)
            self.fail(self._formatMessage(msg, standardMsg))

    def assertDictEqual(self, d1, d2, msg=None):
        self.assertIsInstance(d1, dict, "First argument is not a dictionary")
        self.assertIsInstance(d2, dict, "Second argument is not a dictionary")
        if d1 != d2:
            standardMsg = "%s != %s" % (safe_repr(d1, True), safe_repr(d2, True))
            diff = "\n" + "\n".join(difflib.ndiff(pprint.pformat(d1).splitlines(), pprint.pformat(d2).splitlines()))
            standardMsg = self._truncateMessage(standardMsg, diff)
            self.fail(self._formatMessage(msg, standardMsg))

    def assertDictContainsSubset(self, expected, actual, msg=None):
        """Checks whether actual is a superset of expected."""
        missing = []
        mismatched = []
        for key, _value in expected.iteritems():
            if key not in actual:
                missing.append(key)
        if not missing:
            if not mismatched:
                return
        standardMsg = ""
        if missing:
            standardMsg = "Missing: %s" % ",".join(safe_repr(m) for m in missing)
        if mismatched:
            if standardMsg:
                standardMsg += "; "
            standardMsg += "Mismatched values: %s" % ",".join(mismatched)
        self.fail(self._formatMessage(msg, standardMsg))

    def assertItemsEqual(self, expected_seq, actual_seq, msg=None):
        """An unordered sequence specific comparison. It asserts that
        actual_seq and expected_seq have the same element counts.
        Equivalent to::

            self.assertEqual(Counter(iter(actual_seq)),
                             Counter(iter(expected_seq)))

        Asserts that each element has the same count in both sequences.
        Example:
            - [0, 1, 1] and [1, 0, 1] compare equal.
            - [0, 0, 1] and [0, 1] compare unequal.
        """
        first_seq, second_seq = list(expected_seq), list(actual_seq)
        with warnings.catch_warnings():
            try:
                first = collections.Counter(first_seq)
                second = collections.Counter(second_seq)
            except TypeError:
                differences = _count_diff_all_purpose(first_seq, second_seq)
            else:
                if first == second:
                    return
                differences = _count_diff_hashable(first_seq, second_seq)
        if differences:
            standardMsg = "Element counts were not equal:\n"
            lines = [("First has %d, Second has %d:  %r" % diff) for diff in differences]
            diffMsg = "\n".join(lines)
            standardMsg = self._truncateMessage(standardMsg, diffMsg)
            msg = self._formatMessage(msg, standardMsg)
            self.fail(msg)

    def assertMultiLineEqual(self, first, second, msg=None):
        """Assert that two multi-line strings are equal."""
        self.assertIsInstance(first, str, "First argument is not a string")
        self.assertIsInstance(second, str, "Second argument is not a string")
        if first != second:
            if len(first) > self._diffThreshold or len(second) > self._diffThreshold:
                self.AssertEqual(first, second, msg)
            firstlines = first.splitlines(True)
            secondlines = second.splitlines(True)
            if len(firstlines) == 1:
                if first.strip("\r\n") == first:
                    firstlines = [first + "\n"]
                    secondlines = [second + "\n"]
            standardMsg = "%s != %s" % (safe_repr(first, True), safe_repr(second, True))
            diff = "\n%s" % "".join(difflib.ndiff(firstlines, secondlines))
            standardMsg = self._truncateMessage(standardMsg, diff)
            self.fail(self._formatMessage(msg, standardMsg))

    def assertLess(self, a, b, msg=None):
        """Just like self.assertTrue(a < b), but with a nicer default message."""
        if not a < b:
            standardMsg = "%s not less than %s" % (safe_repr(a), safe_repr(b))
            self.fail(self._formatMessage(msg, standardMsg))

    def assertLessEqual(self, a, b, msg=None):
        """Just like self.assertTrue(a <= b), but with a nicer default message."""
        if not a <= b:
            standardMsg = "%s not less than or equal to %s" % (
                safe_repr(a),
                safe_repr(b),
            )
            self.fail(self._formatMessage(msg, standardMsg))

    def assertGreater(self, a, b, msg=None):
        """Just like self.assertTrue(a > b), but with a nicer default message."""
        if not a > b:
            standardMsg = "%s not greater than %s" % (safe_repr(a), safe_repr(b))
            self.fail(self._formatMessage(msg, standardMsg))

    def assertGreaterEqual(self, a, b, msg=None):
        """Just like self.assertTrue(a >= b), but with a nicer default message."""
        if not a >= b:
            standardMsg = "%s not greater than or equal to %s" % (
                safe_repr(a),
                safe_repr(b),
            )
            self.fail(self._formatMessage(msg, standardMsg))

    def assertIsNone(self, obj, msg=None):
        """Same as self.assertTrue(obj is None), with a nicer default message."""
        if obj is not None:
            standardMsg = "%s is not None" % (safe_repr(obj),)
            self.fail(self._formatMessage(msg, standardMsg))

    def assertIsNotNone(self, obj, msg=None):
        """Included for symmetry with assertIsNone."""
        if obj is None:
            standardMsg = "unexpectedly None"
            self.fail(self._formatMessage(msg, standardMsg))

    def assertIsInstance(self, obj, cls, msg=None):
        """Same as self.assertTrue(isinstance(obj, cls)), with a nicer default message."""
        if not isinstance(obj, cls):
            standardMsg = "%s is not an instance of %r" % (safe_repr(obj), cls)
            self.fail(self._formatMessage(msg, standardMsg))

    def assertNotIsInstance(self, obj, cls, msg=None):
        """Included for symmetry with assertIsInstance."""
        if isinstance(obj, cls):
            standardMsg = "%s is an instance of %r" % (safe_repr(obj), cls)
            self.fail(self._formatMessage(msg, standardMsg))

    def assertRaisesRegexp(self, expected_exception, expected_regexp, callable_obj=None, *args, **kwargs):
        """Asserts that the message in a raised exception matches a regexp.

        Args:
            expected_exception: Exception class expected to be raised.
            expected_regexp: Regexp (re pattern object or string) expected
                    to be found in error message.
            callable_obj: Function to be called.
            args: Extra args.
            kwargs: Extra kwargs.
        """
        if expected_regexp is not None:
            expected_regexp = re.compile(expected_regexp)
        context = _AssertRaisesContext(expected_exception, self, expected_regexp)
        if callable_obj is None:
            return context
        with context:
            callable_obj(*args, **kwargs)

    def assertRegexpMatches(self, text, expected_regexp, msg=None):
        """Fail the test unless the text matches the regular expression."""
        if isinstance(expected_regexp, str):
            expected_regexp = re.compile(expected_regexp)
        if not expected_regexp.search(text):
            msg = msg or "Regexp didn't match"
            msg = "%s: %r not found in %r" % (msg, expected_regexp.pattern, text)
            raise self.failure_exception(msg)

    def assertNotRegexpMatches(self, text, unexpected_regexp, msg=None):
        """Fail the test if the text matches the regular expression."""
        if isinstance(unexpected_regexp, str):
            unexpected_regexp = re.compile(unexpected_regexp)
        match = unexpected_regexp.search(text)
        if match:
            msg = msg or "Regexp matched"
            msg = "%s: %r matches %r in %r" % (
                msg,
                text[match.start() : match.end()],
                unexpected_regexp.pattern,
                text,
            )
            raise self.failure_exception(msg)

    def assertRange(self, a, b, c, msg=None):
        """Just like self.assertTrue(c <= a <= b), but with a nicer default message."""
        if float(b) > float(a) or float(a) > float(c):
            standardMsg = "%s not more than %s, not less than %s" % (
                safe_repr(a), safe_repr(b), safe_repr(c))
            self.fail(self._formatMessage(msg, standardMsg))
