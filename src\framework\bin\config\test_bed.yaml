# 环境配置
Environment:
  nfs:
    ipv4_address: ***************
    username: root
    password: t<PERSON><PERSON>@123

# 服务器配置
Server:
  - id: 1
    DeviceType: Server
    DeviceUtility: DUT
    BMC:
      id: 1
      ipv4_address: ***************
      username: root
      password: hkzy@2023
      serial:
        ipv4_address: ***********
        username: Administrator
        password: Admin@9000
        port: 10023
    HostOS:
      - id: 1
        ipv4_address: ***************
        username: root
        password: hkzy@2023
        os: CentOS
      - id: 2
        ipv4_address: ***************
        username: root
        password: tiangong@123
        os: CentOS

  # - id: 2
  #   DeviceType: Server
  #   DeviceUtility: DST
  #   BMC:
  #     id: 1
  #     ipv4_address: ***************
  #     username: admin
  #     password: admin123
  #   HostOS:
  #     - id: 1
  #       ipv4_address: ***************
  #       username: root
  #       password: root123
  #       os: Debian

# 扩展设备配置
#Extension:
#  - id: 1
#    DeviceType: Extension
#    DeviceUtility: DUT
#    HostOS:
#      - id: 1
#        ipv4_address: ***************
#        username: root
#        password: root123
#        os: Linux