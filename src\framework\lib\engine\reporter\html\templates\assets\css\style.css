/* 全局样式 */
body {
    margin: 0;
    padding: 10px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    padding-bottom: 60px;
}

/* 标题样式 */
.report-title {
    font-size: 18px;
    margin: 0 0 5px 0;
    font-weight: bold;
}

.timestamp {
    color: #666;
    margin-bottom: 15px;
}

.panel-title {
    font-size: 16px;
    margin: 0 0 10px 0;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

/* 三栏布局 */
.report-container {
    display: flex;
    gap: 10px;
    height: calc(100vh - 30px);
    margin-bottom: 10px;
}

/* 左侧：用例信息面板 */
.case-info-panel {
    width: 250px;
    border: 1px solid #ccc;
    padding: 10px;
    background: #f9f9f9;
    overflow: auto;
}

.info-item {
    margin-bottom: 8px;
}

.info-item label {
    display: block;
    color: #666;
    margin-bottom: 2px;
    font-weight: bold;
}

.status-passed {
    color: green;
    font-weight: bold;
}

.status-failed {
    color: red;
    font-weight: bold;
}

.status-running {
    color: blue;
    font-weight: bold;
}

.status-stopped {
    color: orange;
    font-weight: bold;
}

.running {
    color: blue;
}

.stopped {
    color: orange;
}

/* 中间：日志面板 */
.log-panel {
    flex: 1;
    border: 1px solid #ccc;
    padding: 10px;
    overflow: auto;
}

/* 右侧：筛选面板 */
.filter-panel {
    width: 200px;
    border: 1px solid #ccc;
    padding: 10px;
    background: #f9f9f9;
    overflow: auto;
}

.filter-section {
    margin-bottom: 15px;
}

.filter-section label {
    display: block;
    margin-bottom: 5px;
}

.filter-label {
    font-weight: bold;
    margin-bottom: 5px;
}

#keywordSearch, #threadId {
    width: 100%;
    padding: 5px;
    border: 1px solid #ccc;
    margin-bottom: 5px;
}

#submitThreadFilter {
    padding: 5px 10px;
    background: #f0f0f0;
    border: 1px solid #ccc;
    cursor: pointer;
    width: 100%;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 日志表格样式 */
.log-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ccc;
    table-layout: auto;
}

.log-table th {
    background: #f0f0f0;
    text-align: left;
    padding: 5px;
    border: 1px solid #ccc;
    font-weight: normal;
    position: sticky;
    top: 0;
    z-index: 1;
}

.log-table td {
    padding: 5px;
    border: 1px solid #ccc;
    vertical-align: top;
    word-break: break-all;
    white-space: normal;
}

.col-time { width: 160px; }
.col-thread { width: 80px; }
.col-source { 
    width: 200px; 
    white-space: pre-wrap;
    word-wrap: break-word;
}
.col-level { width: 70px; }
.col-message {
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 日志级别样式 */
.log-entry.pass td { color: green; }
.log-entry.passed td { color: green; }
.log-entry.info td { color: black; }
.log-entry.debug td { color: #6c757d; }
.log-entry.warning td { color: orange; }
.log-entry.error td { color: red; }
.log-entry.step td { color: blue; }
.log-entry.cmd td { color: #0dcaf0; }
.log-entry.cmd_response td { color: #198754; }

/* 展开步骤按钮 */
.expand-step {
    background: #f0f0f0;
    border: 1px solid #ccc;
    padding: 2px 5px;
    cursor: pointer;
    margin-right: 10px;
}

/* 步骤内容样式 */
.step-content {
    display: block;
    margin: 5px 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
    background-color: #f8f9fa;
    padding: 5px;
    border-radius: 3px;
    max-height: 300px;
    overflow: auto;
}

/* 步骤详情默认隐藏 */
.step-content-row {
    display: none;
}

/* 非步骤日志正常显示 */
.log-entry:not(.step-header):not(.step-content-row) {
    display: table-row;
}

/* 步骤标题样式 */
.step-header {
    background-color: #f8f9fa;
    display: table-row;
}

.message-content {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
}

/* 响应式布局 */
@media (max-width: 1024px) {
    .report-container {
        flex-direction: column;
        height: auto;
    }
    
    .case-info-panel, .filter-panel {
        width: auto;
        margin-bottom: 10px;
    }
}

/* 用例信息表格 */
.info-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    margin-bottom: 20px;
}

.info-table td {
    padding: 8px 12px;
    border: 1px solid #eee;
}

.info-table td:first-child {
    width: 120px;
    background: #f8f9fa;
}

.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.passed {
    background: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

/* 日志表格 */
.log-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    font-size: 13px;
    font-family: Consolas, Monaco, monospace;
}

.log-table th {
    background: #f8f9fa;
    padding: 8px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
}

.log-table td {
    padding: 4px 8px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
    white-space: nowrap;
}

.col-time { width: 180px; }
.col-thread { width: 100px; }
.col-level { width: 80px; }
.col-message {
    white-space: normal;
    word-break: break-all;
}

/* 筛选器样式 */
.filter-section {
    background: #fff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.filter-header h2 {
    margin: 0;
    font-size: 18px;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

#filterOptions {
    display: flex;
    gap: 10px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 错误信息样式 */
.error-section {
    background: #fff;
    padding: 15px;
    margin-top: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.error-section h2 {
    margin: 0 0 10px;
    color: #dc3545;
    font-size: 18px;
}

.error {
    margin: 0;
    padding: 10px;
    background: #f8d7da;
    border-radius: 4px;
    color: #721c24;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    white-space: pre-wrap;
    word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .summary-section {
        grid-template-columns: repeat(2, 1fr);
    }

    .log-entry {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}

/* 用例信息样式 */
.case-info {
    text-align: left;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 10px 0;
}

.case-info p {
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.case-info strong {
    color: #495057;
}

/* 按钮样式 */
.btn-group .btn {
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-group .btn.active {
    background-color: #0d6efd;
    color: white;
}

/* 分页样式 */
.pagination {
    margin: 0;
}

.page-link {
    padding: 0.375rem 0.75rem;
}

/* 卡片样式优化 */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    margin-bottom: 1rem;
}

.card-header {
    padding: 12px 15px;
}

.source-content {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
    max-height: 100px;
    overflow: auto;
}

/* 摘要表格样式 */
.summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    text-align: center;
}

.summary-table th, .summary-table td {
    border: 1px solid #ddd;
    padding: 8px;
}

.summary-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.summary-table .passed {
    color: green;
    font-weight: bold;
}

.summary-table .failed {
    color: red;
    font-weight: bold;
}

.summary-table .running {
    color: blue;
    font-weight: bold;
}

.summary-table .stopped {
    color: orange;
    font-weight: bold;
}

/* 摘要页面样式 */
.container {
    max-width: 95%;
    margin: 0 auto;
    padding: 15px;
}

.timestamp {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

/* 摘要卡片样式 */
.summary-card, .cases-card, .env-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.summary-header h2, .card-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.summary-timestamp {
    color: #666;
    font-size: 14px;
}

/* 统计数据样式 */
.summary-stats {
    display: flex;
    flex-wrap: wrap;
    padding: 15px;
    gap: 10px;
    justify-content: space-between;
}

.stat-item {
    flex: 1;
    padding: 10px;
    min-width: 100px;
    text-align: center;
    border-radius: 6px;
    background: #f8f9fa;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.stat-item.passed .stat-value { color: #28a745; }
.stat-item.failed .stat-value { color: #dc3545; }
.stat-item.running .stat-value { color: #007bff; }
.stat-item.stopped .stat-value { color: #fd7e14; }

/* 进度条样式 */
.progress-container {
    padding: 0 20px 20px;
}

.progress-bar {
    height: 10px;
    border-radius: 5px;
    background: #e9ecef;
    display: flex;
    overflow: hidden;
}

.progress-segment {
    height: 100%;
}

.progress-segment.passed { background-color: #28a745; }
.progress-segment.failed { background-color: #dc3545; }
.progress-segment.running { background-color: #007bff; }
.progress-segment.stopped { background-color: #fd7e14; }

/* 用例表格样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.filter-controls {
    display: flex;
    gap: 10px;
}

#caseFilter, #statusFilter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.cases-table {
    width: 100%;
    border-collapse: collapse;
}

.cases-table th, .cases-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.cases-table th {
    background: #f8f9fa;
    font-weight: 500;
    color: #495057;
}

.case-row:hover {
    background-color: #f8f9fa;
}

.status-cell {
    text-align: center;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.passed {
    background: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.running {
    background: #cce5ff;
    color: #004085;
}

.status-badge.stopped {
    background: #fff3cd;
    color: #856404;
}

.view-btn {
    display: inline-block;
    padding: 4px 10px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #495057;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s;
}

.view-btn:hover {
    background: #e9ecef;
    color: #212529;
}

/* 环境信息表格 */
.env-card {
    padding: 20px;
}

.env-card h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
}

.env-table {
    width: 100%;
    border-collapse: collapse;
}

.env-table td {
    padding: 6px 0;
    border-bottom: 1px solid #eee;
}

.env-table td:first-child {
    width: 200px;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .summary-stats {
        flex-direction: column;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-controls {
        margin-top: 10px;
        width: 100%;
    }
    
    #caseFilter, #statusFilter {
        flex: 1;
    }
}

/* 添加两列布局样式 */
.two-column-layout {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.left-column {
    width: 25%;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.right-column {
    width: 75%;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 环境信息卡片样式 */
.env-card, .stats-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.card-body {
    padding: 15px;
}

.env-table {
    width: 100%;
    border-collapse: collapse;
}

.env-table td {
    padding: 6px 0;
    border-bottom: 1px solid #eee;
}

.env-table td:first-child {
    width: 40%;
    color: #666;
    font-weight: 500;
}

/* 表格容器，支持滚动 */
.table-container {
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

/* 饼图样式 */
.stat-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.chart-container {
    width: 150px;
    height: 150px;
    position: relative;
}

/* 图例样式 */
.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.legend-color.passed { background-color: #28a745; }
.legend-color.failed { background-color: #dc3545; }
.legend-color.running { background-color: #007bff; }
.legend-color.stopped { background-color: #fd7e14; }

.legend-label {
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .two-column-layout {
        flex-direction: column;
    }
    
    .left-column, .right-column {
        width: 100%;
    }
}

/* 分页导航样式优化 - 固定在底部 */
.pagination-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 10px 0;
    margin: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-btn {
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s;
}

.page-btn:hover {
    background-color: #e9ecef;
    color: #0056b3;
}

.page-btn.disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    opacity: 0.65;
}

.page-info {
    font-size: 14px;
    color: #6c757d;
} 