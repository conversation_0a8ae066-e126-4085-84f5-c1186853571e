"""
Description: 4.5 Syslog命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Syslog(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Syslog, self).__init__()
        self.name = "syslog"

    @validate_param(method=str)
    def cli_query_set_syslog(self, method, value=None, destination=None):
        """4.5.1 查询和设置syslog使能状态（syslog -d state）
            命令功能: syslog -d state命令用于查询和设置iBMC的syslog上报功能的使能状态。
            命令格式: ipmcget -t syslog -d state [-v destination]
                    ipmcset -t syslog -d state -v [destination] <disabled | enabled>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            destination: 表示syslog上报通道的编号。
            value:
                disabled: 表示禁用syslog上报功能。
                enabled: 表示启用syslog上报功能。
        """
        params = {"method": method, "value": value, "destination": destination}
        result = self.dispatcher.dispatch("cli_query_set_syslog", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_certificate_authentication_mode(self, method, option=None):
        """4.5.2 查询和设置证书认证方式（syslog -d auth）
            命令功能: syslog -d auth命令用于查询和设置证书认证方式。
            命令格式: ipmcget -t syslog -d auth
                    ipmcset -t syslog -d auth -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 表示证书认证方式。
        """
        params = {"method": method, "option": option}
        result = self.dispatcher.dispatch("cli_query_set_certificate_authentication_mode", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_syslog_host_identifier(self, method, option=None):
        """4.5.3 查询和设置syslog主机标识（syslog -d identity）
            命令功能: syslog -d identity命令用于查询和设置syslog日志上报时使用的主机标识。
            命令格式: ipmcget -t syslog -d identity
                    ipmcset -t syslog -d identity -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 表示要设置的主机标识
        """
        params = {"method": method, "option": option}
        result = self.dispatcher.dispatch("cli_query_set_syslog_host_identifier", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_protocol_type(self, method, option=None):
        """4.5.4 查询和设置传输协议类型（syslog -d protocol）
            命令功能: syslog -d protocol命令用于查询和设置上报syslog日志时采用的传输协议类型。
            命令格式: ipmcget -t syslog -d protocol
                    ipmcset -t syslog -d protocol -v <option>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            option: 表示采用的协议类型。
        """
        params = {"method": method, "option": option}
        result = self.dispatcher.dispatch("cli_query_set_protocol_type", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_set_log_levels(self, method, level=None):
        """4.5.5 查询和设置上报日志的级别（syslog -d severity）
            命令功能: syslog -d severity命令用于查询和设置通过syslog上报的日志的级别。
            命令格式: ipmcget -t syslog -d severity
                    ipmcset -t syslog -d severity -v <level>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            level: 表示上报日志的级别。
        """
        params = {"method": method, "level": level}
        result = self.dispatcher.dispatch("cli_query_set_log_levels", params=params)[0]["parser"]
        return result

    @validate_param(method=str)
    def cli_query_upload_server_root_certificate(self, method, filepath=None):
        """4.5.6 查询和上传服务器根证书（syslog -d rootcertificate）
            命令功能: syslog -d rootcertificate命令可将syslog服务器的根证书上传到iBMC，或查询当前根证书信息。
            命令格式: ipmcget -t syslog -d rootcertificate
                    ipmcset -t syslog -d rootcertificate -v <filepath>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            filepath: 表示待上传的根证书在iBMC上的绝对路径。
        """
        params = {"method": method, "filepath": filepath}
        result = self.dispatcher.dispatch("cli_query_upload_server_root_certificate", params=params)[0]["parser"]
        return result

    @validate_param(method=str, filepath=str)
    def cli_query_upload_local_certificate(self, method, password=None, filepath=None):
        """4.5.7 查询和上传本地证书（syslog -d clientcertificate）
            命令功能: syslog -d clientcertificate命令可将syslog客户端证书上传到iBMC，或查询本地证书信息。
            命令格式: ipmcget -t syslog -d clientcertificate
                    ipmcset -t syslog -d clientcertificate -v <filepath> <password>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            filepath: 表示待上传的本地证书在iBMC上的绝对路径。
            password: 表示用于解密本地证书的密码。
        """
        params = {"method": method, "password": password, "filepath": filepath}
        result = self.dispatcher.dispatch("cli_query_upload_local_certificate", params=params)[0]["parser"]
        return result

    @validate_param(method=str, ipaddr=str)
    def cli_set_syslog_server_address(self, method, destination=None, ipaddr=None):
        """4.5.8 设置syslog服务器地址（syslog -d address）
            命令功能: syslog -d address命令用于设置syslog服务器地址。
            命令格式: ipmcset -t syslog -d address -v <destination> <ipaddr>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            destination: 表示syslog上报通道的编号。
            ipaddr: 表示syslog服务器地址。
        """
        params = {"method": method, "destination": destination, "ipaddr": ipaddr}
        result = self.dispatcher.dispatch("cli_set_syslog_server_address", params=params)[0]["parser"]
        return result

    @validate_param(method=str, portvalue=str)
    def cli_set_syslog_server_port_number(self, method, destination=None, portvalue=None):
        """4.5.9 设置syslog服务器端口号（syslog -d port）
            命令功能: syslog -d port命令用于设置syslog服务器端口号。
            命令格式: ipmcset -t syslog -d port -v <destination> <portvalue>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            destination: 表示syslog上报通道的编号。
            portvalue: 表示syslog服务器端口号。
        """
        params = {"method": method, "destination": destination, "portvalue": portvalue}
        result = self.dispatcher.dispatch("cli_set_syslog_server_port_number", params=params)[0]["parser"]
        return result

    @validate_param(method=str, type=str)
    def cli_set_logs_types_report(self, method, destination=None, type=None):
        """4.5.10 设置上报日志类型（syslog -d logtype）
            命令功能: syslog -d logtype命令用于设置通过syslog报文上报的日志的类型。
            命令格式: ipmcset -t syslog -d logtype -v <destination> <type>
        Args:
            method: 执行命令方式，查询或者配置，参数为query/set。
            destination: 表示syslog上报通道的编号。
            type: 表示上报日志类型。
        """
        params = {"method": method, "destination": destination, "type": type}
        result = self.dispatcher.dispatch("cli_set_logs_types_report", params=params)[0]["parser"]
        return result

    def cli_test_reachability_syslog_server(self, destination=None):
        """4.5.11 测试syslog服务器是否可连接（syslog -d test）
            命令功能: syslog -d test命令用于测试配置的syslog服务器是否可连接。
            命令格式: ipmcset -t syslog -d test -v <destination>
        Args:
            destination: 表示syslog上报通道的编号。
        """
        params = {"destination": destination}
        result = self.dispatcher.dispatch("cli_test_reachability_syslog_server", params=params)[0]["parser"]
        return result

    def cli_query_configuration_syslog_report_channels(self):
        """4.5.12 查询所有syslog上报通道配置信息（syslog -d iteminfo）
        命令功能: syslog -d iteminfo命令用于查询4条syslog日志上报通道的配置情况。
        命令格式: ipmcget -t syslog -d iteminfo
        """
        result = self.dispatcher.dispatch("cli_query_configuration_syslog_report_channels")[0]["parser"]
        return result
