class EngineError(Exception):
    """引擎基础异常"""

    pass


class ConfigError(EngineError):
    """配置错误"""

    pass


class AdapterError(EngineError):
    """适配器错误"""

    pass


class RunnerError(EngineError):
    """运行器错误"""

    pass


class HookError(EngineError):
    """钩子错误"""

    pass


class TestCaseError(EngineError):
    """测试用例错误"""

    pass


class ResourceError(EngineError):
    """资源错误"""

    pass
