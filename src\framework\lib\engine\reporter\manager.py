"""报告管理器模块"""
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Type

from .base import BaseReporter

class ReportManager:
    """报告管理器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化报告管理器
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.reporters: List[BaseReporter] = []
        # 基础报告目录
        self.base_dir = Path(config.get("report_dir", "reports"))
        # 实际报告目录(带时间戳)
        self.report_dir = None

    def initialize(self) -> None:
        """初始化报告管理器"""
        # 创建基础目录
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建带时间戳的报告目录
        timestamp = datetime.now().strftime("%Y-%m-%d_%H_%M_%S")
        self.report_dir = self.base_dir / timestamp
        self.report_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化所有报告生成器
        enabled_reports = self.config.get("enabled", ["html"])
        for report_type in enabled_reports:
            try:
                # 为每种报告类型创建独立目录
                report_type_dir = self.report_dir / report_type
                report_type_dir.mkdir(parents=True, exist_ok=True)

                reporter = BaseReporter.create(report_type, self.config)
                reporter.initialize(str(report_type_dir))
                self.reporters.append(reporter)
            except Exception as e:
                print(f"Failed to initialize {report_type} report: {e}")

    def start_suite(self, suite_info: Dict[str, Any]) -> None:
        """开始测试套件
        
        Args:
            suite_info: 套件信息
        """
        for reporter in self.reporters:
            if hasattr(reporter, 'start_suite'):
                reporter.start_suite(suite_info)

    def end_suite(self, suite_result: Dict[str, Any]) -> None:
        """结束测试套件
        
        Args:
            suite_result: 套件结果
        """
        for reporter in self.reporters:
            if hasattr(reporter, 'end_suite'):
                reporter.end_suite(suite_result)

    def start_test(self, case_info: Dict[str, Any]) -> None:
        """开始测试用例"""
        for reporter in self.reporters:
            reporter.start_test(case_info)

    def end_test(self, case_result: Dict[str, Any]) -> None:
        """结束测试用例"""
        for reporter in self.reporters:
            reporter.end_test(case_result)

    def add_log(self, log_entry: Dict[str, Any]) -> None:
        """添加日志记录"""
        for reporter in self.reporters:
            try:
                reporter.add_log(log_entry)
            except Exception as e:
                print(f"Failed to add log to reporter {reporter.__class__.__name__}: {e}")

    def generate_reports(self) -> None:
        """生成所有报告"""
        for reporter in self.reporters:
            try:
                reporter.generate()
            except Exception as e:
                print(f"Failed to generate report: {e}") 