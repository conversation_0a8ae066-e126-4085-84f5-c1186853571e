#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 管理器管理 API

提供管理器管理相关功能:
- 管理器资源配置
- 网络服务管理
- 日志服务管理
- 虚拟媒体管理
- 序列控制台管理

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Manager(ApiBase):
    """管理器管理 API"""

    def get_managers(self) -> List[Dict[str, Any]]:
        """获取管理器列表"""
        return self.dispatcher.dispatch("redfish_get_managers")

    def get_manager(self) -> Dict[str, Any]:
        """获取管理器信息"""
        return self.dispatcher.dispatch("redfish_get_manager")

    def reset_manager(self, params: Dict[str, Any]) -> None:
        """
        重置管理器

        参数:
            params: 参数字典，常用字段:
                - ResetType: 重置类型(ForceRestart/GracefulRestart)
        """
        return self.dispatcher.dispatch("redfish_reset_manager", params=params)

    def get_network_protocol(self) -> Dict[str, Any]:
        """获取网络协议配置"""
        return self.dispatcher.dispatch("redfish_get_network_protocol")

    def set_network_protocol(self, params: Dict[str, Any]) -> None:
        """
        设置网络协议配置

        参数:
            params: 参数字典，常用字段:
                - HTTPS: HTTPS配置
                - SSH: SSH配置
                - SNMP: SNMP配置
                等
        """
        return self.dispatcher.dispatch("redfish_set_network_protocol", params=params)

    def get_log_services(self) -> List[Dict[str, Any]]:
        """获取日志服务列表"""
        return self.dispatcher.dispatch("redfish_get_log_services")

    def get_log_entries(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        获取日志条目列表

        参数:
            params: 参数字典，常用字段:
                - log_service_id: 日志服务ID
        """
        return self.dispatcher.dispatch("redfish_get_log_entries", params=params)

    def clear_log(self, params: Dict[str, Any]) -> None:
        """
        清除日志

        参数:
            params: 参数字典，常用字段:
                - log_service_id: 日志服务ID
        """
        return self.dispatcher.dispatch("redfish_clear_log", params=params)

    def get_virtual_media(self) -> List[Dict[str, Any]]:
        """获取虚拟媒体列表"""
        return self.dispatcher.dispatch("redfish_get_virtual_media")

    def insert_virtual_media(self, params: Dict[str, Any]) -> None:
        """
        插入虚拟媒体

        参数:
            params: 参数字典，常用字段:
                - virtual_media_id: 虚拟媒体ID
                - Image: 镜像URI
                - TransferProtocol: 传输协议
                - UserName: 用户名(可选)
                - Password: 密码(可选)
        """
        return self.dispatcher.dispatch("redfish_insert_virtual_media", params=params)

    def eject_virtual_media(self, params: Dict[str, Any]) -> None:
        """
        弹出虚拟媒体

        参数:
            params: 参数字典，常用字段:
                - virtual_media_id: 虚拟媒体ID
        """
        return self.dispatcher.dispatch("redfish_eject_virtual_media", params=params)

    def get_serial_interfaces(self) -> List[Dict[str, Any]]:
        """获取串口接口列表"""
        return self.dispatcher.dispatch("redfish_get_serial_interfaces")

    def set_serial_interface(self, params: Dict[str, Any]) -> None:
        """
        设置串口接口配置

        参数:
            params: 参数字典，常用字段:
                - BitRate: 波特率
                - DataBits: 数据位
                - Parity: 校验位
                - StopBits: 停止位
                等
        """
        return self.dispatcher.dispatch("redfish_set_serial_interface", params=params)

    @validate_param(logservice_id=str)
    def redfish_managers_query_log(self, **kwargs):
        """3.2.102 查询日志集合资源信息
            查询服务器当前日志集合资源信息。
            操作类型：GET
            URL：https://device_ip/redfish/v1/Managers/manager_id/LogServices/logservice_id/Entries

        Args:
            manager_id: 系统资源的ID
            logservice_id: 日志服务资源的ID
        """
        params = {}
        for key, value in kwargs.items():
            params[key] = value
        result = self.dispatcher.dispatch("redfish_managers_query_log", params=params)
        return result

    def get_lldp_service_info(self) -> Dict[str, Any]:
        """获取LLDP服务信息"""
        return self.dispatcher.dispatch("redfish_get_lldp")

    def set_lldp_service_info(self, params: Dict[str, Any]) -> None:
        """
        设置LLDP服务信息

        参数:
            params: 参数字典，常用字段:
                - LLDPEnable: 是否启用LLDP
                - LLDPInterval: LLDP发送间隔
                - LLDPTLVSelector: LLDP TLV选择器
        """
        return self.dispatcher.dispatch("redfish_set_lldp", params=params)

    def redfish_managers_query_ethernet_interfaces(self) -> Dict[str, Any]:
        """
        查询服务器指定管理资源的网口集合信息，当前仅可查询iBMC管理资源的网口集合资源信息。
        操作类型：GET
        URL：https://device_ip/redfish/v1/Managers/manager_id/EthernetInterfaces
        Returns:
            dict : 查询网口资源信息
        """
        return self.dispatcher.dispatch("redfish_query_ethernet_interfaces")

    def redfish_managers_query_ethernet_interfaces_byid(self, params: Dict[str, Any]) -> None:
        """
        查询服务器指定iBMC网口资源信息，当前仅可查询iBMC管理网口的资源信息。
        操作类型：GET
        URL：https://device_ip/redfish/v1/Managers/manager_id/EthernetInterfaces/ethernetinterface_id
        Returns:
            dict : 查询指定网口资源信息
        """
        return self.dispatcher.dispatch("redfish_query_ethernet_interfaces_byid", params)

    def redfish_managers_get_ntp_services(self) -> None:
        """
        获取NTP 服务信息

        URL: /redfish/v1/Managers/{manager_id}/NtpServices
        请求方式: GET
        """
        return self.dispatcher.dispatch("redfish_get_ntp_services")

    def redfish_managers_set_ntp_services(self, params: Dict[str, Any]) -> None:
        """
        设置NTP 服务信息
        URL: /redfish/v1/Managers/{manager_id}/NtpServices
        请求方式: patch
        """
        return self.dispatcher.dispatch("redfish_set_ntp_services", params)
