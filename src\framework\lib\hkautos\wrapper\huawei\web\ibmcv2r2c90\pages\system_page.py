import os

# from hkautos.wrapper.gui.web.ibmc.ibmcv2r2c90.pages.system.sysinfopage import SysInfoPage
from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.system.systeminfo_page import (
    SystemInfoPage,
)
from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.system.powermgt_page import (
    PowerMgtPage,
)

# from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.base_page import BasePage
# from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.system.hardpartsettingpage import (
#     HardPartSettingPage,
# )
from hkautos.wrapper.uniwebs.pagebase import PageBase

from hkautos.wrapper.huawei.web.ibmcv2r2c90.pages.system.fanmgt_page import (
    FanMgtPage,
)
# from hkautos.wrapper.gui.web.ibmc.ibmcv2r2c90.pages.system.biossettingpage import (
#     BiosSettingPage,
# )
# from hkautos.wrapper.gui.web.ibmc.ibmcv2r2c90.pages.system.monitorpage import MonitorPage


class SystemPage(PageBase):
    """
    iBMC系统管理页面

    Args:
        HuaweiWebBase    (UniWebBase): WebBase实例对象

    Returns:
        SystemPage (instance)

    Raises:
        None
    """

    LocatorFile = os.path.join("system.json")

    def __init__(self, WebConnetion):
        super(SystemPage, self).__init__(WebConnetion)
        self.web_init_elements_dict(self.LocatorFile)

    @property
    def web_power_mgt_page(self):
        page = PowerMgtPage(self.WebConnection)
        return page

    @property
    def web_fan_mgt_page(self):
        page = FanMgtPage(self.WebConnection)
        return page

    @property
    def web_system_info_page(self):
        page = SystemInfoPage(self.WebConnection)
        return page
