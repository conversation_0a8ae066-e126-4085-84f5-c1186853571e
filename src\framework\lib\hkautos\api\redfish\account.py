#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 账户管理 API

提供账户管理相关功能:
- 账户服务配置
- 用户账户管理
- 角色权限管理
- 密码策略控制

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Account(ApiBase):
    """账户管理 API"""

    def get_account_service(self) -> Dict[str, Any]:
        """
        获取账户服务信息

        URL: /redfish/v1/AccountService
        请求方式: GET

        返回:
            账户服务信息
        """
        return self.dispatcher.dispatch("redfish_get_account_service")

    def set_account_service(self, params: Dict[str, Any]) -> None:
        """
        设置账户服务配置

        参数:
            params: 参数字典，常用字段:
                - AccountLockoutThreshold: 账户锁定阈值
                - AccountLockoutDuration: 账户锁定时长
                - MinPasswordLength: 最小密码长度
                等
        """
        return self.dispatcher.dispatch("redfish_set_account_service", params=params)

    def get_accounts(self) -> List[Dict[str, Any]]:
        """
        获取所有账户信息

        URL: /redfish/v1/AccountService/Accounts
        请求方式: GET

        返回:
            所有账户信息列表
        """
        return self.dispatcher.dispatch("redfish_get_accounts")

    def get_account(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取指定账户信息

        URL: /redfish/v1/AccountService/Accounts/{account_id}
        请求方式: GET

        参数:
            params: 参数字典，常用字段:
                - account_id: 账户ID

        返回:
            指定账户信息
        """
        return self.dispatcher.dispatch("redfish_get_account", params=params)

    def create_account(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建账户

        URL: /redfish/v1/AccountService/Accounts
        请求方式: POST

        参数:
            params: 参数字典，常用字段:
                - UserName: 用户名
                - Password: 密码
                - RoleId: 角色ID
                - Enabled: 是否启用
                等

        返回:
            创建的账户信息
        """
        return self.dispatcher.dispatch("redfish_create_account", params=params)

    def modify_account(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        修改账户

        URL: /redfish/v1/AccountService/Accounts/{account_id}
        请求方式: PATCH

        参数:
            params: 参数字典，常用字段:
                - account_id: 账户ID
                - Password: 新密码
                - RoleId: 新角色ID
                - Enabled: 是否启用
                等

        返回:
            修改后的账户信息
        """
        return self.dispatcher.dispatch("redfish_modify_account", params=params)

    def delete_account(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        删除账户

        URL: /redfish/v1/AccountService/Accounts/{account_id}
        请求方式: DELETE

        参数:
            params: 参数字典，常用字段:
                - account_id: 账户ID

        返回:
            删除操作的结果
        """
        return self.dispatcher.dispatch("redfish_delete_account", params=params)

    def get_roles(self) -> List[Dict[str, Any]]:
        """
        获取所有角色信息

        URL: /redfish/v1/AccountService/Roles
        请求方式: GET

        返回:
            所有角色信息列表
        """
        return self.dispatcher.dispatch("redfish_get_roles")

    def get_role(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取指定角色信息

        URL: /redfish/v1/AccountService/Roles/{role_id}
        请求方式: GET

        参数:
            params: 参数字典，常用字段:
                - role_id: 角色ID

        返回:
            指定角色信息
        """
        return self.dispatcher.dispatch("redfish_get_role", params=params)

    def create_role(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建角色

        URL: /redfish/v1/AccountService/Roles
        请求方式: POST

        参数:
            params: 参数字典，常用字段:
                - RoleId: 角色ID
                - AssignedPrivileges: 权限列表
                - OemPrivileges: 自定义权限列表

        返回:
            创建的角色信息
        """
        return self.dispatcher.dispatch("redfish_create_role", params=params)

    def update_role(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新角色信息

        URL: /redfish/v1/AccountService/Roles/{role_id}
        请求方式: PATCH

        参数:
            params: 参数字典，常用字段:
                - role_id: 角色ID
                - AssignedPrivileges: 新权限列表
                - OemPrivileges: 新自定义权限列表

        返回:
            更新后的角色信息
        """
        return self.dispatcher.dispatch("redfish_update_role", params=params)

    def delete_role(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        删除角色

        URL: /redfish/v1/AccountService/Roles/{role_id}
        请求方式: DELETE

        参数:
            params: 参数字典，常用字段:
                - role_id: 角色ID

        注意:
            预定义角色不能删除

        返回:
            删除操作的结果
        """
        try:
            self.dispatcher.dispatch("redfish_delete_role", params=params)
        except HKTestException as e:
            raise HKTestException("删除角色失败: ") from e
