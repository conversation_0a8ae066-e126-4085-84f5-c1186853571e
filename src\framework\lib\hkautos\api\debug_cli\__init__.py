import os
from hkautos.api.namespace import NameSpace
from hkautos.utils.codec import import_sub_dir
from pathlib import Path

debug_cli_ns = NameSpace("DebugCli")


def init_module(api):
    # base_dir = os.path.dirname(os.path.abspath(__file__))
    # import_sub_dir(base_dir)
    # api.add_namespace(cli_ns)
    # 获取当前目录路径
    base_dir = Path(__file__).parent
    # 导入子目录模块
    import_sub_dir(base_dir)
    # 注册IPMI命名空间
    api.add_namespace(debug_cli_ns)
