"""
Description: maint_debug_cli命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2025/2/01 11:11 created

"""
from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType
from hkautos.utils.type_check import validate_param
from hkautos.api.debug_cli import debug_cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@debug_cli_ns.dispatchertype(HostType.BMC)
class Maintdebug(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Maintdebug, self).__init__()
        self.name = "maintdebug"

    @validate_param(attribute_name=str)
    def maintdebug_get_attribute_name(self, attribute_name):
        """4.15.1 查找属性
            命令功能: getprop命令用于查找对象。
            命令格式: getprop [attribute_name]
        Args:
            attribute_name: 要查找的属性名
        """
        params = {"attribute_name": attribute_name}
        result = self.dispatcher.dispatch("getprop_attribute", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(object_name=str)
    def maintdebug_get_object_name(self, object_name):
        """4.15.2 查找对象
            命令功能: lsobj命令用于查找对象。
            命令格式: lsobj [object_name]
        Args:
            object_name: 要查找的对象名
        """
        params = {"object_name": object_name}
        result = self.dispatcher.dispatch("getobj_attribute", params=params)[0][
            "parser"
        ]
        return result

    @validate_param(property_name=str)
    def maintdebug_get_property_name(self, property_name):
        """4.15.3 查找特性
            命令功能: lsprop命令用于查找特性。
            命令格式: lsprop [property_name]
        Args:
            property_name: 要查找的特性名
        """
        params = {"property_name": property_name}
        result = self.dispatcher.dispatch("lsprop_attribute", params=params)[0][
            "parser"
        ]
        return result

    def get_object_index(self, product_name):
        """
        获取部件对象的后缀
            Args:
                product_name: 产品名称
            Returns:
                返回部件对象的后缀
            Raises:
                None
            Examples:
                server.get_object_index()
        """
        result = dict()
        # self.bmc_version = self.getActiveController().getHostObject()
        # if self.bmc_version.isiBMCV3():
        # todo 适配ibmc_v3
        if False:
            pass
            # params = {'objName': 'CPU'}
            # result.update({'Cpu': self.dispatch('getLsObjInfoV3', params)[0]['parser'][0].split('_')[-1]})
            # params1 = {'objName': 'Fan'}
            # result.update({'FANClass': self.dispatch('getLsObjInfoV3', params1)[0]['parser'][0].split('_')[-1]})
            # params3 = {'objName': 'Scanner'}
            # scanner_index = self.dispatch('getLsObjInfoV3', params3)[0]['stdout']
            # for scanner in scanner_index:
            #     if 'Inlet' in scanner:
            #         in_index = scanner
            #     if 'Outlet' in scanner:
            #         out_index = scanner
            #
            # result.update({'Inouttemp': in_index.split('_')[-1]})
            # result.update({'outtemp': out_index.split('_')[-1]})
            # params4 = {'objName': 'OnePower'}
            # result.update({'Psu': self.dispatch('getLsObjInfoV3', params4)[0]['parser'][0].split('_')[-1]})
            # params5 = {'objName': 'CoolingConfig'}
            # result.update({'CoolingConfig': self.dispatch('getLsObjInfoV3', params5)[0]['parser'][0].split('_')[-1]})
            # params6 = {'objName': 'FanBoard'}
            # result.update({'FanBoard': self.dispatch('getLsObjInfoV3', params6)[0]['parser'][0].split('_')[-1]})
        else:
            if product_name != 'RM211':
                params = {'object_name': 'Cpu'}
                result.update({'Cpu': self.dispatcher.dispatch("getobj_attribute", params=params)[0]["parser"].split('-')[-1]})
            params1 = {'objName': 'FANClass'}
            result.update({'FANClass': self.dispatcher.dispatch("getobj_attribute", params=params1)[0]["parser"].split('-')[-1]})
            if product_name not in ('Atlas 800D G1', 'RM211'):
                params2 = {'objName': 'HDDBackplane'}
                result.update({'HDDBackplane': self.dispatcher.dispatch("getobj_attribute", params=params2)[0]["parser"].split('-')[-1]})
        return result