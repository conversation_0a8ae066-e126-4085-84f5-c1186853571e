"""
Description: FAN LOGIC

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/7/24 9:15

"""
import copy

from hkautos import log
from hkautos.config.enum import HostType

from tests.test_logic.alias import BMCMgt
from tests.test_logic.logic import Logic
from tests.test_logic.hardware_mgt.components.fan_mgt import FanMgt
from tests.test_logic.system_mgt.logics.logs_logic import LogLogic


class FanLogic(Logic):
    alias = BMCMgt.FanControl

    def __init__(self, owning_device):
        super(FanLogic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.bmc_host = self.owning_device.get_host(HostType.BMC)
        self.fanComponent = FanMgt(owning_device)
        self.logobj = LogLogic(owning_device)
        self.bmc_host = self.owning_device.get_host(HostType.BMC, host_id="1")

    def check_fan_info(self, hardware_info, mode="web"):
        """通过web/redfish读取FAN信息，检查信息是否正确
        Args:
            hardware_info：硬件配置表
            mode:   接口模式，可选”web“、”redfish“，缺省值为“web”
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        if mode == "web":
            fans_info = self.fanComponent.web_api.web_get_fan_info()
            lut = self.fanComponent.web_api.web_get_system_lut()["fan_info"]
        elif mode == "redfish":
            pass
            # TODO: 待实现
        else:
            self.logger.error(f"参数mode当前只支持“web”、“redfish”，暂不支持{mode}")
            return False

        self.logger.info(f"web获取的信息: {fans_info}")
        fans_hardware_info = copy.deepcopy(hardware_info["Others"]["Fan_Model"])

        if len(fans_hardware_info) != len(fans_info):
            self.logger.error(f"【风扇模块数量不正确】：\n{mode}获取的数量: {len(fans_info)}\n硬件配置文件数量: "
                              f"{len(fans_hardware_info)}")
            return False

        for fan_hw_info in fans_hardware_info:
            key = "fan" + fan_hw_info["fanSlot"] + "_name"
            fan_hw_info.update({"name": lut[key]})
            del fan_hw_info["fanSlot"]
        for fan_info in fans_info:
            for fan_hw_info in fans_hardware_info:
                if set(fan_hw_info.values()) & set(fan_info.values()) == set(fan_hw_info.values()):
                    self.logger.info(f"【信息一致】：\nWeb获取的值:{fan_info}\n硬件配置文件信息:{fan_hw_info}")
                    # 删除配置表中已经匹配到元素，防止反复对同一个元素进行匹配
                    fans_hardware_info.remove(fan_hw_info)
                    break

                # hardware_info配置文件中最后一个元素都没有匹配到，匹配失败
                if fan_hw_info == fans_hardware_info[-1]:
                    self.logger.error(f"【信息不一致】：\nWeb获取的值: {fan_info}\n硬件配置文件信息: {fans_hardware_info}")
                    return False
        return True
