"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class IpmiShellDevice(ApiBase):
    def __init__(self):
        super(IpmiShellDevice, self).__init__()

    def session_info_active(self):
        """
        通过ipmitool shell交互模式获取会话信息

        Args:

        Returns:

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmitool_shell_session_info_active")[0]["parser"]

    def raw_session_info_active(self):
        """
        通过ipmitool shell交互模式通过raw获取会话信息

        Args:

        Returns:

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmitool_shell_raw_session_info_active")[0]["parser"]

    def edit_session_privilege_level(self):
        """
        通修改ipmi shell交互方式获取会话权限

        Args:

        Returns:

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmitool_shell_edit_session_privilege_level")[0]["parser"]

    def ipmi_shell_set_bmc_cold_reset(self):
        """
        通过ipmitool shell冷复位BMC

        Args:

        Returns:

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_shell_set_bmc_cold_reset")[0]["parser"]