import unittest
from typing import Any, Dict

from ...utils.exceptions import AdapterError


class UnittestResultParser:
    """Unittest 测试结果解析器"""

    def parse_result(self, result: unittest.TestResult) -> Dict[str, Any]:
        """解析 unittest 的 TestResult 对象"""
        try:
            status = "passed"
            if result.failures or result.errors:
                status = "failed"
            elif result.skipped:
                status = "skipped"

            details = {
                "total": result.testsRun,
                "failures": len(result.failures),
                "errors": len(result.errors),
                "skipped": len(result.skipped),
            }

            # 收集错误信息
            error_details = []
            for test, err in result.failures:
                error_details.append({"type": "failure", "test": str(test), "message": err})
            for test, err in result.errors:
                error_details.append({"type": "error", "test": str(test), "message": err})

            return {"status": status, "details": details, "errors": error_details}

        except Exception as e:
            raise AdapterError(f"Failed to parse unittest result: {str(e)}") from e
