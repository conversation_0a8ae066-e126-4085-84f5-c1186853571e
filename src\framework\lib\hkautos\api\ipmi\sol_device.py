"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class SolDevice(ApiBase):
    def __init__(self):
        super(SolDevice, self).__init__()

    def get_sol_info(self, params=None):
        """
        获取SOL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SOL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sol_info", params=params)[0]["parser"]

    def get_sol_config(self, params=None):
        """
        获取SOL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SOL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sol_config", params=params)[0]["parser"]

    def set_sol_config(self, params=None):
        """
        设置SOL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SOL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sol_config", params=params)[0]["parser"]

    def set_sol(self, params=None):
        """
        设置SOL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SOL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_set_sol", params=params)[0]["parser"]

    def create_sol(self, params=None):
        """
        设置SOL信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SOL信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_create_sol", params=params)[0]["parser"]

