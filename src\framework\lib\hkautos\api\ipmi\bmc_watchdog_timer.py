"""BMC Watchdog Timer IPMI API.

提供BMC看门狗定时器相关的IPMI操作接口，包括重置、设置和获取看门狗定时器状态。

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司
"""

from typing import Dict, Optional

from hkautos.api.apibase import ApiBase
from hkautos.api.ipmi import ipmi_ns
from hkautos.config.enum import HostType


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class BMCWatchdogTimer(ApiBase):
    """BMC看门狗定时器管理类."""

    def ipmi_reset_watchdog_timer(self, params: Optional[Dict] = None) -> Dict:
        """复位看门狗定时器.
        IPMI Command: App 06h 22h (操作员权限)
        Args:
            params: 可选的参数字典
        Returns:
            Dict: 命令执行结果
        """
        return self.dispatcher.dispatch("ipmi_reset_watchdog_timer", params=params)[0]["parser"]

    def ipmi_set_watchdog_timer(self, params: Optional[Dict] = None) -> Dict:
        """设置看门狗定时器.
        IPMI Command: App 06h 24h (操作员权限)
        Args:
            params: 可选的参数字典
        Returns:
            Dict: 命令执行结果
        """
        return self.dispatcher.dispatch("ipmi_set_watchdog_timer", params=params)[0]["parser"]

    def ipmi_get_watchdog_timer(self, params=None) -> Dict:
        """获取看门狗定时器状态.
        IPMI Command: App 06h 25h (用户权限)
        Args:
            params: 参数字典
                opt (str): 命令类型
                    - 'cmd': 发送解析后的字符串命令查询
                    - 'oem': 发送标准命令(oem命令定义表)
        Returns:
            Dict: 看门狗状态信息，包含以下字段：
                - Watchdog Timer Use
                - Watchdog Timer Is
                - Watchdog Timer Actions
                - Pre-timeout interval
                - Timer Expiration Flags
                - Initial Countdown
                - Present Countdown
        """
        return self.dispatcher.dispatch("ipmi_get_watchdog_timer", params=params)[0]["parser"]

