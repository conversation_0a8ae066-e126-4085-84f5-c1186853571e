"""
Description: 业务类型定义模块，包含接口类型和业务领域的枚举定义

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/01/01
"""
from enum import Enum, unique


@unique
class InterfaceMgt(Enum):
    """接口类型定义"""
    Redfish = "Redfish"
    IPMI = "IPMI"
    SNMP = "SNMP"
    SSH = "SSH"
    WebUI = "Web UI"
    CLI = "CLI"
    WebRest = "Web REST"
    FusionDirector = "Fusion Director"
    eSight = "eSight"


@unique
class BMCMgt(Enum):
    """BMC管理类业务"""
    Network = "Network Configuration"
    TimeZone = "Time Zone Settings"
    Firmware = "Firmware Update"
    License = "License Management"
    Watchdog = "Watchdog Control"
    Sensor = "Sensor Reading"
    Session = "Session Management"
    Certificate = "Certificate Management"
    EventLog = "Event Log"
    PowerControl = "Power Control"
    FanControl = "Fan Control"
    Storage = "Storage Management"
    UserMgt = "User Management"
    Fru = "Fru Management"
    Led = "Led Control"
    Tmu = "Tmu"
    Sol = "Sol Management"
    Compatibility = "compatibility"


@unique
class ServerMgt(Enum):
    """服务器管理类业务"""
    PowerStatus = "Power Status"
    BootOptions = "Boot Options"
    BiosSettings = "BIOS Settings"
    HardwareInfo = "Hardware Information"
    MemoryInfo = "Memory Information"
    ProcessorInfo = "Processor Information"
    StorageInfo = "Storage Information"
    NetworkAdapter = "Network Adapter"
    PCIeDevices = "PCIe Devices"
    Sensors = "Sensor Readings"
    Inventory = "System Inventory"


@unique
class ServiceMgt(Enum):
    """服务管理类业务"""
    VirtualMedia = "Virtual Media"
    VirtualConsole = "Virtual Console"
    RemoteControl = "Remote Control"
    FileTransfer = "File Transfer"
    EventService = "Event Service"
    TaskService = "Task Service"
    UpdateService = "Update Service"
    TelemetryService = "Telemetry Service"


@unique
class SecurityMgt(Enum):
    """安全管理类业务"""
    Authentication = "Authentication"
    Authorization = "Authorization"
    AccountService = "Account Service"
    SessionService = "Session Service"
    SecurityPolicy = "Security Policy"
    CertificateMgt = "Certificate Management"
    KeyManagement = "Key Management"
    AuditLog = "Audit Log"


@unique
class MonitorMgt(Enum):
    """监控管理类业务"""
    HealthStatus = "Health Status"
    PowerMetrics = "Power Metrics"
    ThermalMetrics = "Thermal Metrics"
    EventMonitor = "Event Monitor"
    AlertManagement = "Alert Management"
    Performance = "Performance Monitor"
    ResourceUsage = "Resource Usage"


@unique
class ToolMgt(Enum):
    """工具类型定义"""
    Stress = "Stress"


@unique
class ServicesMgt(Enum):
    """工具类型定义"""
    NfsServer = "Nfs Server"
    NetWorkServer = "network Server"


@unique
class SystemMgt(Enum):
    """系统管理类定义"""
    AlarmEvent = "Alarm & SEL"
    Syslog = "Syslog Notification"
    Email = "Email Notification"
    Trap = "Trap Notification"
    Diagnostics = "Diagnostics"
    Logs = "iBMC Logs"
    WorkRecords = "Work Records"
    FDM = "FDM"
    Video = "video"
    Elabel = "Elabel"
    SystemInfo = "System Info"


# todo 待修改
@unique
class ThermalMgt(Enum):
    """散热管理类型定义"""
    FanCooling = "Fan Cooling"


@unique
class CoolingMgt(Enum):
    """散热管理类型定义"""
    Tmu = "Tmu"


@unique
class HardwareMgt(Enum):
    """硬件管理类型定义"""
    Cpu = "Cpu"
    Boards = "Boards"
    Mem = "Mem"
