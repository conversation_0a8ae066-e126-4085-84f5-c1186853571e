"""
Description: HKTest配置模块

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/01/01
"""
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List, Set, Tuple, Union
from pathlib import Path
import yaml
from engine.utils.exceptions import ConfigError
import importlib.util
import sys
import concurrent.futures
import logging
import time
import os
import json
from datetime import datetime

logger = logging.getLogger(__name__)


def validate_single_case(args: Tuple[Dict[str, Any], Path, Set[str]]) -> Tuple[Dict[str, Any], List[str]]:
    """验证单个测试用例"""
    case, test_cases_root, _ = args
    errors = []
    logger = logging.getLogger('hktest.config')
    
    if not isinstance(case, dict):
        logger.error(f"无效的测试用例配置: {case}")
        return case, [f"无效的测试用例配置: {case}"]
        
    case_name = case.get("name")
    if not case_name:
        logger.error(f"测试用例配置缺少name字段: {case}")
        return case, [f"测试用例配置缺少name字段: {case}"]
    
    # 在test_cases目录及其子目录下查找测试用例文件
    found_files = list(test_cases_root.rglob(f"{case_name}.py"))
    
    if not found_files:
        logger.error(f"未找到测试用例文件: {case_name}.py")
        return case, [f"未找到测试用例文件: {case_name}.py"]
    elif len(found_files) > 1:
        logger.error(f"在多个位置找到测试用例 {case_name}")
        errors = [f"在多个位置找到测试用例 {case_name}:"]
        for file in found_files:
            logger.error(f"  - {file}")
            errors.append(f"  - {file}")
        return case, errors
    
    # 更新测试用例的路径信息
    test_file = found_files[0]
    relative_path = test_file.relative_to(test_cases_root)
    module_path = str(relative_path.parent / relative_path.stem).replace('\\', '.').replace('/', '.')
    case['path'] = module_path
    
    # 尝试导入模块验证其有效性
    try:
        spec = importlib.util.spec_from_file_location(module_path, test_file)
        if not spec or not spec.loader:
            logger.error(f"无法加载测试用例模块: {module_path}")
            return case, [f"无法加载测试用例模块: {module_path}"]
            
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_path] = module
        spec.loader.exec_module(module)
        
        # 验证测试用例类是否存在
        case_class = getattr(module, case_name, None)
        if not case_class:
            logger.error(f"在模块 {module_path} 中未找到测试用例类 {case_name}")
            return case, [f"在模块 {module_path} 中未找到测试用例类 {case_name}"]
            
    except Exception as e:
        logger.error(f"加载测试用例 {case_name} 失败: {str(e)}")
        return case, [f"加载测试用例 {case_name} 失败: {str(e)}"]
    
    return case, []


def find_test_case_file(case_name: str, test_cases_dir: Path) -> Tuple[Path, List[str]]:
    """查找测试用例文件
    
    Args:
        case_name: 测试用例名称
        test_cases_dir: 测试用例根目录
        
    Returns:
        Tuple[Path, List[str]]: (测试用例文件路径, 错误信息列表)
    """
    found_files = list(test_cases_dir.rglob(f"{case_name}.py"))
    
    if not found_files:
        return None, [f"未找到测试用例文件: {case_name}.py"]
    elif len(found_files) > 1:
        errors = [f"在多个位置找到测试用例 {case_name}:"]
        for file in found_files:
            errors.append(f"  - {file}")
        return None, errors
        
    return found_files[0], []
    
def get_module_path(test_file: Path, test_cases_dir: Path) -> str:
    """获取测试用例的模块导入路径
    
    Args:
        test_file: 测试用例文件路径
        test_cases_dir: 测试用例根目录
        
    Returns:
        str: 模块导入路径
    """
    relative_path = test_file.relative_to(test_cases_dir)
    return str(relative_path.parent / relative_path.stem).replace('\\', '.').replace('/', '.')


@dataclass
class HKTestConfig:
    """HKTest配置类
    
    管理测试框架的所有配置项，包括：
    1. 测试环境和测试集配置
    2. 运行时参数（并行、超时等）
    3. 状态上报配置
    4. 日志和报告配置
    """
    # 基础配置
    test_bed: str                                  # 测试环境配置文件路径
    test_set: str                                  # 测试集配置文件路径
    test_cases_dir: str = "tests/test_cases"       # 测试用例目录路径
    
    # 测试套件信息
    name: str = "默认测试套件"
    description: str = "默认测试套件描述"
    test_cases: List[Dict[str, Any]] = field(default_factory=list)
    
    # 运行时配置
    parallel: bool = False                         # 是否并行执行
    workers: int = 1                              # 并行工作进程数
    timeout: int = 3600                           # 执行超时时间（秒）
    retry_count: int = 1                          # 失败重试次数
    
    # 日志配置
    log_level: str = "INFO"                       # 日志级别
    log_file: Optional[str] = None                # 日志文件路径
    
    # 报告配置
    reports: Dict[str, Any] = field(default_factory=dict)
    
    # 状态上报配置
    status_report: Optional[Dict[str, Any]] = None  # 默认不启用状态上报
    
    def __post_init__(self):
        """初始化配置
        
        1. 设置日志记录器
        2. 验证配置文件路径
        3. 加载测试集配置
        4. 初始化状态上报配置
        """
        self._setup_logger()
        self._validate_paths()
        self._load_test_set()
        self._init_status_report()
        
    def _setup_logger(self):
        """设置日志记录器"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.propagate = False
        self.logger.setLevel(self.log_level)
        
        if not self.logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(self.log_level)
            formatter = logging.Formatter(
                '[%(asctime)s][%(process)d][%(levelname)s] > %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            
    def _validate_paths(self):
        """验证配置文件路径"""
        paths = {
            "测试环境配置": Path(self.test_bed),
            "测试集配置": Path(self.test_set),
            "测试用例目录": Path(self.test_cases_dir)
        }
        
        for name, path in paths.items():
            if not path.exists():
                raise ConfigError(f"{name}不存在: {path}")
            if name.endswith("配置") and not path.is_file():
                raise ConfigError(f"{name}不是文件: {path}")
            if name.endswith("目录") and not path.is_dir():
                raise ConfigError(f"{name}不是目录: {path}")
                
        self.logger.debug("配置文件路径验证通过")
        
    def _load_test_set(self):
        """加载测试集配置"""
        self.logger.debug(f"加载测试集配置: {self.test_set}")
        test_set = self._load_yaml(self.test_set)
        
        # 更新基本信息
        self.name = test_set.get('name', self.name)
        self.description = test_set.get('description', self.description)
        
        # 更新执行配置
        if execution := test_set.get('execution'):
            self.parallel = execution.get('parallel', self.parallel)
            self.timeout = execution.get('timeout', self.timeout)
            self.retry_count = execution.get('max_retries', self.retry_count)
            self.workers = execution.get('workers', self.workers)
            
        # 验证执行配置
        if self.timeout <= 0:
            raise ConfigError("超时时间必须大于0")
        if self.retry_count < 0:
            raise ConfigError("重试次数不能为负数")
        if self.parallel and self.workers <= 0:
            raise ConfigError("并行执行时工作进程数必须大于0")
            
        # 更新报告配置
        if reports := test_set.get('reports'):
            self.reports = reports
            
        # 验证测试用例
        test_cases = test_set.get('test_cases', [])
        if not isinstance(test_cases, list):
            raise ConfigError("测试用例配置必须是列表格式")
        self.test_cases = test_cases
        
    def _init_status_report(self):
        """初始化状态上报配置"""
        # 如果没有task_id，则不启用状态上报
        if not self.status_report or not self.status_report.get('task_id'):
            self.status_report = None
            self.logger.debug("状态上报未启用")
            return
            
    def _load_yaml(self, path: str) -> Dict[str, Any]:
        """加载YAML文件"""
        with open(path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
            if not isinstance(data, dict):
                raise ConfigError(f"YAML文件格式错误: {path}")
            return data
