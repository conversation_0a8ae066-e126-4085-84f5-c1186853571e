import logging
from typing import Any, Dict

from .registry import HookRegistry


class DefaultHooks:
    """默认的钩子处理类"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.tag = "[Default]"  # 添加标签

    def register_to(self, registry: HookRegistry):
        """注册默认钩子到注册器"""
        # 套件级别钩子
        registry.register("before_suite", self.on_suite_start)
        registry.register("after_suite", self.on_suite_end)

        # 用例级别钩子
        registry.register("before_test", self.on_test_start)
        registry.register("after_test", self.on_test_end)

        # 结果钩子
        registry.register("before_pass", self.on_test_pass)
        registry.register("after_pass", self.on_test_pass_complete)
        registry.register("before_fail", self.on_test_fail)
        registry.register("after_fail", self.on_test_fail_complete)
        registry.register("before_skip", self.on_test_skip)
        registry.register("after_skip", self.on_test_skip_complete)

    def on_suite_start(self):
        """测试套件开始时的钩子"""
        self.logger.info(f"{self.tag} 测试套件开始执行")

    def on_suite_end(self, results):
        """测试套件结束时的钩子"""
        self.logger.info(f"{self.tag} 测试套件执行完成")

    def on_test_start(self, test_case: Dict[str, Any]):
        """测试用例开始时的钩子"""
        self.logger.info(f"{self.tag} 开始执行测试用例: {test_case['name']}")

    def on_test_end(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例结束时的钩子"""
        self.logger.info(f"{self.tag} 测试用例执行完成: {test_case['name']}, 状态: {result['status']}")


    def on_test_pass(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例通过时的钩子"""
        self.logger.info(f"{self.tag} 测试用例通过: {test_case['name']}")

    def on_test_pass_complete(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例通过后的钩子"""
        self.logger.info(f"{self.tag} 测试用例通过处理完成: {test_case['name']}")

    def on_test_fail(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例失败时的钩子"""
        self.logger.error(f"{self.tag} 测试用例失败: {test_case['name']}")
        if "details" in result:
            for detail in result["details"]:
                if "error" in detail:
                    self.logger.error(f"{self.tag} 错误信息: {detail['error']}")

    def on_test_fail_complete(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例失败后的钩子"""
        self.logger.info(f"{self.tag} 测试用例失败处理完成: {test_case['name']}")

    def on_test_skip(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例跳过时的钩子"""
        self.logger.info(f"{self.tag} 测试用例跳过: {test_case['name']}")

    def on_test_skip_complete(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """测试用例跳过后的钩子"""
        self.logger.info(f"{self.tag} 测试用例跳过处理完成: {test_case['name']}")
