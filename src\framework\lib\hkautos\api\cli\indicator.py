"""
Description: 4.11 指示灯命令

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 11:11 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import cli_ns


# @cli_ns.dispatchertype(HostType.BMC, HostType.HMM)
@cli_ns.dispatchertype(HostType.BMC)
class Indicator(ApiBase):
    """BMC和HMM板共有的命令接口"""

    def __init__(self):
        super(Indicator, self).__init__()
        self.name = "indicator"

    def cli_query_state_current_indicator(self):
        """4.11.1 查询服务器指示灯信息（ledinfo）
        命令功能: ledinfo命令用来查询服务器指示灯信息。
        命令格式: ipmcget -d ledinfo
        """
        result = self.dispatcher.dispatch("cli_query_state_current_indicator")[0]["parser"]
        return result

    def cli_set_uid_indicator(self, value=None):
        """4.11.2 设置UID指示灯状态（identify）
            命令功能: identify命令用于设置UID指示灯状态。
            命令格式: ipmcset -d identify [-v {time | force} ]
        Args:
            value:
                time: 表示UID指示灯闪烁时长。
                force: 表示永久点亮UID指示灯。
        """
        params = {"value": value}
        result = self.dispatcher.dispatch("cli_set_uid_indicator", params=params)[0]["parser"]
        return result
