import logging
from typing import Any, Dict

from .registry import HookRegistry


class CleanupHooks:
    """清理相关的钩子处理类"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.tag = "[Cleanup]"  # 添加标签

    def register_to(self, registry: HookRegistry):
        """注册清理钩子到注册器"""
        # 清理相关钩子
        registry.register("before_cleanup", self.on_cleanup_start)
        registry.register("after_cleanup", self.on_cleanup_end)

    def on_cleanup_start(self, test_case: Dict[str, Any]):
        """清理开始时的钩子"""
        self.logger.info(f"{self.tag} 开始清理测试用例: {test_case['name']}")
        self.logger.info(f"{self.tag} 清理参数: {test_case.get('cleanup_params', {})}")

    def on_cleanup_end(self, test_case: Dict[str, Any], result: Dict[str, Any]):
        """清理结束时的钩子"""
        self.logger.info(f"{self.tag} 测试用例清理完成: {test_case['name']}")
        if result.get("status") == "success":
            self.logger.info(f"{self.tag} 清理成功")
        else:
            self.logger.error(f"{self.tag} 清理失败: {result.get('error', '未知错误')}")
