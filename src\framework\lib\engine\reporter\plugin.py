from abc import ABC, abstractmethod
from typing import Any, Dict

class ReporterPlugin(ABC):
    """报告插件基类"""
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件"""
        pass
        
    @abstractmethod
    def process_case(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用例数据"""
        pass
        
    @abstractmethod
    def process_log(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理日志数据"""
        pass
        
    @abstractmethod
    def finalize(self) -> None:
        """完成处理"""
        pass 