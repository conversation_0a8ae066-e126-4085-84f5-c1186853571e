#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Web风扇管理
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import web_ns


@web_ns.dispatchertype(HostType.BMC)
class FanControl(ApiBase):
    """风扇管理 API"""

    def web_get_fan_info(self) -> List[Dict[str, Any]]:
        """通过WEB获取FAN信息"""
        return self.dispatcher.dispatch("web_get_fan_info")

