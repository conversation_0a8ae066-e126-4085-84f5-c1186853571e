#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 证书管理 API

提供证书管理相关功能:
- 证书服务配置
- 证书生成
- 证书导入导出
- 证书更新
- 证书删除

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Certificate(ApiBase):
    """证书管理 API"""

    def get_certificate_service(self) -> Dict[str, Any]:
        """获取证书服务配置"""
        return self.dispatcher.dispatch("redfish_get_certificate_service")

    def get_certificates(self) -> List[Dict[str, Any]]:
        """获取证书列表"""
        return self.dispatcher.dispatch("redfish_get_certificates")

    def generate_csr(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成证书签名请求

        参数:
            params: 参数字典，常用字段:
                - CommonName: 通用名称
                - Organization: 组织名称
                - OrganizationalUnit: 组织单位
                - City: 城市
                - State: 州/省
                - Country: 国家
                - AlternativeNames: 备用名称列表
                - KeyPairAlgorithm: 密钥对算法
                - KeyBitLength: 密钥长度
                等
        """
        return self.dispatcher.dispatch("redfish_generate_csr", params=params)

    def replace_certificate(self, params: Dict[str, Any]) -> None:
        """
        替换证书

        参数:
            params: 参数字典，常用字段:
                - CertificateString: 证书内容
                - CertificateType: 证书类型(PEM/PKCS12等)
                - CertificateUri: 证书URI
        """
        return self.dispatcher.dispatch("redfish_replace_certificate", params=params)

    def delete_certificate(self, params: Dict[str, Any]) -> None:
        """
        删除证书

        参数:
            params: 参数字典，常用字段:
                - CertificateUri: 证书URI
        """
        return self.dispatcher.dispatch("redfish_delete_certificate", params=params)
