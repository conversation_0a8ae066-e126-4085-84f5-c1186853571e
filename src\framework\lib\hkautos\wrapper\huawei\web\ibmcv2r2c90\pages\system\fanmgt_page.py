import os
from typing import Any, Dict, List

from hkautos import log
from hkautos.wrapper.uniwebs.pagebase import PageBase


class FanMgtPage(PageBase):
    LocatorFile = os.path.join("system", "fan_mgt.json")

    def __init__(self, params):
        super(FanMgtPage, self).__init__(params)
        self.logger = log.get_logger(__name__)
        self.web_init_elements_dict(self.LocatorFile)

    def web_get_fan_info(self) -> List[Dict[str, Any]]:
        """
        获取WEB获取FAN信息
        Args:
            None
        Returns:
            result: List[Dict[str, Any]]，FAN信息
        """
        result = []
        self.switch_page("fan_info")
        self.WebConnection.wait_for_selector(f'//*[@id="fanId"]/div[2]/app-fan-info/div')
        count = self.WebConnection.get_elements(f'//*[@id="fanId"]/div[2]/app-fan-info/div/div[2]/*').__len__()
        for index in range(count):
            name = self.WebConnection.get_inner_text(f'//*[@id="fan{index}"]/div[1]/*')
            result_dict = {"name": name}
            items = self.WebConnection.get_inner_text(f'//*[@id="fan{index}"]/div[3]/*')
            item1 = items.replace("\t\n\t", '\t').strip()
            result_dict.update(dict(zip(item1.split("\t")[::2], item1.split("\t")[1::2])))
            result.append(result_dict)
        return result
