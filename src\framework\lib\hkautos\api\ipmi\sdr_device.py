"""
Description: Ipmi API

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司

Date: 2023/11/01 15:30 created

"""

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import ipmi_ns


@ipmi_ns.dispatchertype(HostType.Local, HostType.HostOS)
class SDRDevice(ApiBase):
    def __init__(self):
        super(SDRDevice, self).__init__()

    def get_sdr_info(self, params=None):
        """
        获取SDR信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SDR信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_info", params=params)[0]["parser"]

    def get_sdr_list(self, params=None):
        """
        获取SDR列表

        Args:
            params: 参数字典

        Returns:
            dict: 返回解析后的SDR列表信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_list", params=params)[0]["parser"]

    def get_sdr(self, params=None):
        """
        获取指定SDR记录

        Args:
            params: 参数字典
                   例如: {'sensor_id': 'CPU1 Temp'}

        Returns:
            dict: 返回解析后的SDR记录信息

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_get", params=params)[0]["parser"]

    def dump_sdr(self, params=None):
        """
        导出SDR记录到文件

        Args:
            params: 参数字典
                   例如: {'filename': 'sdr.dat'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_dump", params=params)[0]["parser"]

    def fill_sdr(self, params=None):
        """
        从文件导入SDR记录

        Args:
            params: 参数字典
                   例如: {'filename': 'sdr.dat'}

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_fill", params=params)[0]["parser"]

    def clear_sdr(self, params=None):
        """
        清除SDR记录

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_clear", params=params)[0]["parser"]

    def get_reservation_id(self, params=None):
        """
        清除SDR记录

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_reservation", params=params)[0]["parser"]

    def get_sdr_device_info(self, params=None):
        """
        清除SDR记录

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sdr_device_info", params=params)[0]["parser"]

    def get_sensor_sdr_info(self, params=None):
        """
        清除SDR记录

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sensor_sdr_info", params=params)[0]["parser"]

    def get_sdr_stores(self, params=None):
        """
        查询sdr仓库信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_get_sdr_stores", params=params)[0]["parser"]

    def get_sdr_list_event(self, params=None):
        """
        查询sdr仓库信息

        Args:
            params: 参数字典

        Returns:
            dict: 返回命令执行结果

        Raises:
            None
        """
        return self.dispatcher.dispatch("ipmi_sdr_list_event", params=params)[0]["parser"]
