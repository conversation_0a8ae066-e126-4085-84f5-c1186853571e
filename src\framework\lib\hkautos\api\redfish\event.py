#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 事件管理 API

提供事件管理相关功能:
- 事件服务配置
- 事件订阅管理
- 事件目标控制
- 事件日志监控

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Event(ApiBase):
    """事件管理 API"""

    def get_event_service(self) -> Dict[str, Any]:
        """获取事件服务配置"""
        return self.dispatcher.dispatch("redfish_get_event_service")

    def set_event_service(self, params: Dict[str, Any]) -> None:
        """
        设置事件服务配置

        参数:
            params: 参数字典，常用字段:
                - ServiceEnabled: 是否启用事件服务
                - DeliveryRetryAttempts: 重试次数
                - DeliveryRetryIntervalSeconds: 重试间隔
                等
        """
        return self.dispatcher.dispatch("redfish_set_event_service", params=params)

    def get_subscriptions(self) -> List[Dict[str, Any]]:
        """获取事件订阅列表"""
        return self.dispatcher.dispatch("redfish_get_subscriptions")

    def create_subscription(self, params: Dict[str, Any]) -> None:
        """
        创建事件订阅

        参数:
            params: 参数字典，常用字段:
                - Destination: 订阅目标URL
                - Protocol: 协议类型(Redfish/SMTP等)
                - Context: 上下文信息
                - EventTypes: 事件类型列表
                - RegistryPrefixes: 注册表前缀列表
                - ResourceTypes: 资源类型列表
                等
        """
        return self.dispatcher.dispatch("redfish_create_subscription", params=params)

    def delete_subscription(self, params: Dict[str, Any]) -> None:
        """
        删除事件订阅

        参数:
            params: 参数字典，常用字段:
                - subscription_id: 订阅ID
        """
        return self.dispatcher.dispatch("redfish_delete_subscription", params=params)

    def test_event_subscription(self, params: Dict[str, Any]) -> None:
        """
        测试事件订阅

        URL: /redfish/v1/EventService/Actions/EventService.SubmitTestEvent
        请求方式: POST

        参数:
            params: 参数字典，常用字段:
                - EventType: 事件类型
                - EventId: 事件ID
                - EventTimestamp: 事件时间戳
                - Message: 事件消息
                - MessageId: 消息ID
                - OriginOfCondition: 事件源
        """
        return self.dispatcher.dispatch("redfish_test_event_subscription", params=params)
