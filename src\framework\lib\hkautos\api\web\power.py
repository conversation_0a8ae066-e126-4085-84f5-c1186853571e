#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Web 登录 API

提供登录相关功能:
- 用户登录
- 登录状态检查
- 登出操作
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import web_ns


@web_ns.dispatchertype(HostType.BMC)
class PowerControl(ApiBase):
    """电源管理 API"""

    def web_set_power_control(self, params: Dict[str, Any]) -> None:
        """通过WEB进行机框电源管理，包含"off", "on", "forcedOff", "forcedRestart", "forcedCycle"等"""
        return self.dispatcher.dispatch("web_set_power_control", params=params)

    def web_get_psu_info(self) -> List[Dict[str, Any]]:
        """通过WEB获取PSU信息"""
        result = self.dispatcher.dispatch("web_get_psu_info")
        return result
