
"""
功   能：web查询电源信息

修改信息：
    日期：   2025/07/24
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import BMCMgt

class POWER_PSU_MGMT_FUNC_1010(Case):
    """
    CaseId:
        POWER_PSU_MGMT_FUNC_1010
    RunLevel:
        1(#1)
    CaseName:
        web查询电源信息
    PreCondition:
        1、环境正常
        2、配置相应电源
    TestStep:
        1._环境配置安装有相应PSU电源，系统上电，通过web查询电源信息是否正确。
    ExpectedResult:
        1._电源静态信息获取：厂商、类型、序列号、固件版本、序列号、部件编码、额定功率
        电源动态信息获取：输入电压、输出电压、输出功率及对应的传感器适配
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.redfish_api = self.device_dut.get_api(ns_name="Redfish")
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.web_api = self.device_dut.get_api(ns_name="Web")

        self.power_mgt = self.device_dut.find(BMCMgt.PowerControl)
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }
        self.logger.step("1. 环境配置安装有相应PSU电源，系统上电，通过web查询电源信息是否正确。")
        self.web_handle = self.web_api.login(self.web_login_params)
        res = self.power_mgt.check_power_info(self.hardware_json)
        self.assertTrue(res, "电源静态信息获取：厂商、类型、序列号、固件版本、序列号、部件编码、额定功率, "
                             "电源动态信息获取：输入电压、输出电压、输出功率及对应的传感器适配。")

    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.web_api.logout(self.web_handle)
