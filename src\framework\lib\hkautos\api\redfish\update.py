#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
Description: Redfish 更新服务 API

提供更新服务相关功能:
- 更新服务配置
- 固件资源管理
- 固件更新控制
- 更新状态监控

Copyright: ©2023-2028 四川华鲲振宇智能科技有限责任公司
"""

from typing import Any, Dict, List

from hkautos.api.apibase import ApiBase
from hkautos.config.enum import HostType

from . import redfish_ns


@redfish_ns.dispatchertype(HostType.BMC)
class Update(ApiBase):
    """更新服务 API"""

    def get_update_service(self) -> Dict[str, Any]:
        """
        获取更新服务信息

        URL: /redfish/v1/UpdateService
        请求方式: GET

        返回:
            更新服务信息，包含:
            - Id: 服务标识符
            - Name: 服务名称
            - Status: 服务状态
            - ServiceEnabled: 是否启用
            - HttpPushUri: HTTP推送URI
            - FirmwareInventory: 固件清单
            等
        """
        return self.dispatcher.dispatch("redfish_get_update_service")

    def get_firmware_inventory(self) -> List[Dict[str, Any]]:
        """
        获取固件清单

        URL: /redfish/v1/UpdateService/FirmwareInventory
        请求方式: GET

        返回:
            固件列表，每个固件包含:
            - Id: 固件ID
            - Name: 固件名称
            - Version: 固件版本
            - Updateable: 是否可更新
            - SoftwareId: 软件ID
            等
        """
        return self.dispatcher.dispatch("redfish_get_firmware_inventory")

    def get_firmware(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取固件信息

        URL: /redfish/v1/UpdateService/FirmwareInventory/{firmware_id}
        请求方式: GET

        参数:
            params: 参数字典，常用字段:
                - firmware_id: 固件ID

        返回:
            固件详细信息
        """
        return self.dispatcher.dispatch("redfish_get_firmware", params=params)

    def simple_update(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        简单更新固件

        URL: /redfish/v1/UpdateService/Actions/UpdateService.SimpleUpdate
        请求方式: POST

        参数:
            params: 参数字典，常用字段:
                - ImageURI: 固件镜像URI
                - TransferProtocol: 传输协议(HTTPS/SFTP等)
                - Targets: 目标设备URI列表
                - Username: 用户名(可选)
                - Password: 密码(可选)

        返回:
            更新任务信息
        """
        return self.dispatcher.dispatch("redfish_simple_update", params=params)

    def start_update(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        开始更新固件

        URL: /redfish/v1/UpdateService/Actions/UpdateService.StartUpdate
        请求方式: POST

        参数:
            params: 参数字典，常用字段:
                - SoftwareImage: 固件镜像URI
                - Targets: 目标设备URI列表
                - ResetType: 重启类型

        返回:
            更新任务信息
        """
        return self.dispatcher.dispatch("redfish_start_update", params=params)

    def cancel_update(self, params: Dict[str, Any]) -> None:
        """
        取消更新

        URL: /redfish/v1/UpdateService/Actions/UpdateService.CancelUpdate
        请求方式: POST

        参数:
            params: 参数字典，常用字段:
                - task_id: 任务ID
        """
        return self.dispatcher.dispatch("redfish_cancel_update", params=params)

    def get_update_status(self, params: Dict[str, Any]) -> None:
        """
        获取任务状态

        URL: /redfish/v1/TaskService/Tasks/{task_id}
        请求方式: POST
        参数:
            params: 参数字典，常用字段:
                - task_id: 任务ID
        """
        return self.dispatcher.dispatch("redfish_get_update_status", params=params)
