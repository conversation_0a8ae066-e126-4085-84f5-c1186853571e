<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试报告</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* 内联样式，确保基本样式可见 */
        body { font-family: Arial; padding: 10px; }
        .debug-info { background: #f8d7da; padding: 10px; margin: 10px 0; }
        
        /* 工具提示样式 */
        .tooltip-container {
            position: relative;
            display: inline-block;
        }
        
        .tooltip-container:hover .tooltip-content {
            visibility: visible;
            opacity: 1;
        }
        
        .tooltip-content {
            visibility: hidden;
            opacity: 0;
            position: absolute;
            z-index: 1000;
            width: 400px;
            max-width: 90vw;
            left: 0;
            top: 100%;
            margin-top: 5px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            padding: 10px;
            transition: opacity 0.3s;
            font-weight: normal;
            text-align: left;
            color: #333;
        }
        
        .tooltip-title {
            font-weight: bold;
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
            color: #495057;
        }
        
        .tooltip-body {
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        
        /* 确保工具提示不会被截断 */
        .case-info-panel {
            overflow: visible !important;
        }
        
        /* 用例ID样式 */
        .case-id {
            cursor: help;
            border-bottom: 1px dashed #6c757d;
            display: inline-block;
        }
        
        /* 信息图标样式 */
        .info-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background-color: #17a2b8;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 5px;
            cursor: help;
        }
        
        /* 提示文本样式 */
        .hint-text {
            font-size: 11px;
            color: #6c757d;
            margin-left: 5px;
            font-style: italic;
        }
        
        /* 调整信息项间距 */
        .info-item {
            margin-bottom: 12px;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- 左侧：用例基本信息 -->
        <div class="case-info-panel">
            <h2 class="panel-title">用例信息</h2>
            <div class="info-item">
                <label>用例ID:</label>
                {% if case.description %}
                <div class="tooltip-container">
                    <span class="case-id">{{ case.id }}</span>
                    <span class="info-icon" title="鼠标悬停查看详细描述">i</span>
                    <div class="tooltip-content">
                        <div class="tooltip-title">用例描述</div>
                        <div class="tooltip-body">{{ case.description }}</div>
                    </div>
                </div>
                <span class="hint-text">(悬停查看详情)</span>
                {% else %}
                <span>{{ case.id }}</span>
                {% endif %}
            </div>
            <div class="info-item">
                <label>执行状态:</label>
                <span class="status-{{ case.status }}">{{ case.status }}</span>
            </div>
            <div class="info-item">
                <label>执行时长:</label>
                <span>{{ case.duration }}秒</span>
            </div>
            <div class="info-item">
                <label>开始时间:</label>
                <span>{{ case.start_time }}</span>
            </div>
            <div class="info-item">
                <label>结束时间:</label>
                <span>{{ case.end_time }}</span>
            </div>
        </div>
        
        <!-- 中间：执行日志 -->
        <div class="log-panel">
            <h2 class="panel-title">执行日志</h2>
            
            <!-- 日志表格 -->
            <table class="log-table">
                <thead>
                    <tr>
                        <th class="col-time">时间</th>
                        <th class="col-thread">线程ID</th>
                        <th class="col-source">代码源</th>
                        <th class="col-level">级别</th>
                        <th class="col-message">消息</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in case.logs %}
                    <tr class="log-entry {{ log.level|lower }}"
                        data-level="{{ log.level }}"
                        data-thread-id="{{ log.thread_id|default('') }}">
                        <td class="col-time">{{ log.timestamp|default('') }}</td>
                        <td class="col-thread">{{ log.thread_id|default('') }}</td>
                        <td class="col-source"><pre class="source-content">{{ log.source|default('') }}</pre></td>
                        <td class="col-level">{{ log.level }}</td>
                        <td class="col-message">
                            {% if log.level == 'STEP' %}
                            <pre class="step-content">{{ log.message }}</pre>
                            {% else %}
                            <pre class="message-content">{{ log.message }}</pre>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 右侧：筛选器 -->
        <div class="filter-panel">
            <h2 class="panel-title">筛选器</h2>
            
            <div class="filter-section">
                <label for="keywordSearch">输入关键字:</label>
                <input type="text" id="keywordSearch" placeholder="搜索...">
            </div>
            
            <div class="filter-section">
                <div class="filter-label">Filter Options:</div>
                <div id="filterOptions" class="filter-options"></div>
            </div>
            
            <div class="filter-section">
                <label for="threadId">ThreadID:</label>
                <input type="text" id="threadId">
                <button id="submitThreadFilter">Submit</button>
            </div>
        </div>
    </div>

    <!-- 在页面底部添加分页导航 -->
    <div class="pagination-container">
        <div class="pagination">
            {% if prev_page %}
            <a href="{{ prev_page }}" class="page-btn prev-page">上一页</a>
            {% else %}
            <span class="page-btn disabled prev-page">上一页</span>
            {% endif %}
            
            <span class="page-info">第 {{ current_page }} 页，共 {{ total_pages }} 页</span>
            
            {% if next_page %}
            <a href="{{ next_page }}" class="page-btn next-page">下一页</a>
            {% else %}
            <span class="page-btn disabled next-page">下一页</span>
            {% endif %}
        </div>
    </div>

    <script src="../assets/js/filter.js"></script>
    <script>
        // 内联脚本，检查资源是否加载
        document.addEventListener('DOMContentLoaded', function() {
            const styleLoaded = Array.from(document.styleSheets).some(sheet => 
                sheet.href && sheet.href.includes('style.css'));
                
            // 处理工具提示位置，避免超出视口
            const tooltips = document.querySelectorAll('.tooltip-content');
            tooltips.forEach(tooltip => {
                const container = tooltip.parentElement;
                
                // 检查工具提示是否会超出右侧边界
                container.addEventListener('mouseenter', () => {
                    const rect = tooltip.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    
                    if (rect.right > viewportWidth) {
                        tooltip.style.left = 'auto';
                        tooltip.style.right = '0';
                    }
                    
                    // 如果内容过长，确保滚动条可用
                    const tooltipBody = tooltip.querySelector('.tooltip-body');
                    if (tooltipBody && tooltipBody.scrollHeight > 300) {
                        tooltipBody.style.height = '300px';
                    }
                });
            });
            
            // 保留提取CaseName的代码，但不再使用
            function extractCaseName() {
                try {
                    const description = `{{ case.description|safe }}`;
                    if (!description) return;
                    
                    // 尝试匹配CaseName:后面的内容
                    const caseNameMatch = description.match(/CaseName:\s*([^\n]+)/);
                    if (caseNameMatch && caseNameMatch[1]) {
                        const caseName = caseNameMatch[1].trim();
                        // 不再更新DOM，但保留代码以备将来可能需要
                        // document.getElementById('caseName').textContent = caseName;
                    }
                } catch (e) {
                    console.error('Error extracting CaseName:', e);
                }
            }
            
            // 执行提取CaseName，但不使用结果
            // extractCaseName();
        });
    </script>
</body>
</html> 