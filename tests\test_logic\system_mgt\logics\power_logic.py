"""
Description: POWER LOGIC

Copyright: ©2025-2030 四川华鲲振宇智能科技有限责任公司

Date: 2025/2/28 17:15

"""
import time
import copy

from hkautos import log
from hkautos.config.enum import HostType
from hkautos.exception.hktest_exception import HKTestException

from tests.test_logic.alias import BMCMgt
from tests.test_logic.logic import Logic
from tests.test_logic.system_mgt.components.power_mgt import PowerMgt
from tests.test_logic.system_mgt.logics.logs_logic import LogLogic
from tests.test_data import common_fun as CommonFun


class PowerLogic(Logic):
    alias = BMCMgt.PowerControl

    def __init__(self, owning_device):
        super(PowerLogic, self).__init__(owning_device)
        self.logger = log.get_logger(__name__)
        self.bmc_host = self.owning_device.get_host(HostType.BMC)
        self.powerComponent = PowerMgt(owning_device)
        self.logobj = LogLogic(owning_device)
        self.bmc_host = self.owning_device.get_host(HostType.BMC, host_id="1")

    def set_power_policy_and_check_log(self, mode="ipmi", host="local", params=None, **kwargs):
        """
        设置电源策略,并检查对应日志
        Args:
            None
        Returns:
            True or False
        """
        try:
            start_time = time.time()
            self.powerComponent.set_power_policy_interface(mode=mode, host=host, params=params,
                                                           type=kwargs.get("type", "raw"))
        except Exception as e:
            raise HKTestException(f"设置电源策略出错！, error: {e}") from e
        kwargs["user_name"] = params.get("UserName")
        kwargs["start_time"] = start_time
        check_ret = self.logobj.check_log_validation(mode, host, kwargs)
        if check_ret:
            return check_ret
        raise HKTestException("设置电源策略后检查日志失败")

    def set_acpi_power_state_and_check_log(self, mode="ipmi", host="local", params=None, **kwargs):
        """
        设置电源ACPI状态,并检查对应日志
        Args:
            None
        Returns:
            True or False
        """
        try:
            self.powerComponent.set_acpi_power_state_interface(cmd_mode=mode, host=host, params=params)
        except Exception as e:
            raise HKTestException(f"设置电源ACPI状态出错！, error: {e}") from e
        kwargs["user_name"] = params.get("UserName")
        check_ret = self.logobj.check_log_validation(mode, host, kwargs)
        if check_ret:
            return check_ret
        raise HKTestException("设置电源ACPI状态后检查日志失败！")

    def set_chassis_power_state_and_check_log(self, mode="ipmi", host="local", params=None, **kwargs):
        """
        设置机箱电源状态状态,并检查对应日志
        Args:
            None
        Returns:
            True or False
        """
        try:
            self.powerComponent.set_power_state(mode=mode, host=host, params=params, type=kwargs.get("type", "raw"))
        except Exception as e:
            raise HKTestException(f"设置机箱电源状态出错！, error: {e}") from e
        kwargs["user_name"] = params.get("UserName")
        check_ret = self.logobj.check_log_validation(mode, host, kwargs)
        if check_ret:
            return check_ret
        raise HKTestException("设置机箱电源状态后检查日志失败！")

    def check_power_info(self, hardware_info, mode="web"):
        """通过web/redfish读取CPU信息，检查信息是否正确
        Args:
            hardware_info：硬件配置表
            mode:   接口模式，可选”web“、”redfish“，缺省值为“web”
        Returns:
            True:   信息检查正确
            False:  信息检查不正确
        """
        if mode == "web":
            psus_info = self.powerComponent.web_api.web_get_psu_info()
        elif mode == "redfish":
            psus_info = self.powerComponent.redfish_api.get_psu_info()
        else:
            self.logger.error(f"参数mode当前只支持“web”、“redfish”，暂不支持{mode}")
            return False

        self.logger.info(f"{mode}获取的信息: {psus_info}")
        psus_hardware_info = copy.deepcopy(hardware_info["Others"]['PSU'])

        if len(psus_hardware_info) != len(psus_info):
            self.logger.error(f"【PSU数量不正确】：\n{mode}获取的数量: {len(psus_info)}\n硬件配置文件数量: "
                              f"{len(psus_hardware_info)}")
            return False

        for psu_info in psus_info:
            # 配置文件中的值是按照WEB的返回格式编写的，redfish获取的值先做一下格式转化
            if mode == "redfish":
                psu_info.pop('Redundancy')
                psu_info = CommonFun.flatten_dict(psu_info)
                psu_info = CommonFun.convert_values_to_str(psu_info)
                psu_info.update({'CapacityWatts': str(psu_info['PowerCapacityWatts']) + " W"})
                if psu_info['State'] != 'Enabled' or psu_info['Health'] != 'OK':
                    self.logger.error(f"PSU状态异常，State：{psu_info['State']}， Health：{psu_info['Health']}")
                    return False

            for psu_hw_info in psus_hardware_info:
                if set(psu_hw_info.values()) & set(psu_info.values()) == set(psu_hw_info.values()):
                    self.logger.info(f"【信息一致】：\nWeb获取的值:{psu_info}\n硬件配置文件信息:{psu_hw_info}相匹配")
                    # 删除配置表中已经匹配到元素，防止反复对同一个元素进行匹配
                    psus_hardware_info.remove(psu_hw_info)
                    break

                # hardware_info配置文件中最后一个元素都没有匹配到，匹配失败
                if psu_hw_info == psus_hardware_info[-1]:
                    self.logger.error(f"【信息不一致】：\nWeb获取的信息: {psu_info}\n硬件配置文件信息: {psus_hardware_info}")
                    return False
        return True
