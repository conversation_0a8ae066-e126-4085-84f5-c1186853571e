
"""
功   能：web查询SW板信息测试

修改信息：
    日期：   2025/07/22
    修改内容：创建
    修改人：  赵一江/HK1525

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from engine.core.case import Case
from hkautos.config.enum import DeviceType, HostType
from utilitylibraries.test_data.test_data import TestData
from tests.test_logic.alias import HardwareMgt

class FRAME_SWBD_INFO_FUNC_1010(Case):
    """
    CaseId:
        FRAME_SWBD_INFO_FUNC_1010
    RunLevel:
        1(#1)
    CaseName:
        web查询SW板信息测试
    PreCondition:
        1、环境正常
    TestStep:
        1._登录BMC_Web，进入系统管理->系统信息->其他，查询SW板相关信息，有预期1
    ExpectedResult:
        1._SW板信息包含单板名称、单板ID、组件UID、CPLD版本_编号、描述、位置、厂商、PCB版本、部件编码、序列号等，各项信息均正确。
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start.... ")

    def pre_test_case(self):
        self.logger.info("Pre Test Case Start.... ")
        self.device_dut = self.resource.get_device(device_type=DeviceType.Server, utility="DUT")
        self.bmc_obj = self.device_dut.get_host(host_type=HostType.BMC)
        self.web_api = self.device_dut.get_api(ns_name="Web")

        self.boards_mgt = self.device_dut.find(HardwareMgt.Boards)
        self.data_obj = TestData(self.device_dut)
        self.hardware_json = self.data_obj.get_hardware_obj("hardware")

    def procedure(self):
        self.web_login_params = {
            "username": self.bmc_obj.username,
            "password": self.bmc_obj.password,
            "login_method": "",
            "web_protocol": "https",
            "web_port": 443,
            "ip_address": self.bmc_obj.local_ip,
            "update_pwd": False,
        }
        self.logger.step("1. 登录BMC Web，进入系统管理->系统信息->其他，查询SW板相关信息，有预期1")
        self.web_handle = self.web_api.login(self.web_login_params)
        res = self.boards_mgt.web_check_board_info(self.hardware_json, "Switch_Boards")
        self.assertTrue(res, "SW板信息包含单板名称、单板ID、组件UID、CPLD版本_编号、描述、位置、厂商、PCB版本、部件编码、序列号等，各项信息均正确")

    def post_test_case(self):
        self.logger.info("post test case.... ")
        self.web_api.logout(self.web_handle)
