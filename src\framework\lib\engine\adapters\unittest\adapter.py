import unittest
from pathlib import Path
from typing import Any, Dict

from ..base import TestFrameworkAdapter
from .parser import UnittestResultParser


class UnittestAdapter(TestFrameworkAdapter):
    """Unittest 适配器"""

    def __init__(self):
        self.parser = UnittestResultParser()

    def run_test(self, test_path: str, **kwargs) -> Dict[str, Any]:
        """执行 unittest 测试"""
        loader = unittest.TestLoader()
        suite = loader.discover(str(Path(test_path).parent), pattern=Path(test_path).name)

        runner = unittest.TextTestRunner(**self.get_framework_args(**kwargs))
        result = runner.run(suite)

        return {"path": test_path, "raw_result": result}

    def parse_results(self, raw_results: unittest.TestResult) -> Dict[str, Any]:
        """解析 unittest 结果"""
        return self.parser.parse_result(raw_results)

    def get_framework_args(self, **kwargs) -> Dict[str, Any]:
        """转换为 unittest 特定的参数"""
        args = {}
        if kwargs.get("verbose"):
            args["verbosity"] = kwargs["verbose"]
        return args
