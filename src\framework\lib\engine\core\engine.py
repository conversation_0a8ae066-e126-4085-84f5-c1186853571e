import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from ..adapters.hktest.adapter import HKTestAdapter
from ..hooks.cleanup_hooks import CleanupHooks
from ..hooks.default_hooks import DefaultHooks
from ..hooks.hook_manager import HookManager
from ..hooks.registry import HookRegistry
from ..reporter import ReportManager
from ..utils.exceptions import EngineError
from ..utils.logger import setup_logger
from .hktest_config import HKTestConfig
from .status import Status, StatusReporter


class Engine:
    """测试执行引擎"""

    def __init__(self, config):
        self.config = config
        # 初始化报告管理器
        report_config = {
            # 只启用HTML报告
            "enabled": ["html"],
            # 报告目录配置
            "report_dir": "reports",
            # 通用配置
            "common": {
                "title": config.name or "Test Report",
                "description": config.description or ""
            }
        }
        self.report_manager = ReportManager(report_config)

        # 只在有task_id时才初始化状态上报器
        self.status_reporter = None
        if config.status_report and config.status_report.get('task_id'):
            self.status_reporter = StatusReporter()

        # 设置日志
        self.logger = setup_logger(
            level=config.log_level,
            log_file=config.log_file,
            logger_name="engine"
        )

        # 初始化报告目录
        self.report_manager.initialize()

        # 初始化其他属性
        self.adapter = None
        self.hooks = None
        self.hook_manager = None
        self.default_hooks = None
        self.cleanup_hooks = None

    @classmethod
    def create(
            cls,
            config: Optional[HKTestConfig] = None,
            adapter: Optional[HKTestAdapter] = None
    ) -> "Engine":
        """创建引擎实例"""
        # 确保配置存在
        config = config or HKTestConfig(test_bed="", test_set="")

        # 创建引擎实例
        engine = cls(config=config)

        # 设置适配器
        engine.adapter = adapter or HKTestAdapter()
        engine.hooks = HookRegistry()

        # 初始化HKTest
        try:
            if engine.config.test_bed:
                engine.adapter.initialize(
                    engine.config.test_bed,
                    test_cases_dir=engine.config.test_cases_dir
                )
        except Exception as e:
            # 上报环境初始化错误状态
            env_info = {
                "id": "env_check"
            }
            engine._report_case_status(
                env_info,
                Status.ERROR,
                duration=0,
                error_msg=str(e),
                log_info=f"环境初始化出错: {str(e)}"
            )
            raise EngineError(f"报错是{e}")

        # 初始化hooks
        engine.default_hooks = DefaultHooks(engine.logger)
        engine.cleanup_hooks = CleanupHooks(engine.logger)

        # 创建hook管理器并注册所有钩子
        engine.hook_manager = HookManager(
            engine.hooks,
            [engine.default_hooks, engine.cleanup_hooks]
        )

        # 初始化状态上报器
        if engine.config.status_report:
            loop = asyncio.new_event_loop()
            try:
                loop.run_until_complete(
                    engine.status_reporter.initialize(engine.config.status_report)
                )
            finally:
                loop.close()

        return engine

    def run_suite(self) -> Dict[str, Any]:
        """运行测试套件"""
        start_time = time.time()

        try:
            # 构建报告链接
            summary_report, build_case_link = self._build_report_links()

            # 开始测试套件
            suite_info = {
                'name': self.config.name,
                'description': self.config.description,
                'test_cases': self.config.test_cases
            }
            self.report_manager.start_suite(suite_info)

            # 执行前置钩子
            self.hook_manager.handle_suite(is_start=True)

            # 运行所有测试用例
            passed_count = 0
            failed_count = 0

            for test_case in self.config.test_cases:
                case_start_time = time.time()
                case_info = None

                try:
                    # 开始测试用例
                    case_info = {
                        "id": test_case.get("name", "").replace(" ", "_"),
                        "name": test_case.get("name", "Unknown"),
                        "description": test_case.get("description", ""),
                        "suite": test_case.get("suite", "Default Suite"),
                        "file": str(test_case.get("file", "")),
                        "line": test_case.get("line", 0),
                        "tags": test_case.get("tags", [])
                    }
                    
                    # 在用例开始前获取文档信息
                    try:
                        doc = self.adapter.get_test_doc(test_case)
                        if doc and not case_info.get('description'):
                            # 更新用例描述，如果原来没有描述或者描述为空
                            case_info['description'] = doc
                            # 更新测试用例的描述
                            test_case['description'] = doc
                    except Exception as e:
                        self.logger.warning(f"获取用例文档失败: {e}")
                    
                    self.logger.debug(f"开始执行测试用例: {case_info['name']}")
                    self.report_manager.start_test(case_info)

                    # 上报用例开始状态
                    self._report_case_status(
                        case_info,
                        Status.RUNNING,
                        log_info=f"测试用例 {case_info['name']} 开始执行"
                    )

                    # 执行测试步骤
                    try:
                        result = self.adapter.run_test(test_case, report_manager=self.report_manager)
                    except KeyboardInterrupt:
                        result = {'status': 'stopped', 'reason': '用户中断执行'}
                    except TimeoutError:
                        result = {'status': 'stopped', 'reason': '执行超时'}
                    except Exception as e:
                        result = {'status': 'failed', 'error': str(e)}

                    # 计算用例执行时间
                    duration = round(time.time() - case_start_time, 3)
                    result['duration'] = duration

                    # 更新测试用例状态并添加到报告
                    test_case.update(result)
                    
                    # 将更新后的用例信息添加到结果中，以便在报告中显示
                    result['case_info'] = case_info
                    
                    self.report_manager.end_test(result)

                    # 根据执行结果映射状态
                    if result['status'] == 'passed':
                        passed_count += 1
                        status = Status.PASSED
                        log_info = "测试用例执行通过"
                    elif result['status'] == 'stopped':
                        failed_count += 1
                        status = Status.INTERRUPTED
                        reason = result.get('reason', '未知原因')
                        log_info = f"测试用例执行中断: {reason}"
                        # 根据中断原因细分日志信息
                        if '用户中断' in reason:
                            log_info = "测试用例被用户中断"
                        elif '超时' in reason:
                            log_info = "测试用例执行超时"
                    else:  # failed 或其他状态
                        failed_count += 1
                        status = Status.FAILED
                        log_info = f"测试用例执行失败: {result.get('reason', result.get('error', '未知错误'))}"

                    # 上报用例完成状态，包含文档信息
                    self._report_case_status(
                        case_info,
                        status,
                        duration=duration,
                        error_msg=result.get('error', result.get('reason', '')),
                        log_info=log_info
                    )

                except Exception as e:
                    # 处理用例执行过程中的异常
                    duration = round(time.time() - case_start_time, 3)
                    failed_count += 1

                    error_result = {
                        'status': Status.ERROR,  # 使用 ERROR 表示异常
                        'error': str(e),
                        'duration': duration
                    }

                    if case_info:
                        self.report_manager.end_test(error_result)

                        # 上报用例错误状态
                        self._report_case_status(
                            case_info,
                            Status.ERROR,
                            duration=duration,
                            error_msg=str(e),
                            log_info=f"测试用例执行出错: {str(e)}"
                        )
                    else:
                        self.logger.error(f"用例信息获取失败，无法上报状态: {str(e)}")

                # 在每个用例执行后记录日志
                self.logger.info(
                    f"用例 {case_info['name'] if case_info else '未知'} "
                    f"执行完成，状态: {status if 'status' in locals() else 'ERROR'}")

            # 执行后置钩子
            self.hook_manager.handle_suite(is_start=False)

            # 结束测试套件
            suite_result = {
                'total': len(self.config.test_cases),
                'passed': passed_count,
                'failed': failed_count,
            }
            self.report_manager.end_suite(suite_result)

            # 记录测试套件执行结果
            self.logger.info(
                f"测试套件执行完成。总计: {suite_result['total']}, 通过: "
                f"{suite_result['passed']}, 失败: {suite_result['failed']}")

            return suite_result

        except Exception as e:
            # 记录错误但不上报任务状态
            self.logger.error(f"测试执行出错: {str(e)}")
            raise EngineError(f"测试套件执行失败: {e}") from e

        finally:
            # 生成报告并清理资源
            try:
                self.report_manager.generate_reports()
                if self.status_reporter:
                    self.status_reporter.close_sync()
            except Exception as e:
                self.logger.error(f"资源清理失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            # 关闭状态上报器
            self.status_reporter.close_sync()
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            raise

    def _build_report_links(self):
        """构建报告链接，返回总报告链接和构建用例报告链接的函数"""
        report_dir = self.report_manager.report_dir / "html"
        jenkins_url = os.getenv('JOB_URL', '')
        build_number = os.getenv('BUILD_NUMBER', '')

        # 构建总报告链接
        if jenkins_url and build_number:
            try:
                base_url = f"{jenkins_url}{build_number}/artifact"
                report_path = (Path(report_dir) / "index.html").as_posix()
                summary_report = f"{base_url}/{report_path}"
            except Exception as e:
                self.logger.warning(f"构建Jenkins报告URL失败: {e}, 使用相对路径")
                summary_report = (Path(report_dir) / "index.html").as_posix()
        else:
            summary_report = (Path(report_dir) / "index.html").as_posix()

        # 返回构建用例报告链接的函数
        def build_case_link(case_id):
            if jenkins_url and build_number:
                try:
                    case_path = (Path(report_dir) / case_id / f"{case_id}--0.html").as_posix()
                    return f"{base_url}/{case_path}"
                except Exception as e:
                    self.logger.warning(f"构建Jenkins用例报告URL失败: {e}, 使用相对路径")

            return (Path(report_dir) / case_id / f"{case_id}--0.html").as_posix()

        return summary_report, build_case_link

    def _report_case_status(self, case_info: dict, status: Status, duration: float = 0,
                            error_msg: str = '', log_info: str = ''):
        """上报用例状态"""
        if not self.status_reporter:
            return

        try:
            summary_report, build_case_link = self._build_report_links()
            case_id = case_info["id"]
            report_link = build_case_link(case_id)

            # 构建状态数据
            status_data = {
                'caseId': case_id,
                'status': status,
                'updateTime': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'duration': duration,
                'errorMsg': error_msg,
                'logInfo': log_info,
                'reportLinks': report_link,
                'totalCases': len(self.config.test_cases),
                'taskInfo': {
                    'reportLinks': summary_report
                }
            }

            # 发送状态数据
            self.status_reporter._send_single_sync(status_data)

        except Exception as e:
            self.logger.error(f"上报用例状态失败: {e}")
